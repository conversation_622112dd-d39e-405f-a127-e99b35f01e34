import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { requireAuth } from '@/lib/auth'
import { handleApiError, NotFoundError, AuthorizationError } from '@/lib/errors'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authentication
    const currentUser = await requireAuth()

    // Await params
    const { id } = await params

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const format = searchParams.get('format') || 'pdf'
    const companyName = searchParams.get('companyName') || 'CTB Security'
    const reportTitle = searchParams.get('reportTitle')

    // Get scan with vulnerabilities
    const scan = await db.scan.findUnique({
      where: { id },
      include: {
        asset: {
          select: {
            id: true,
            url: true,
            domain: true,
            title: true,
            description: true,
            status: true
          }
        },
        vulnerabilities: {
          orderBy: [
            { severity: 'desc' },
            { timestamp: 'desc' }
          ]
        }
      }
    })

    if (!scan) {
      throw new NotFoundError('Scan not found')
    }

    // Check ownership
    if (scan.userId !== currentUser.userId) {
      throw new AuthorizationError('Access denied')
    }

    // Return report data for client-side PDF generation
    const reportData = {
      scan,
      asset: scan.asset,
      vulnerabilities: scan.vulnerabilities,
      reportType: 'scan' as const,
      generatedAt: new Date().toISOString(),
      companyName,
      reportTitle
    }

    return NextResponse.json(reportData)

  } catch (error) {
    return handleApiError(error)
  }
}



# CTB Scanner - Windows PowerShell Development Setup Script
# This script sets up the development environment for CTB Scanner on Windows

param(
    [switch]$SkipDependencies,
    [switch]$SkipDatabase,
    [switch]$SkipNuclei,
    [switch]$Force
)

# Configuration
$AppDir = Split-Path -Parent $PSScriptRoot
$RequiredNodeVersion = "18.0.0"
$RequiredNpmVersion = "8.0.0"

# Logging functions
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $color = switch ($Level) {
        "ERROR" { "Red" }
        "WARNING" { "Yellow" }
        "SUCCESS" { "Green" }
        "INFO" { "Cyan" }
        default { "White" }
    }
    
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
}

function Write-Success { param([string]$Message) Write-Log $Message "SUCCESS" }
function Write-Error { param([string]$Message) Write-Log $Message "ERROR" }
function Write-Warning { param([string]$Message) Write-Log $Message "WARNING" }
function Write-Info { param([string]$Message) Write-Log $Message "INFO" }

# Check if running as administrator
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# Check Node.js version
function Test-NodeVersion {
    try {
        $nodeVersion = & node --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            $version = $nodeVersion -replace 'v', ''
            $versionParts = $version.Split('.')
            $requiredParts = $RequiredNodeVersion.Split('.')
            
            if ([int]$versionParts[0] -ge [int]$requiredParts[0]) {
                Write-Success "Node.js version $nodeVersion is compatible"
                return $true
            }
            else {
                Write-Warning "Node.js version $nodeVersion is too old. Required: v$RequiredNodeVersion+"
                return $false
            }
        }
        else {
            Write-Warning "Node.js not found"
            return $false
        }
    }
    catch {
        Write-Warning "Failed to check Node.js version"
        return $false
    }
}

# Install Node.js using winget
function Install-NodeJS {
    Write-Info "Installing Node.js..."
    
    try {
        if (Get-Command winget -ErrorAction SilentlyContinue) {
            & winget install OpenJS.NodeJS
            if ($LASTEXITCODE -eq 0) {
                Write-Success "Node.js installed successfully"
                # Refresh PATH
                $env:PATH = [System.Environment]::GetEnvironmentVariable("PATH", "Machine") + ";" + [System.Environment]::GetEnvironmentVariable("PATH", "User")
                return $true
            }
        }
        else {
            Write-Warning "winget not available. Please install Node.js manually from https://nodejs.org/"
            return $false
        }
    }
    catch {
        Write-Error "Failed to install Node.js: $($_.Exception.Message)"
        return $false
    }
}

# Check and install dependencies
function Install-Dependencies {
    Write-Info "Installing project dependencies..."
    
    try {
        Set-Location $AppDir
        
        # Install npm dependencies
        Write-Info "Running npm install..."
        & npm install
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Dependencies installed successfully"
            return $true
        }
        else {
            Write-Error "Failed to install dependencies"
            return $false
        }
    }
    catch {
        Write-Error "Failed to install dependencies: $($_.Exception.Message)"
        return $false
    }
}

# Setup database
function Initialize-Database {
    Write-Info "Setting up database..."
    
    try {
        Set-Location $AppDir
        
        # Check if .env exists
        if (!(Test-Path ".env")) {
            if (Test-Path ".env.example") {
                Copy-Item ".env.example" ".env"
                Write-Info "Created .env file from .env.example"
                Write-Warning "Please update the database connection string in .env file"
            }
            else {
                Write-Warning ".env file not found. Please create one with database configuration"
                return $false
            }
        }
        
        # Run Prisma migrations
        Write-Info "Running database migrations..."
        & npx prisma migrate dev --name init
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Database migrations completed"
            
            # Generate Prisma client
            Write-Info "Generating Prisma client..."
            & npx prisma generate
            
            if ($LASTEXITCODE -eq 0) {
                Write-Success "Prisma client generated"
                return $true
            }
            else {
                Write-Error "Failed to generate Prisma client"
                return $false
            }
        }
        else {
            Write-Warning "Database migrations failed. Please check your database connection"
            return $false
        }
    }
    catch {
        Write-Error "Database setup failed: $($_.Exception.Message)"
        return $false
    }
}

# Install Nuclei
function Install-Nuclei {
    Write-Info "Installing Nuclei scanner..."
    
    try {
        # Check if Nuclei is already installed
        $nucleiVersion = & nuclei -version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Nuclei is already installed: $nucleiVersion"
            return $true
        }
        
        # Try to install via Go
        if (Get-Command go -ErrorAction SilentlyContinue) {
            Write-Info "Installing Nuclei via Go..."
            & go install -v github.com/projectdiscovery/nuclei/v3/cmd/nuclei@latest
            
            if ($LASTEXITCODE -eq 0) {
                Write-Success "Nuclei installed successfully"
                
                # Update templates
                Write-Info "Updating Nuclei templates..."
                & nuclei -update-templates -silent
                
                return $true
            }
        }
        
        # Try to download binary
        Write-Info "Downloading Nuclei binary..."
        $nucleiUrl = "https://github.com/projectdiscovery/nuclei/releases/latest/download/nuclei_windows_amd64.zip"
        $tempFile = "$env:TEMP\nuclei.zip"
        $nucleiDir = "$env:USERPROFILE\nuclei"
        
        Invoke-WebRequest -Uri $nucleiUrl -OutFile $tempFile
        
        if (!(Test-Path $nucleiDir)) {
            New-Item -ItemType Directory -Path $nucleiDir -Force | Out-Null
        }
        
        Expand-Archive -Path $tempFile -DestinationPath $nucleiDir -Force
        Remove-Item $tempFile -Force
        
        # Add to PATH
        $userPath = [Environment]::GetEnvironmentVariable("PATH", "User")
        if ($userPath -notlike "*$nucleiDir*") {
            [Environment]::SetEnvironmentVariable("PATH", "$userPath;$nucleiDir", "User")
            $env:PATH += ";$nucleiDir"
        }
        
        Write-Success "Nuclei installed to $nucleiDir"
        
        # Update templates
        Write-Info "Updating Nuclei templates..."
        & "$nucleiDir\nuclei.exe" -update-templates -silent
        
        return $true
    }
    catch {
        Write-Error "Failed to install Nuclei: $($_.Exception.Message)"
        Write-Info "Please install Nuclei manually from https://github.com/projectdiscovery/nuclei"
        return $false
    }
}

# Create development directories
function New-DevDirectories {
    Write-Info "Creating development directories..."
    
    $directories = @(
        "logs",
        "backups",
        "temp",
        "uploads"
    )
    
    foreach ($dir in $directories) {
        $fullPath = Join-Path $AppDir $dir
        if (!(Test-Path $fullPath)) {
            New-Item -ItemType Directory -Path $fullPath -Force | Out-Null
            Write-Info "Created directory: $dir"
        }
    }
    
    Write-Success "Development directories created"
}

# Setup Git hooks
function Install-GitHooks {
    Write-Info "Setting up Git hooks..."
    
    try {
        Set-Location $AppDir
        
        if (Test-Path ".git") {
            # Install husky if package.json has it
            if (Test-Path "package.json") {
                $packageJson = Get-Content "package.json" | ConvertFrom-Json
                if ($packageJson.devDependencies -and $packageJson.devDependencies.husky) {
                    & npx husky install
                    Write-Success "Git hooks installed"
                }
            }
        }
        else {
            Write-Warning "Not a Git repository"
        }
    }
    catch {
        Write-Warning "Failed to setup Git hooks: $($_.Exception.Message)"
    }
}

# Main setup function
function Start-DevSetup {
    Write-Info "Starting CTB Scanner development setup..."
    
    $success = $true
    
    # Check if running as admin for some operations
    if (!(Test-Administrator)) {
        Write-Warning "Not running as administrator. Some operations may fail."
    }
    
    # Check and install Node.js
    if (!$SkipDependencies) {
        if (!(Test-NodeVersion)) {
            if ($Force -or (Read-Host "Install Node.js? (y/N)") -eq 'y') {
                if (!(Install-NodeJS)) { $success = $false }
            }
        }
        
        # Install project dependencies
        if (!(Install-Dependencies)) { $success = $false }
    }
    
    # Setup database
    if (!$SkipDatabase) {
        if (!(Initialize-Database)) { $success = $false }
    }
    
    # Install Nuclei
    if (!$SkipNuclei) {
        if (!(Install-Nuclei)) { $success = $false }
    }
    
    # Create directories
    New-DevDirectories
    
    # Setup Git hooks
    Install-GitHooks
    
    if ($success) {
        Write-Success "Development setup completed successfully!"
        Write-Info "You can now run 'npm run dev' to start the development server"
    }
    else {
        Write-Warning "Development setup completed with some issues. Check the logs above."
    }
    
    return $success
}

# Help function
function Show-Help {
    Write-Host @"
CTB Scanner Development Setup Script (PowerShell)

Usage: .\dev-setup.ps1 [options]

Options:
  -SkipDependencies    Skip Node.js and npm dependency installation
  -SkipDatabase        Skip database setup and migrations
  -SkipNuclei          Skip Nuclei scanner installation
  -Force               Force installation without prompts

Examples:
  .\dev-setup.ps1                           # Full setup
  .\dev-setup.ps1 -SkipNuclei              # Setup without Nuclei
  .\dev-setup.ps1 -SkipDatabase -Force     # Skip database, no prompts
"@
}

# Main script execution
if ($args -contains "-help" -or $args -contains "--help" -or $args -contains "/?") {
    Show-Help
    exit 0
}

Start-DevSetup

'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { FileText, Download, Eye, Loader2 } from 'lucide-react'
import { PDFGenerator } from '@/lib/pdf-generator'
import { PDFReportTemplate } from './pdf-report-template'
// Using console for notifications since no toast system is currently implemented

interface PDFReportGeneratorProps {
  scanId?: string
  assetId?: string
  scanData?: any
  reportType?: 'scan' | 'asset'
  className?: string
}

export const PDFReportGenerator: React.FC<PDFReportGeneratorProps> = ({
  scanId,
  assetId,
  scanData,
  reportType = 'scan',
  className = ''
}) => {
  const [isGenerating, setIsGenerating] = useState(false)
  const [reportData, setReportData] = useState(scanData)
  const [showPreview, setShowPreview] = useState(false)

  const fetchReportData = async () => {
    try {
      const endpoint = reportType === 'asset'
        ? `/api/reports/pdf/assets/${assetId}`
        : `/api/reports/pdf/scans/${scanId}`

      const response = await fetch(endpoint)
      if (!response.ok) {
        throw new Error('Failed to fetch report data')
      }
      const data = await response.json()
      setReportData(data)
      return data
    } catch (error) {
      console.error('Error fetching report data:', error)
      alert('Failed to fetch report data')
      throw error
    }
  }

  const generatePDF = async (preview = false) => {
    setIsGenerating(true)
    try {
      let data = reportData
      if (!data) {
        data = await fetchReportData()
      }

      if (preview) {
        setShowPreview(true)
        setIsGenerating(false)
        return
      }

      // Wait for the component to render
      await new Promise(resolve => setTimeout(resolve, 100))

      const reportId = reportType === 'asset' ? data.asset.id : data.scan.id
      const filename = `${reportType}-report-${reportId}-${new Date().toISOString().split('T')[0]}.pdf`
      
      await PDFGenerator.downloadScanReport(data, {
        filename,
        format: 'a4',
        orientation: 'portrait'
      })

      alert('PDF report generated successfully!')
    } catch (error) {
      console.error('Error generating PDF:', error)
      alert('Failed to generate PDF report')
    } finally {
      setIsGenerating(false)
    }
  }

  const closePreview = () => {
    setShowPreview(false)
  }

  return (
    <>
      <div className={`flex gap-2 ${className}`}>
        <Button
          onClick={() => generatePDF(false)}
          disabled={isGenerating}
          variant="outline"
          size="sm"
          className="flex items-center gap-2"
        >
          {isGenerating ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <Download className="w-4 h-4" />
          )}
          {isGenerating ? 'Generating...' : 'Download PDF'}
        </Button>

        <Button
          onClick={() => generatePDF(true)}
          disabled={isGenerating}
          variant="outline"
          size="sm"
          className="flex items-center gap-2"
        >
          <Eye className="w-4 h-4" />
          Preview
        </Button>
      </div>

      {/* Preview Modal */}
      {showPreview && reportData && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-6xl max-h-[90vh] overflow-auto">
            <div className="sticky top-0 bg-white border-b p-4 flex justify-between items-center">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <FileText className="w-5 h-5" />
                PDF Report Preview
              </h3>
              <div className="flex gap-2">
                <Button
                  onClick={() => generatePDF(false)}
                  disabled={isGenerating}
                  size="sm"
                  className="flex items-center gap-2"
                >
                  {isGenerating ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <Download className="w-4 h-4" />
                  )}
                  Download
                </Button>
                <Button
                  onClick={closePreview}
                  variant="outline"
                  size="sm"
                >
                  Close
                </Button>
              </div>
            </div>
            <div className="p-4">
              <PDFReportTemplate
                scan={reportData.scan}
                asset={reportData.asset}
                vulnerabilities={reportData.vulnerabilities}
                reportType={reportData.reportType}
                generatedAt={reportData.generatedAt}
                companyName="CTB Security"
                reportTitle={`${reportType === 'scan' ? 'Vulnerability Scan' : 'Asset Security'} Report`}
              />
            </div>
          </div>
        </div>
      )}

      {/* Hidden PDF Template for Generation */}
      {reportData && (
        <div style={{ position: 'absolute', left: '-9999px', top: '-9999px' }}>
          <PDFReportTemplate
            scan={reportData.scan}
            asset={reportData.asset}
            vulnerabilities={reportData.vulnerabilities}
            reportType={reportData.reportType}
            generatedAt={reportData.generatedAt}
            companyName="CTB Security"
            reportTitle={`${reportType === 'scan' ? 'Vulnerability Scan' : 'Asset Security'} Report`}
          />
        </div>
      )}
    </>
  )
}

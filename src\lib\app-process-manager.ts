import { logger } from './logger'
import { errorTracker } from './error-tracking'
import { db } from './db'

/**
 * Application Process Manager for handling graceful shutdowns and restarts
 */
class AppProcessManager {
  private isShuttingDown = false
  private activeConnections = new Set<any>()
  private shutdownTimeout = 30000 // 30 seconds
  private healthCheckInterval: NodeJS.Timeout | null = null
  
  constructor() {
    this.setupSignalHandlers()
    this.setupUncaughtExceptionHandlers()
    this.startHealthCheck()
  }
  
  /**
   * Setup signal handlers for graceful shutdown
   */
  private setupSignalHandlers(): void {
    const signals = ['SIGTERM', 'SIGINT', 'SIGUSR2'] as const
    
    signals.forEach(signal => {
      process.on(signal, async () => {
        if (this.isShuttingDown) {
          logger.warn(`Received ${signal} during shutdown, forcing exit`, {
            component: 'APP_PROCESS_MANAGER'
          })
          process.exit(1)
        }
        
        logger.info(`Received ${signal}, starting graceful shutdown`, {
          component: 'APP_PROCESS_MANAGER',
          signal,
          activeConnections: this.activeConnections.size
        })
        
        await this.gracefulShutdown()
      })
    })
  }
  
  /**
   * Setup uncaught exception handlers
   */
  private setupUncaughtExceptionHandlers(): void {
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception - shutting down gracefully', error, {
        component: 'APP_PROCESS_MANAGER',
        type: 'UNCAUGHT_EXCEPTION'
      })
      
      errorTracker.trackError(error, {
        component: 'UNCAUGHT_EXCEPTION',
        fatal: true
      }, 'critical')
      
      // Give some time for error tracking to complete
      setTimeout(() => {
        this.gracefulShutdown().finally(() => {
          process.exit(1)
        })
      }, 1000)
    })
    
    process.on('unhandledRejection', (reason, promise) => {
      const error = reason instanceof Error ? reason : new Error(String(reason))
      
      logger.error('Unhandled Rejection - continuing but tracking error', error, {
        component: 'APP_PROCESS_MANAGER',
        type: 'UNHANDLED_REJECTION',
        promise: promise.toString()
      })
      
      errorTracker.trackError(error, {
        component: 'UNHANDLED_REJECTION',
        promise: promise.toString()
      }, 'high')
    })
    
    // Handle memory warnings
    process.on('warning', (warning) => {
      logger.warn('Process warning', {
        component: 'APP_PROCESS_MANAGER',
        name: warning.name,
        message: warning.message,
        stack: warning.stack
      })
      
      if (warning.name === 'MaxListenersExceededWarning') {
        errorTracker.trackError(new Error(warning.message), {
          component: 'MEMORY_WARNING',
          warningName: warning.name
        }, 'medium')
      }
    })
  }
  
  /**
   * Start health check monitoring
   */
  private startHealthCheck(): void {
    this.healthCheckInterval = setInterval(async () => {
      try {
        await this.performHealthCheck()
      } catch (error) {
        logger.error('Health check failed', error as Error, {
          component: 'APP_PROCESS_MANAGER'
        })
      }
    }, 60000) // Check every minute
  }
  
  /**
   * Perform health check
   */
  private async performHealthCheck(): Promise<void> {
    const memoryUsage = process.memoryUsage()
    const memoryUsageMB = {
      rss: Math.round(memoryUsage.rss / 1024 / 1024),
      heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
      heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
      external: Math.round(memoryUsage.external / 1024 / 1024)
    }
    
    // Log memory usage (debug level to avoid spam)
    logger.debug('Health check - memory usage', {
      component: 'APP_PROCESS_MANAGER',
      memory: memoryUsageMB,
      uptime: Math.round(process.uptime()),
      activeConnections: this.activeConnections.size
    })
    
    // Alert on high memory usage (>500MB RSS)
    if (memoryUsageMB.rss > 500) {
      logger.warn('High memory usage detected', {
        component: 'APP_PROCESS_MANAGER',
        memory: memoryUsageMB
      })
      
      errorTracker.trackError(new Error(`High memory usage: ${memoryUsageMB.rss}MB RSS`), {
        component: 'MEMORY_MONITOR',
        memory: memoryUsageMB
      }, 'medium')
    }
    
    // Test database connection
    try {
      await db.$queryRaw`SELECT 1`
    } catch (error) {
      logger.error('Database health check failed', error as Error, {
        component: 'APP_PROCESS_MANAGER'
      })
      
      errorTracker.trackError(error as Error, {
        component: 'DATABASE_HEALTH_CHECK'
      }, 'high')
    }
  }
  
  /**
   * Register an active connection
   */
  registerConnection(connection: any): void {
    this.activeConnections.add(connection)
  }
  
  /**
   * Unregister an active connection
   */
  unregisterConnection(connection: any): void {
    this.activeConnections.delete(connection)
  }
  
  /**
   * Perform graceful shutdown
   */
  async gracefulShutdown(): Promise<void> {
    if (this.isShuttingDown) {
      return
    }
    
    this.isShuttingDown = true
    
    logger.info('Starting graceful shutdown process', {
      component: 'APP_PROCESS_MANAGER',
      activeConnections: this.activeConnections.size
    })
    
    // Stop health check
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval)
    }
    
    // Set shutdown timeout
    const shutdownTimer = setTimeout(() => {
      logger.error('Graceful shutdown timeout, forcing exit', {
        component: 'APP_PROCESS_MANAGER',
        timeout: this.shutdownTimeout
      })
      process.exit(1)
    }, this.shutdownTimeout)
    
    try {
      // Close active connections
      logger.info('Closing active connections', {
        component: 'APP_PROCESS_MANAGER',
        count: this.activeConnections.size
      })
      
      const connectionPromises = Array.from(this.activeConnections).map(async (connection) => {
        try {
          if (connection && typeof connection.close === 'function') {
            await connection.close()
          } else if (connection && typeof connection.destroy === 'function') {
            connection.destroy()
          }
        } catch (error) {
          logger.warn('Error closing connection', {
            component: 'APP_PROCESS_MANAGER',
            error: error instanceof Error ? error.message : String(error)
          })
        }
      })
      
      await Promise.allSettled(connectionPromises)
      
      // Close database connections
      logger.info('Closing database connections', {
        component: 'APP_PROCESS_MANAGER'
      })
      
      try {
        await db.$disconnect()
      } catch (error) {
        logger.warn('Error disconnecting from database', {
          component: 'APP_PROCESS_MANAGER',
          error: error instanceof Error ? error.message : String(error)
        })
      }
      
      // Flush any remaining logs
      logger.info('Graceful shutdown completed successfully', {
        component: 'APP_PROCESS_MANAGER'
      })
      
      clearTimeout(shutdownTimer)
      process.exit(0)
      
    } catch (error) {
      logger.error('Error during graceful shutdown', error as Error, {
        component: 'APP_PROCESS_MANAGER'
      })
      
      clearTimeout(shutdownTimer)
      process.exit(1)
    }
  }
  
  /**
   * Get process status
   */
  getStatus(): {
    isShuttingDown: boolean
    activeConnections: number
    uptime: number
    memoryUsage: NodeJS.MemoryUsage
  } {
    return {
      isShuttingDown: this.isShuttingDown,
      activeConnections: this.activeConnections.size,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage()
    }
  }
  
  /**
   * Force restart (for development)
   */
  async forceRestart(): Promise<void> {
    logger.info('Force restart requested', {
      component: 'APP_PROCESS_MANAGER'
    })
    
    await this.gracefulShutdown()
  }
}

// Global instance - only initialize in production
export const appProcessManager = process.env.NODE_ENV === 'production' ? new AppProcessManager() : null

// Export for testing
export { AppProcessManager }

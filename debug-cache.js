const { MemoryCache } = require('./src/lib/cache.ts');

const lruCache = new MemoryCache({
  maxSize: 2,
  ttl: 10000,
  strategy: 'lru'
});

console.log('Setting key1 and key2...');
lruCache.set('key1', 'value1');
lruCache.set('key2', 'value2');

console.log('Cache after setting both keys:');
console.log('key1 exists:', lruCache.has('key1'));
console.log('key2 exists:', lruCache.has('key2'));

console.log('Accessing key1...');
const val1 = lruCache.get('key1');
console.log('Got value:', val1);

console.log('Setting key3 (should evict key2)...');
lruCache.set('key3', 'value3');

console.log('Cache after setting key3:');
console.log('key1 exists:', lruCache.has('key1'));
console.log('key2 exists:', lruCache.has('key2'));
console.log('key3 exists:', lruCache.has('key3'));

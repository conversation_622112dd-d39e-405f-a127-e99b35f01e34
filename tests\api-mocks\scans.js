const express = require('express')
const jwt = require('jsonwebtoken')
const { PrismaClient } = require('@prisma/client')
const { v4: uuidv4 } = require('uuid')

// Function to create router with prisma instance
const createScansRouter = (prisma) => {
  const scansRouter = express.Router()

// Validation helpers
const validateUrl = (url) => {
  try {
    const urlObj = new URL(url)
    
    // Check for private IP ranges
    const hostname = urlObj.hostname
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
      return { valid: false, error: 'Cannot scan localhost or private IP addresses' }
    }
    
    // Check for private IP ranges
    const privateRanges = [
      /^10\./,
      /^172\.(1[6-9]|2[0-9]|3[0-1])\./,
      /^192\.168\./
    ]
    
    if (privateRanges.some(range => range.test(hostname))) {
      return { valid: false, error: 'Cannot scan private IP addresses' }
    }
    
    return { valid: true }
  } catch (error) {
    return { valid: false, error: 'Invalid URL format' }
  }
}

const validateScanType = (scanType) => {
  const validTypes = ['quick', 'deep']
  return validTypes.includes(scanType)
}

const validateSeverity = (severity) => {
  const validSeverities = ['unknown', 'info', 'low', 'medium', 'high', 'critical']
  const severityList = severity.split(',').map(s => s.trim())
  return severityList.every(s => validSeverities.includes(s))
}

// Middleware to verify JWT token
const verifyToken = async (req, res, next) => {
  try {
    // Try to get token from cookies first, then manually parse from headers
    let token = req.cookies?.token

    if (!token && req.headers.cookie) {
      // Manually parse cookie header
      const cookies = req.headers.cookie.split(';').reduce((acc, cookie) => {
        const [key, value] = cookie.trim().split('=')
        acc[key] = value
        return acc
      }, {})
      token = cookies.token
    }

    if (!token) {
      return res.status(401).json({ error: 'Authentication required' })
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET)
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: { id: true, email: true, firstName: true, lastName: true, companyName: true, country: true }
    })

    if (!user) {
      return res.status(401).json({ error: 'Invalid token' })
    }

    req.user = user
    next()
  } catch (error) {
    return res.status(401).json({ error: 'Invalid token' })
  }
}

// POST /api/scans
scansRouter.post('/', verifyToken, async (req, res) => {
  try {
    const { url, scanType, severity } = req.body

    // Validate URL
    const urlValidation = validateUrl(url)
    if (!urlValidation.valid) {
      return res.status(400).json({ error: urlValidation.error })
    }

    // Validate scan type
    if (!validateScanType(scanType)) {
      return res.status(400).json({ error: 'Invalid scan type. Must be "quick" or "deep"' })
    }

    // Validate severity
    if (!validateSeverity(severity)) {
      return res.status(400).json({ error: 'Invalid severity levels' })
    }

    // Create or find asset
    let asset = await prisma.asset.findFirst({
      where: { 
        url: url,
        userId: req.user.id 
      }
    })

    if (!asset) {
      const urlObj = new URL(url)
      asset = await prisma.asset.create({
        data: {
          url: url,
          domain: urlObj.hostname,
          userId: req.user.id
        }
      })
    }

    // Create scan
    const scan = await prisma.scan.create({
      data: {
        id: uuidv4(),
        targetUrl: url,
        scanType: scanType.toUpperCase(),
        severity: severity,
        status: 'PENDING',
        userId: req.user.id,
        assetId: asset.id
      }
    })

    res.status(201).json({
      message: 'Scan initiated successfully',
      scanId: scan.id,
      status: scan.status
    })
  } catch (error) {
    console.error('Create scan error:', error)
    res.status(500).json({ error: 'Internal server error' })
  }
})

// GET /api/scans
scansRouter.get('/', verifyToken, async (req, res) => {
  try {
    const { page = 1, limit = 10, status } = req.query
    const pageNum = parseInt(page)
    const limitNum = parseInt(limit)
    const offset = (pageNum - 1) * limitNum

    const where = {
      userId: req.user.id
    }

    if (status) {
      where.status = status
    }

    const [scans, total] = await Promise.all([
      prisma.scan.findMany({
        where,
        include: {
          asset: true,
          vulnerabilities: true
        },
        orderBy: { createdAt: 'desc' },
        skip: offset,
        take: limitNum
      }),
      prisma.scan.count({ where })
    ])

    const scansWithCounts = scans.map(scan => ({
      ...scan,
      totalVulns: scan.vulnerabilities.length,
      criticalVulns: scan.vulnerabilities.filter(v => v.severity === 'critical').length,
      highVulns: scan.vulnerabilities.filter(v => v.severity === 'high').length,
      mediumVulns: scan.vulnerabilities.filter(v => v.severity === 'medium').length,
      lowVulns: scan.vulnerabilities.filter(v => v.severity === 'low').length,
      infoVulns: scan.vulnerabilities.filter(v => v.severity === 'info').length,
      unknownVulns: scan.vulnerabilities.filter(v => v.severity === 'unknown').length
    }))

    const pages = Math.ceil(total / limitNum)

    res.status(200).json({
      scans: scansWithCounts,
      pagination: {
        page: pageNum,
        pages,
        total,
        limit: limitNum
      }
    })
  } catch (error) {
    console.error('Get scans error:', error)
    res.status(500).json({ error: 'Internal server error' })
  }
})

// GET /api/scans/[id]
scansRouter.get('/:id', verifyToken, async (req, res) => {
  try {
    const { id } = req.params

    const scan = await prisma.scan.findFirst({
      where: {
        id
      },
      include: {
        asset: true,
        vulnerabilities: {
          orderBy: { createdAt: 'desc' }
        }
      }
    })

    if (!scan) {
      return res.status(404).json({ error: 'Scan not found' })
    }

    // Check if user owns this scan
    if (scan.userId !== req.user.id) {
      return res.status(403).json({ error: 'Access denied' })
    }

    const scanWithCounts = {
      ...scan,
      totalVulns: scan.vulnerabilities.length,
      criticalVulns: scan.vulnerabilities.filter(v => v.severity === 'critical').length,
      highVulns: scan.vulnerabilities.filter(v => v.severity === 'high').length,
      mediumVulns: scan.vulnerabilities.filter(v => v.severity === 'medium').length,
      lowVulns: scan.vulnerabilities.filter(v => v.severity === 'low').length,
      infoVulns: scan.vulnerabilities.filter(v => v.severity === 'info').length,
      unknownVulns: scan.vulnerabilities.filter(v => v.severity === 'unknown').length
    }

    res.status(200).json({
      scan: scanWithCounts
    })
  } catch (error) {
    console.error('Get scan error:', error)
    res.status(500).json({ error: 'Internal server error' })
  }
})

  return scansRouter
}

module.exports = createScansRouter

import { ChildProcess } from 'child_process'

interface RunningProcess {
  scanId: string
  process: ChildProcess
  startTime: number
  targetUrl: string
  lastHeartbeat: number
  retryCount: number
  maxRetries: number
}

interface ProcessStats {
  totalProcesses: number
  runningProcesses: number
  stuckProcesses: number
  oldestProcess: number
  averageRuntime: number
}

class ProcessManager {
  private runningProcesses = new Map<string, RunningProcess>()
  private cleanupInterval: NodeJS.Timeout | null = null
  private heartbeatInterval: NodeJS.Timeout | null = null
  private readonly STUCK_THRESHOLD_MS = 30 * 60 * 1000 // 30 minutes
  private readonly HEARTBEAT_INTERVAL_MS = 60 * 1000 // 1 minute
  private readonly CLEANUP_INTERVAL_MS = 5 * 60 * 1000 // 5 minutes

  constructor() {
    this.startPeriodicCleanup()
    this.startHeartbeatMonitoring()
  }

  /**
   * Register a running process for a scan
   */
  registerProcess(scanId: string, process: ChildProcess, targetUrl: string, maxRetries: number = 3): void {
    console.log(`📝 Registering process for scan ${scanId} (PID: ${process.pid})`)

    const now = Date.now()
    this.runningProcesses.set(scanId, {
      scanId,
      process,
      startTime: now,
      targetUrl,
      lastHeartbeat: now,
      retryCount: 0,
      maxRetries
    })

    // Auto-cleanup when process exits
    process.on('exit', (code, signal) => {
      console.log(`🔄 Process for scan ${scanId} exited with code ${code}, signal ${signal}`)
      this.unregisterProcess(scanId)
    })

    process.on('error', (error) => {
      console.error(`❌ Process error for scan ${scanId}:`, error.message)
      this.unregisterProcess(scanId)
    })

    // Update heartbeat when process outputs data
    process.stdout?.on('data', () => {
      this.updateHeartbeat(scanId)
    })

    process.stderr?.on('data', () => {
      this.updateHeartbeat(scanId)
    })
  }

  /**
   * Update heartbeat for a running process
   */
  private updateHeartbeat(scanId: string): void {
    const runningProcess = this.runningProcesses.get(scanId)
    if (runningProcess) {
      runningProcess.lastHeartbeat = Date.now()
    }
  }

  /**
   * Unregister a process (called automatically when process exits)
   */
  unregisterProcess(scanId: string): void {
    if (this.runningProcesses.has(scanId)) {
      console.log(`Unregistering process for scan ${scanId}`)
      this.runningProcesses.delete(scanId)
    }
  }

  /**
   * Kill a running process for a specific scan
   */
  killProcess(scanId: string): boolean {
    const runningProcess = this.runningProcesses.get(scanId)
    
    if (!runningProcess) {
      console.log(`No running process found for scan ${scanId}`)
      return false
    }

    try {
      console.log(`Killing process for scan ${scanId} (PID: ${runningProcess.process.pid})`)
      
      // Try graceful termination first
      runningProcess.process.kill('SIGTERM')
      
      // Force kill after 5 seconds if still running
      setTimeout(() => {
        if (!runningProcess.process.killed) {
          console.log(`Force killing process for scan ${scanId}`)
          runningProcess.process.kill('SIGKILL')
        }
      }, 5000)

      this.unregisterProcess(scanId)
      return true
    } catch (error) {
      console.error(`Failed to kill process for scan ${scanId}:`, error)
      return false
    }
  }

  /**
   * Check if a scan has a running process
   */
  hasRunningProcess(scanId: string): boolean {
    return this.runningProcesses.has(scanId)
  }

  /**
   * Get information about a running process
   */
  getProcessInfo(scanId: string): RunningProcess | null {
    return this.runningProcesses.get(scanId) || null
  }

  /**
   * Get all running processes
   */
  getAllRunningProcesses(): RunningProcess[] {
    return Array.from(this.runningProcesses.values())
  }

  /**
   * Kill all running processes (for cleanup)
   */
  killAllProcesses(): void {
    console.log(`🛑 Killing ${this.runningProcesses.size} running processes`)

    for (const [scanId, runningProcess] of this.runningProcesses) {
      try {
        console.log(`🔪 Terminating process for scan ${scanId} (PID: ${runningProcess.process.pid})`)
        runningProcess.process.kill('SIGTERM')

        // Force kill after 5 seconds
        setTimeout(() => {
          if (!runningProcess.process.killed) {
            console.log(`💀 Force killing process for scan ${scanId}`)
            runningProcess.process.kill('SIGKILL')
          }
        }, 5000)
      } catch (error) {
        console.error(`❌ Failed to kill process for scan ${scanId}:`, error)
      }
    }

    this.runningProcesses.clear()
    this.stopPeriodicCleanup()
  }

  /**
   * Start periodic cleanup of stuck processes
   */
  private startPeriodicCleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
    }

    this.cleanupInterval = setInterval(() => {
      this.cleanupStuckProcesses()
    }, this.CLEANUP_INTERVAL_MS)

    console.log(`🔄 Started periodic process cleanup (every ${this.CLEANUP_INTERVAL_MS / 1000}s)`)
  }

  /**
   * Start heartbeat monitoring
   */
  private startHeartbeatMonitoring(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
    }

    this.heartbeatInterval = setInterval(() => {
      this.checkHeartbeats()
    }, this.HEARTBEAT_INTERVAL_MS)

    console.log(`💓 Started heartbeat monitoring (every ${this.HEARTBEAT_INTERVAL_MS / 1000}s)`)
  }

  /**
   * Stop periodic cleanup
   */
  private stopPeriodicCleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = null
    }

    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }

    console.log('🛑 Stopped periodic cleanup and heartbeat monitoring')
  }

  /**
   * Clean up stuck processes
   */
  private async cleanupStuckProcesses(): Promise<void> {
    const now = Date.now()
    const stuckProcesses: string[] = []

    for (const [scanId, runningProcess] of this.runningProcesses) {
      const runtime = now - runningProcess.startTime
      const timeSinceHeartbeat = now - runningProcess.lastHeartbeat

      // Consider process stuck if it's been running too long or no heartbeat
      if (runtime > this.STUCK_THRESHOLD_MS || timeSinceHeartbeat > this.STUCK_THRESHOLD_MS) {
        stuckProcesses.push(scanId)
        console.warn(`⚠️ Detected stuck process for scan ${scanId} (runtime: ${Math.round(runtime / 1000)}s, last heartbeat: ${Math.round(timeSinceHeartbeat / 1000)}s ago)`)
      }
    }

    if (stuckProcesses.length > 0) {
      console.log(`🧹 Cleaning up ${stuckProcesses.length} stuck processes`)

      for (const scanId of stuckProcesses) {
        await this.handleStuckProcess(scanId)
      }
    }
  }

  /**
   * Handle a stuck process
   */
  private async handleStuckProcess(scanId: string): Promise<void> {
    const runningProcess = this.runningProcesses.get(scanId)
    if (!runningProcess) return

    try {
      // Update scan status to failed
      const { scanEventManager } = await import('./scan-events')
      await scanEventManager.emitStatus(
        scanId,
        'FAILED',
        'Scan process was stuck and has been terminated'
      )

      // Kill the process
      const killed = this.killProcess(scanId)
      if (killed) {
        console.log(`✅ Successfully cleaned up stuck process for scan ${scanId}`)
      } else {
        console.error(`❌ Failed to clean up stuck process for scan ${scanId}`)
      }

      // Update database scan status
      const { db } = await import('./db')
      await db.scan.update({
        where: { id: scanId },
        data: {
          status: 'FAILED',
          completedAt: new Date()
        }
      })

    } catch (error) {
      console.error(`❌ Error handling stuck process for scan ${scanId}:`, error)
    }
  }

  /**
   * Check heartbeats and detect unresponsive processes
   */
  private checkHeartbeats(): void {
    const now = Date.now()
    let unresponsiveCount = 0

    for (const [scanId, runningProcess] of this.runningProcesses) {
      const timeSinceHeartbeat = now - runningProcess.lastHeartbeat

      if (timeSinceHeartbeat > this.HEARTBEAT_INTERVAL_MS * 3) { // 3 missed heartbeats
        unresponsiveCount++
        console.warn(`💔 Process for scan ${scanId} appears unresponsive (${Math.round(timeSinceHeartbeat / 1000)}s since last heartbeat)`)
      }
    }

    if (unresponsiveCount > 0) {
      console.warn(`⚠️ ${unresponsiveCount} processes appear unresponsive`)
    }
  }

  /**
   * Get process statistics
   */
  getProcessStats(): ProcessStats {
    const now = Date.now()
    const processes = Array.from(this.runningProcesses.values())

    const runtimes = processes.map(p => now - p.startTime)
    const stuckCount = processes.filter(p => {
      const runtime = now - p.startTime
      const timeSinceHeartbeat = now - p.lastHeartbeat
      return runtime > this.STUCK_THRESHOLD_MS || timeSinceHeartbeat > this.STUCK_THRESHOLD_MS
    }).length

    return {
      totalProcesses: processes.length,
      runningProcesses: processes.length,
      stuckProcesses: stuckCount,
      oldestProcess: runtimes.length > 0 ? Math.max(...runtimes) : 0,
      averageRuntime: runtimes.length > 0 ? runtimes.reduce((a, b) => a + b, 0) / runtimes.length : 0
    }
  }

  /**
   * Get detailed process information
   */
  getDetailedProcessInfo(): Array<{
    scanId: string
    targetUrl: string
    runtime: number
    timeSinceHeartbeat: number
    isStuck: boolean
    retryCount: number
    maxRetries: number
  }> {
    const now = Date.now()

    return Array.from(this.runningProcesses.values()).map(process => {
      const runtime = now - process.startTime
      const timeSinceHeartbeat = now - process.lastHeartbeat
      const isStuck = runtime > this.STUCK_THRESHOLD_MS || timeSinceHeartbeat > this.STUCK_THRESHOLD_MS

      return {
        scanId: process.scanId,
        targetUrl: process.targetUrl,
        runtime,
        timeSinceHeartbeat,
        isStuck,
        retryCount: process.retryCount,
        maxRetries: process.maxRetries
      }
    })
  }

  /**
   * Get statistics about running processes
   */
  getStats(): {
    totalRunning: number
    processes: Array<{
      scanId: string
      targetUrl: string
      duration: number
      pid?: number
    }>
  } {
    const now = Date.now()
    const processes = Array.from(this.runningProcesses.values()).map(p => ({
      scanId: p.scanId,
      targetUrl: p.targetUrl,
      duration: Math.round((now - p.startTime) / 1000),
      pid: p.process.pid
    }))

    return {
      totalRunning: this.runningProcesses.size,
      processes
    }
  }
}

// Singleton instance
export const processManager = new ProcessManager()

// Graceful shutdown - kill all processes when the application exits
process.on('SIGTERM', () => {
  console.log('Shutting down process manager...')
  processManager.killAllProcesses()
})

process.on('SIGINT', () => {
  console.log('Shutting down process manager...')
  processManager.killAllProcesses()
})

process.on('exit', () => {
  processManager.killAllProcesses()
})

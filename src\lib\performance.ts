/**
 * Performance Monitoring and Optimization Utilities
 * 
 * This module provides comprehensive performance monitoring, metrics collection,
 * and optimization utilities for the CTB Scanner application.
 */

interface PerformanceMetric {
  name: string
  value: number
  unit: string
  timestamp: number
  tags?: Record<string, string>
}

interface TimingMetric {
  name: string
  startTime: number
  endTime?: number
  duration?: number
  tags?: Record<string, string>
}

interface ResourceMetrics {
  memory: {
    used: number
    total: number
    percentage: number
  }
  cpu: {
    usage: number
    loadAverage: number[]
  }
  uptime: number
}

/**
 * Performance metrics collector
 */
class PerformanceMonitor {
  private metrics: PerformanceMetric[] = []
  private timings: Map<string, TimingMetric> = new Map()
  private maxMetrics = 10000 // Keep last 10k metrics

  /**
   * Record a performance metric
   */
  recordMetric(name: string, value: number, unit: string = 'ms', tags?: Record<string, string>): void {
    const metric: PerformanceMetric = {
      name,
      value,
      unit,
      timestamp: Date.now(),
      tags
    }

    this.metrics.push(metric)

    // Keep only recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics)
    }

    // Log significant metrics
    if (this.isSignificantMetric(name, value, unit)) {
      console.log(`📊 Performance: ${name} = ${value}${unit}`, tags || {})
    }
  }

  /**
   * Start timing an operation
   */
  startTiming(name: string, tags?: Record<string, string>): void {
    this.timings.set(name, {
      name,
      startTime: performance.now(),
      tags
    })
  }

  /**
   * End timing an operation and record the metric
   */
  endTiming(name: string): number | null {
    const timing = this.timings.get(name)
    if (!timing) {
      console.warn(`⚠️ No timing found for: ${name}`)
      return null
    }

    const endTime = performance.now()
    const duration = endTime - timing.startTime

    timing.endTime = endTime
    timing.duration = duration

    // Record as metric
    this.recordMetric(name, duration, 'ms', timing.tags)

    // Clean up
    this.timings.delete(name)

    return duration
  }

  /**
   * Time a function execution
   */
  async timeFunction<T>(name: string, fn: () => Promise<T> | T, tags?: Record<string, string>): Promise<T> {
    this.startTiming(name, tags)
    try {
      const result = await fn()
      this.endTiming(name)
      return result
    } catch (error) {
      this.endTiming(name)
      this.recordMetric(`${name}_error`, 1, 'count', { ...tags, error: String(error) })
      throw error
    }
  }

  /**
   * Get metrics for a specific time range
   */
  getMetrics(since?: number, name?: string): PerformanceMetric[] {
    let filtered = this.metrics

    if (since) {
      filtered = filtered.filter(m => m.timestamp >= since)
    }

    if (name) {
      filtered = filtered.filter(m => m.name === name)
    }

    return filtered
  }

  /**
   * Get performance statistics
   */
  getStats(name?: string): {
    count: number
    avg: number
    min: number
    max: number
    p50: number
    p95: number
    p99: number
  } {
    const metrics = this.getMetrics(undefined, name)
    
    if (metrics.length === 0) {
      return { count: 0, avg: 0, min: 0, max: 0, p50: 0, p95: 0, p99: 0 }
    }

    const values = metrics.map(m => m.value).sort((a, b) => a - b)
    const count = values.length
    const sum = values.reduce((a, b) => a + b, 0)

    return {
      count,
      avg: Math.round((sum / count) * 100) / 100,
      min: values[0],
      max: values[count - 1],
      p50: this.percentile(values, 0.5),
      p95: this.percentile(values, 0.95),
      p99: this.percentile(values, 0.99)
    }
  }

  /**
   * Get system resource metrics
   */
  getResourceMetrics(): ResourceMetrics {
    const memUsage = process.memoryUsage()
    const cpuUsage = process.cpuUsage()
    
    return {
      memory: {
        used: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
        total: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
        percentage: Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100)
      },
      cpu: {
        usage: Math.round((cpuUsage.user + cpuUsage.system) / 1000), // ms
        loadAverage: process.platform !== 'win32' ? require('os').loadavg() : [0, 0, 0]
      },
      uptime: Math.round(process.uptime())
    }
  }

  /**
   * Clear old metrics
   */
  clearMetrics(olderThan?: number): number {
    const before = this.metrics.length
    
    if (olderThan) {
      this.metrics = this.metrics.filter(m => m.timestamp >= olderThan)
    } else {
      this.metrics = []
    }

    return before - this.metrics.length
  }

  private percentile(values: number[], p: number): number {
    const index = Math.ceil(values.length * p) - 1
    return values[Math.max(0, index)]
  }

  private isSignificantMetric(name: string, value: number, unit: string): boolean {
    // Log slow operations
    if (unit === 'ms' && value > 1000) return true
    
    // Log database operations
    if (name.includes('db_') || name.includes('query_')) return true
    
    // Log scan operations
    if (name.includes('scan_') || name.includes('nuclei_')) return true
    
    // Log API operations that are slow
    if (name.includes('api_') && value > 500) return true
    
    return false
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor()

/**
 * Performance decorator for methods
 */
export function timed(name?: string, tags?: Record<string, string>) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    const metricName = name || `${target.constructor.name}.${propertyKey}`

    descriptor.value = async function (...args: any[]) {
      return performanceMonitor.timeFunction(
        metricName,
        () => originalMethod.apply(this, args),
        tags
      )
    }

    return descriptor
  }
}

/**
 * Database query performance wrapper
 */
export async function timedQuery<T>(
  queryName: string,
  queryFn: () => Promise<T>,
  tags?: Record<string, string>
): Promise<T> {
  return performanceMonitor.timeFunction(
    `db_query_${queryName}`,
    queryFn,
    { type: 'database', ...tags }
  )
}

/**
 * API endpoint performance wrapper
 */
export async function timedApiCall<T>(
  endpoint: string,
  apiFn: () => Promise<T>,
  tags?: Record<string, string>
): Promise<T> {
  return performanceMonitor.timeFunction(
    `api_${endpoint.replace(/[^a-zA-Z0-9]/g, '_')}`,
    apiFn,
    { type: 'api', endpoint, ...tags }
  )
}

/**
 * Nuclei scan performance wrapper
 */
export async function timedScan<T>(
  scanType: string,
  scanFn: () => Promise<T>,
  tags?: Record<string, string>
): Promise<T> {
  return performanceMonitor.timeFunction(
    `scan_${scanType}`,
    scanFn,
    { type: 'scan', scanType, ...tags }
  )
}

/**
 * Start performance monitoring with periodic reporting
 */
export function startPerformanceMonitoring(intervalMs: number = 60000): NodeJS.Timeout {
  return setInterval(() => {
    const resources = performanceMonitor.getResourceMetrics()
    
    // Record resource metrics
    performanceMonitor.recordMetric('memory_used', resources.memory.used, 'MB')
    performanceMonitor.recordMetric('memory_percentage', resources.memory.percentage, '%')
    performanceMonitor.recordMetric('cpu_usage', resources.cpu.usage, 'ms')
    performanceMonitor.recordMetric('uptime', resources.uptime, 's')

    // Log if memory usage is high
    if (resources.memory.percentage > 80) {
      console.warn(`⚠️ High memory usage: ${resources.memory.percentage}% (${resources.memory.used}MB/${resources.memory.total}MB)`)
    }

    // Clean old metrics (keep last 24 hours)
    const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000)
    const cleaned = performanceMonitor.clearMetrics(oneDayAgo)
    
    if (cleaned > 0) {
      console.log(`🧹 Cleaned ${cleaned} old performance metrics`)
    }
  }, intervalMs)
}

/**
 * Get comprehensive performance report
 */
export function getPerformanceReport(): {
  resources: ResourceMetrics
  metrics: {
    database: any
    api: any
    scans: any
    overall: any
  }
  topSlowOperations: Array<{ name: string; avgTime: number; count: number }>
} {
  const resources = performanceMonitor.getResourceMetrics()
  
  // Get metrics by category
  const dbMetrics = performanceMonitor.getMetrics(undefined, undefined)
    .filter(m => m.name.startsWith('db_'))
  
  const apiMetrics = performanceMonitor.getMetrics(undefined, undefined)
    .filter(m => m.name.startsWith('api_'))
  
  const scanMetrics = performanceMonitor.getMetrics(undefined, undefined)
    .filter(m => m.name.startsWith('scan_'))

  // Find slowest operations
  const operationStats = new Map<string, { total: number; count: number }>()
  
  performanceMonitor.getMetrics().forEach(metric => {
    if (metric.unit === 'ms') {
      const existing = operationStats.get(metric.name) || { total: 0, count: 0 }
      existing.total += metric.value
      existing.count += 1
      operationStats.set(metric.name, existing)
    }
  })

  const topSlowOperations = Array.from(operationStats.entries())
    .map(([name, stats]) => ({
      name,
      avgTime: Math.round((stats.total / stats.count) * 100) / 100,
      count: stats.count
    }))
    .sort((a, b) => b.avgTime - a.avgTime)
    .slice(0, 10)

  return {
    resources,
    metrics: {
      database: performanceMonitor.getStats('db_'),
      api: performanceMonitor.getStats('api_'),
      scans: performanceMonitor.getStats('scan_'),
      overall: performanceMonitor.getStats()
    },
    topSlowOperations
  }
}

/**
 * Memory usage tracker
 */
export function trackMemoryUsage(label: string): () => void {
  const startMemory = process.memoryUsage()
  
  return () => {
    const endMemory = process.memoryUsage()
    const diff = {
      heapUsed: endMemory.heapUsed - startMemory.heapUsed,
      heapTotal: endMemory.heapTotal - startMemory.heapTotal,
      external: endMemory.external - startMemory.external
    }
    
    performanceMonitor.recordMetric(
      `memory_diff_${label}`,
      Math.round(diff.heapUsed / 1024 / 1024),
      'MB',
      { type: 'memory_tracking' }
    )
  }
}

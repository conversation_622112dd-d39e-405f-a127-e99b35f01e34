import React from 'react'
import { useScanEvents } from '@/hooks/use-scan-events'
import { ScanStatusBadge } from '@/components/ui'
import { Loader2, Shield } from 'lucide-react'

interface RealTimeScanRowProps {
  scan: any
  children: React.ReactNode
}

export function RealTimeScanRow({ scan, children }: RealTimeScanRowProps) {
  const {
    totalCount,
    severityCount,
    status: realtimeStatus,
    progress: realtimeProgress,
    isConnected
  } = useScanEvents(scan.id)

  // Use real-time data if available and scan is running
  const isRunning = scan.status === 'RUNNING' || scan.status === 'PENDING'
  const displayStatus = (isRunning && realtimeStatus) ? realtimeStatus.status : scan.status
  const displayTotalVulns = (isRunning && totalCount > 0) ? totalCount : scan.totalVulns
  const displaySeverityCount = (isRunning && Object.keys(severityCount).length > 0) ? severityCount : {
    CRITICAL: scan.criticalVulns,
    HIGH: scan.highVulns,
    MEDIUM: scan.mediumVulns,
    LOW: scan.lowVulns,
    INFO: scan.infoVulns
  }



  // Clone children and inject real-time data
  const enhancedChildren = React.Children.map(children, (child) => {
    if (React.isValidElement(child)) {
      // Enhanced scan data with real-time updates
      const enhancedScan = {
        ...scan,
        status: displayStatus,
        totalVulns: displayTotalVulns,
        criticalVulns: displaySeverityCount.CRITICAL || 0,
        highVulns: displaySeverityCount.HIGH || 0,
        mediumVulns: displaySeverityCount.MEDIUM || 0,
        lowVulns: displaySeverityCount.LOW || 0,
        infoVulns: displaySeverityCount.INFO || 0
      }

      // Clone the TR element and modify its children to add real-time indicators
      if (child.type === 'tr') {
        const originalChildren = React.Children.toArray((child as any).props.children)

        // Modify the last cell (actions column) to include real-time indicators
        const modifiedChildren = originalChildren.map((cell, index) => {
          if (React.isValidElement(cell) && index === originalChildren.length - 1) {
            // This is the actions column - add real-time indicators
            return React.cloneElement(cell as React.ReactElement<any>, {
              children: (
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-1">
                    {/* Original actions content */}
                    {(cell as any).props.children}
                  </div>

                  {/* Real-time indicators */}
                  {isRunning && isConnected && (
                    <div className="flex items-center space-x-1 ml-2">
                      {realtimeProgress && (
                        <div className="flex items-center space-x-1 bg-blue-100 text-blue-800 px-1 py-0.5 rounded text-xs">
                          <Loader2 className="h-2 w-2 animate-spin" />
                          <span className="hidden sm:inline">Live</span>
                        </div>
                      )}

                      {totalCount > 0 && (
                        <div className="flex items-center space-x-1 bg-green-100 text-green-800 px-1 py-0.5 rounded text-xs">
                          <Shield className="h-2 w-2" />
                          <span>+{totalCount}</span>
                        </div>
                      )}

                      <div className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse" title="Real-time updates active" />
                    </div>
                  )}
                </div>
              )
            })
          }
          return cell
        })

        return React.cloneElement(child as React.ReactElement<any>, {
          scan: enhancedScan,
          children: modifiedChildren
        })
      }

      return React.cloneElement(child as React.ReactElement<any>, {
        scan: enhancedScan
      })
    }
    return child
  })

  return <>{enhancedChildren}</>
}

import { NextRequest, NextResponse } from 'next/server'
import { getServiceStatus } from '@/lib/init'
import { getRecoveryStatus, forceRecovery } from '@/lib/recovery'
import { processManager } from '@/lib/process-manager'
import { getApplicationStatus } from '@/lib/startup'

/**
 * GET /api/system - Get comprehensive system status
 * 
 * Provides detailed information about all system components,
 * running processes, and recovery status.
 */
export async function GET(request: NextRequest) {
  try {
    const systemStatus = {
      timestamp: new Date().toISOString(),
      application: getApplicationStatus(),
      services: getServiceStatus(),
      recovery: getRecoveryStatus(),
      processes: {
        stats: processManager.getProcessStats(),
        details: processManager.getDetailedProcessInfo()
      },
      system: {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        platform: process.platform,
        nodeVersion: process.version,
        pid: process.pid,
        environment: process.env.NODE_ENV || 'development'
      }
    }

    return NextResponse.json(systemStatus)
  } catch (error) {
    console.error('System status error:', error)
    
    return NextResponse.json({
      error: 'Failed to get system status',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

/**
 * POST /api/system - System management operations
 * 
 * Supports various system management operations like recovery,
 * process cleanup, and service restart.
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, ...params } = body

    switch (action) {
      case 'force-recovery':
        return await handleForceRecovery()
      
      case 'cleanup-processes':
        return await handleCleanupProcesses()
      
      case 'restart-services':
        return await handleRestartServices(params)
      
      case 'kill-process':
        return await handleKillProcess(params.scanId)
      
      default:
        return NextResponse.json({
          error: 'Invalid action',
          supportedActions: ['force-recovery', 'cleanup-processes', 'restart-services', 'kill-process']
        }, { status: 400 })
    }
  } catch (error) {
    console.error('System management error:', error)
    
    return NextResponse.json({
      error: 'System management operation failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

/**
 * Handle force recovery operation
 */
async function handleForceRecovery(): Promise<NextResponse> {
  try {
    console.log('🔧 Manual recovery requested via API')
    
    await forceRecovery()
    
    return NextResponse.json({
      success: true,
      message: 'Recovery operation completed',
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'Recovery operation failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

/**
 * Handle process cleanup operation
 */
async function handleCleanupProcesses(): Promise<NextResponse> {
  try {
    console.log('🧹 Process cleanup requested via API')
    
    const processStats = processManager.getProcessStats()
    const stuckProcesses = processManager.getDetailedProcessInfo()
      .filter(p => p.isStuck)
    
    // Kill stuck processes
    for (const process of stuckProcesses) {
      console.log(`🔪 Killing stuck process: ${process.scanId}`)
      processManager.killProcess(process.scanId)
    }
    
    return NextResponse.json({
      success: true,
      message: `Cleaned up ${stuckProcesses.length} stuck processes`,
      processStats: {
        before: processStats,
        cleanedUp: stuckProcesses.length
      },
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'Process cleanup failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

/**
 * Handle service restart operation
 */
async function handleRestartServices(params: any): Promise<NextResponse> {
  try {
    console.log('🔄 Service restart requested via API')
    
    const { services = ['all'] } = params
    const results: Record<string, { success: boolean; message: string }> = {}
    
    for (const service of services) {
      try {
        switch (service) {
          case 'all':
            // Restart all services by reinitializing
            const { reinitializeApplication } = await import('@/lib/startup')
            await reinitializeApplication()
            results[service] = { success: true, message: 'All services restarted' }
            break
            
          case 'job-queue':
            const { jobQueue } = await import('@/lib/job-queue')
            jobQueue.stop()
            jobQueue.initialize()
            results[service] = { success: true, message: 'Job queue restarted' }
            break
            
          case 'recovery':
            const { recoveryService } = await import('@/lib/recovery')
            recoveryService.stop()
            recoveryService.start()
            results[service] = { success: true, message: 'Recovery service restarted' }
            break
            
          default:
            results[service] = { success: false, message: `Unknown service: ${service}` }
        }
      } catch (error) {
        results[service] = { 
          success: false, 
          message: error instanceof Error ? error.message : 'Unknown error' 
        }
      }
    }
    
    const allSuccessful = Object.values(results).every(r => r.success)
    
    return NextResponse.json({
      success: allSuccessful,
      message: allSuccessful ? 'All services restarted successfully' : 'Some services failed to restart',
      results,
      timestamp: new Date().toISOString()
    }, { status: allSuccessful ? 200 : 207 }) // 207 = Multi-Status
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'Service restart failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

/**
 * Handle kill process operation
 */
async function handleKillProcess(scanId: string): Promise<NextResponse> {
  try {
    if (!scanId) {
      return NextResponse.json({
        success: false,
        error: 'scanId is required'
      }, { status: 400 })
    }
    
    console.log(`🔪 Kill process requested for scan ${scanId}`)
    
    const killed = processManager.killProcess(scanId)
    
    if (killed) {
      return NextResponse.json({
        success: true,
        message: `Process for scan ${scanId} killed successfully`,
        timestamp: new Date().toISOString()
      })
    } else {
      return NextResponse.json({
        success: false,
        message: `No running process found for scan ${scanId}`,
        timestamp: new Date().toISOString()
      }, { status: 404 })
    }
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'Kill process operation failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

/**
 * DELETE /api/system - Emergency shutdown
 * 
 * Performs emergency shutdown of all services and processes.
 * Use with caution - this will stop all running scans.
 */
export async function DELETE(request: NextRequest) {
  try {
    console.log('🚨 Emergency shutdown requested via API')
    
    // Kill all processes
    processManager.killAllProcesses()
    
    // Stop all services
    const { shutdownApplication } = await import('@/lib/startup')
    await shutdownApplication()
    
    return NextResponse.json({
      success: true,
      message: 'Emergency shutdown completed',
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Emergency shutdown error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Emergency shutdown failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

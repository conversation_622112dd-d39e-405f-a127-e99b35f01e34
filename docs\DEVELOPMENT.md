# CTB Scanner - Development Setup Guide

## Overview

This guide helps developers set up CTB Scanner for local development on Windows, macOS, and Linux. The application is a Next.js-based vulnerability scanning platform that integrates with Nuclei scanner.

## Prerequisites

### System Requirements
- **RAM**: Minimum 4GB, Recommended 8GB+
- **Storage**: Minimum 5GB free space
- **Node.js**: Version 18+ (LTS recommended)
- **Git**: Latest version

### Required Software
- Node.js 18+ and npm
- MySQL 8.0+ (or Docker for MySQL)
- Nuclei scanner
- Git
- Code editor (VS Code recommended)

## Platform-Specific Setup

### Windows Setup

#### 1. Install Node.js
- Download from [nodejs.org](https://nodejs.org/)
- Choose LTS version (18+)
- Verify installation: `node --version` and `npm --version`

#### 2. Install MySQL
**Option A: MySQL Installer**
- Download MySQL Installer from [mysql.com](https://dev.mysql.com/downloads/installer/)
- Choose "Developer Default" setup
- Set root password during installation

**Option B: Docker (Recommended)**
```powershell
# Install Docker Desktop first
docker run --name mysql-ctb -e MYSQL_ROOT_PASSWORD=rootroot -p 3306:3306 -d mysql:8.0
```

#### 3. Install Nuclei
```powershell
# Using PowerShell
Invoke-WebRequest -Uri "https://github.com/projectdiscovery/nuclei/releases/latest/download/nuclei_2.9.15_windows_amd64.zip" -OutFile "nuclei.zip"
Expand-Archive -Path "nuclei.zip" -DestinationPath "C:\tools\nuclei"
# Add C:\tools\nuclei to PATH environment variable

# Verify installation
nuclei -version

# Install templates
nuclei -update-templates
```

#### 4. Install Git
- Download from [git-scm.com](https://git-scm.com/)
- Use default settings during installation

### macOS Setup

#### 1. Install Homebrew (if not installed)
```bash
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
```

#### 2. Install Node.js
```bash
brew install node@18
node --version  # Should be 18+
npm --version
```

#### 3. Install MySQL
**Option A: Homebrew**
```bash
brew install mysql
brew services start mysql
mysql_secure_installation
```

**Option B: Docker (Recommended)**
```bash
docker run --name mysql-ctb -e MYSQL_ROOT_PASSWORD=rootroot -p 3306:3306 -d mysql:8.0
```

#### 4. Install Nuclei
```bash
brew install nuclei
nuclei -version
nuclei -update-templates
```

### Linux (Ubuntu/Debian) Setup

#### 1. Update System
```bash
sudo apt update && sudo apt upgrade -y
```

#### 2. Install Node.js
```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs
node --version  # Should be 18+
npm --version
```

#### 3. Install MySQL
**Option A: APT Package**
```bash
sudo apt install -y mysql-server
sudo mysql_secure_installation
```

**Option B: Docker (Recommended)**
```bash
sudo apt install -y docker.io
sudo systemctl start docker
sudo docker run --name mysql-ctb -e MYSQL_ROOT_PASSWORD=rootroot -p 3306:3306 -d mysql:8.0
```

#### 4. Install Nuclei
```bash
wget https://github.com/projectdiscovery/nuclei/releases/latest/download/nuclei_2.9.15_linux_amd64.zip
unzip nuclei_2.9.15_linux_amd64.zip
sudo mv nuclei /usr/local/bin/
sudo chmod +x /usr/local/bin/nuclei
nuclei -version
nuclei -update-templates
```

## Project Setup

### 1. Clone Repository
```bash
git clone <your-repository-url>
cd ctb-scanner
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Environment Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your settings
```

#### Environment Variables (.env)
```bash
# Database
DATABASE_URL="mysql://root:rootroot@localhost:3306/ctb_scanner"

# Authentication
JWT_SECRET="your-development-jwt-secret"
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-development-nextauth-secret"

# Application
NODE_ENV="development"
APP_URL="http://localhost:3000"

# Nuclei (auto-detected if in PATH)
NUCLEI_PATH=""
NUCLEI_TEMPLATES_DIR=""

# Development settings
DEBUG_MODE="true"
LOG_LEVEL="DEBUG"
DEV_TOOLS_ENABLED="true"
```

### 4. Database Setup

#### Create Database
```bash
# Connect to MySQL
mysql -u root -p

# Create database
CREATE DATABASE ctb_scanner CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
EXIT;
```

#### Run Database Migrations
```bash
npx prisma generate
npx prisma db push
```

#### Seed Database (Optional)
```bash
npx prisma db seed
```

### 5. Start Development Server
```bash
npm run dev
```

The application will be available at `http://localhost:3000`

## Development Workflow

### Available Scripts

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run lint:fix     # Fix ESLint issues

# Database
npm run db:generate  # Generate Prisma client
npm run db:push      # Push schema to database
npm run db:migrate   # Create and run migrations
npm run db:seed      # Seed database with sample data
npm run db:reset     # Reset database (dev only)
npm run db:studio    # Open Prisma Studio

# Testing
npm run test         # Run all tests
npm run test:unit    # Run unit tests only
npm run test:watch   # Run tests in watch mode
npm run test:coverage # Run tests with coverage

# Code Quality
npm run type-check   # TypeScript type checking
npm run format       # Format code with Prettier
npm run format:check # Check code formatting
```

### Project Structure
```
ctb-scanner/
├── src/
│   ├── app/                 # Next.js App Router pages
│   ├── components/          # React components
│   │   ├── atoms/          # Basic UI components
│   │   ├── molecules/      # Composite components
│   │   ├── organisms/      # Complex components
│   │   └── templates/      # Page layouts
│   ├── lib/                # Utility libraries
│   │   ├── auth.ts         # Authentication logic
│   │   ├── cache.ts        # Caching system
│   │   ├── db.ts           # Database connection
│   │   ├── logger.ts       # Logging system
│   │   └── nuclei.ts       # Nuclei scanner integration
│   └── types/              # TypeScript type definitions
├── prisma/                 # Database schema and migrations
├── public/                 # Static assets
├── tests/                  # Test files
├── docs/                   # Documentation
└── scripts/                # Build and deployment scripts
```

### Code Style and Standards

#### TypeScript
- Use strict TypeScript configuration
- Define interfaces for all data structures
- Use proper type annotations
- Avoid `any` type

#### React Components
- Use functional components with hooks
- Follow atomic design principles
- Implement proper error boundaries
- Use TypeScript for props

#### Database
- Use Prisma ORM for database operations
- Define proper relationships in schema
- Use transactions for complex operations
- Implement proper error handling

#### Testing
- Write unit tests for utilities and functions
- Write component tests for React components
- Write integration tests for API routes
- Aim for 70%+ test coverage

### Common Development Tasks

#### Adding a New Component
1. Create component file in appropriate directory
2. Define TypeScript interface for props
3. Implement component with proper styling
4. Write unit tests
5. Export from index file

#### Adding a New API Route
1. Create route file in `src/app/api/`
2. Implement proper request validation
3. Add error handling and logging
4. Write integration tests
5. Update API documentation

#### Database Schema Changes
1. Modify `prisma/schema.prisma`
2. Generate migration: `npx prisma migrate dev`
3. Update TypeScript types: `npx prisma generate`
4. Test changes locally
5. Update seed data if needed

#### Adding New Dependencies
```bash
# Production dependency
npm install package-name

# Development dependency
npm install -D package-name

# Update package.json and package-lock.json
```

## Troubleshooting

### Common Issues

#### 1. Database Connection Issues
```bash
# Check MySQL service
# Windows: Check Services app
# macOS: brew services list | grep mysql
# Linux: sudo systemctl status mysql

# Test connection
mysql -u root -p -h localhost -P 3306

# Reset database
npm run db:reset
```

#### 2. Nuclei Scanner Issues
```bash
# Check Nuclei installation
nuclei -version

# Update templates
nuclei -update-templates

# Check templates directory
# Windows: %USERPROFILE%\.nuclei-templates
# macOS/Linux: ~/.nuclei-templates
```

#### 3. Node.js/npm Issues
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Check Node.js version
node --version  # Should be 18+
```

#### 4. Port Already in Use
```bash
# Find process using port 3000
# Windows: netstat -ano | findstr :3000
# macOS/Linux: lsof -i :3000

# Kill process
# Windows: taskkill /PID <PID> /F
# macOS/Linux: kill -9 <PID>
```

#### 5. TypeScript Errors
```bash
# Check TypeScript configuration
npm run type-check

# Regenerate Prisma client
npx prisma generate

# Clear Next.js cache
rm -rf .next
npm run dev
```

### Performance Tips

1. **Database Optimization**
   - Use database indexes for frequently queried fields
   - Implement connection pooling
   - Use database transactions for related operations

2. **Frontend Optimization**
   - Use Next.js Image component for images
   - Implement proper loading states
   - Use React.memo for expensive components

3. **Development Speed**
   - Use TypeScript strict mode
   - Enable ESLint and Prettier
   - Use VS Code extensions for better DX

### VS Code Extensions (Recommended)

- TypeScript and JavaScript Language Features
- Prisma
- Tailwind CSS IntelliSense
- ESLint
- Prettier
- GitLens
- Thunder Client (API testing)

### Environment-Specific Notes

#### Windows
- Use PowerShell or Command Prompt
- Consider using WSL2 for better Linux compatibility
- Docker Desktop for containerized services

#### macOS
- Use Terminal or iTerm2
- Homebrew for package management
- Docker Desktop for containerized services

#### Linux
- Use terminal of choice
- APT/YUM for system packages
- Docker for containerized services

## 📁 Detailed Project Structure

```
ctb-scanner/
├── public/                        # Static assets (SVGs, images)
├── prisma/                        # Database schema and migrations
│   ├── schema.prisma
│   └── migrations/
├── scripts/                       # Utility and test scripts
│   ├── diagnose-nuclei.js
│   ├── test-api.js
│   ├── test-db.js
│   ├── test-nuclei-integration.js
│   ├── test-real-time-logs.js
│   ├── test-scan.js
│   └── test-10-second-scan.md
├── src/
│   ├── app/
│   │   ├── api/
│   │   │   ├── admin/
│   │   │   ├── assets/
│   │   │   ├── auth/
│   │   │   ├── dashboard/
│   │   │   ├── queue/
│   │   │   ├── scans/
│   │   │   └── vulnerabilities/
│   │   ├── dashboard/
│   │   │   ├── assets/
│   │   │   ├── profile/
│   │   │   ├── scan/
│   │   │   ├── scans/
│   │   │   ├── settings/
│   │   │   └── vulnerabilities/
│   │   ├── login/
│   │   ├── signup/
│   │   ├── layout.tsx
│   │   ├── page.tsx
│   │   └── globals.css
│   ├── components/                   # Atomic Design System
│   │   ├── atoms/                   # Basic building blocks
│   │   │   ├── button.tsx
│   │   │   ├── input.tsx
│   │   │   ├── badge.tsx
│   │   │   ├── avatar.tsx
│   │   │   └── index.ts
│   │   ├── molecules/               # Combinations of atoms
│   │   │   ├── form-field.tsx
│   │   │   ├── card.tsx
│   │   │   ├── status-badge.tsx
│   │   │   ├── pagination.tsx
│   │   │   └── index.ts
│   │   ├── organisms/               # Complex UI components
│   │   │   ├── data-table.tsx
│   │   │   ├── sidebar.tsx
│   │   │   ├── scan-form.tsx
│   │   │   └── index.ts
│   │   ├── templates/               # Page-level layouts
│   │   │   ├── page-layout.tsx
│   │   │   └── index.ts
│   │   ├── features/                # Feature-specific components
│   │   ├── client-wrapper.tsx
│   │   ├── index.ts
│   │   ├── loading.tsx
│   │   ├── no-ssr.tsx
│   │   ├── protected-route.tsx
│   │   ├── sidebar.tsx
│   │   ├── features/
│   │   │   └── scan/
│   │   ├── layout/
│   │   │   ├── data-table.tsx
│   │   │   ├── index.ts
│   │   │   ├── page-container.tsx
│   │   │   └── page-header.tsx
│   │   └── ui/
│   │       ├── alert.tsx
│   │       ├── badge.tsx
│   │       ├── button.tsx
│   │       ├── card.tsx
│   │       ├── empty-state.tsx
│   │       ├── index.ts
│   │       ├── input.tsx
│   │       ├── label.tsx
│   │       ├── loading-spinner.tsx
│   │       ├── pagination.tsx
│   │       ├── status-badge.tsx
│   │       └── tabs.tsx
│   ├── contexts/
│   │   └── auth-context.tsx
│   ├── hooks/                       # Custom React hooks
│   │   ├── use-pagination.ts
│   │   ├── use-debounce.ts
│   │   ├── use-scan-events.ts
│   │   ├── use-api.ts
│   │   ├── use-export.ts
│   │   └── index.ts
│   ├── lib/                         # Core utilities and services
│   │   ├── constants/               # Application constants
│   │   │   └── index.ts
│   │   ├── types/                   # TypeScript type definitions
│   │   │   └── index.ts
│   │   ├── auth.ts
│   │   ├── db.ts
│   │   ├── errors.ts
│   │   ├── job-queue.ts
│   │   ├── nuclei.ts
│   │   ├── pdf-generator.ts         # PDF report generation
│   │   ├── process-manager.ts
│   │   ├── rate-limit.ts
│   │   ├── scan-events.ts
│   │   ├── security.ts
│   │   ├── utils.ts
│   │   ├── validations.ts
│   │   ├── index.ts                 # Barrel exports
│   │   └── __tests__/
│   │       ├── auth.test.ts
│   │       └── validations.test.ts
│   └── middleware.ts
├── temp/
│   └── nuclei/                     # Temporary scan result files
├── .env.example                    # Example environment variables
├── eslint.config.mjs
├── next-env.d.ts
├── next.config.ts
├── NUCLEI_TEMPLATE_DESIGN.md
├── package.json
├── postcss.config.mjs
├── README.md
├── simple-test.json
├── test-nuclei-comparison.js
├── test-redirect-fix.json
├── tsconfig.json
```

## Getting Help

1. **Documentation**: Check `/docs` directory
2. **Logs**: Check console output and log files
3. **Database**: Use Prisma Studio (`npm run db:studio`)
4. **API Testing**: Use Thunder Client or Postman
5. **Debugging**: Use browser dev tools and VS Code debugger

## Contributing

1. Create feature branch from `main`
2. Make changes following code standards
3. Write/update tests
4. Run linting and type checking
5. Submit pull request with clear description

---

**Note**: This guide assumes basic familiarity with Node.js, React, and database concepts. For beginners, consider reviewing the official documentation for Next.js, Prisma, and TypeScript.

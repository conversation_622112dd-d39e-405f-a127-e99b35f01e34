import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { requireAuth } from '@/lib/auth'
import { handleApiError } from '@/lib/errors'

export async function GET(request: NextRequest) {
  try {
    // Authentication
    const currentUser = await requireAuth()

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 100)
    const severity = searchParams.get('severity')
    const search = searchParams.get('search')
    const offset = (page - 1) * limit

    // Build where clause
    const where: Record<string, unknown> = {
      scan: {
        userId: currentUser.userId
      }
    }

    if (severity) {
      where.severity = severity.toUpperCase()
    }

    if (search) {
      where.OR = [
        { name: { contains: search } },
        { templateId: { contains: search } },
        { host: { contains: search } },
        { description: { contains: search } }
      ]
    }

    // Get vulnerabilities with pagination
    const [vulnerabilities, total] = await Promise.all([
      db.vulnerability.findMany({
        where,
        include: {
          scan: {
            select: {
              id: true,
              targetUrl: true,
              asset: {
                select: {
                  id: true,
                  title: true,
                  domain: true
                }
              }
            }
          }
        },
        orderBy: [
          { severity: 'desc' },
          { timestamp: 'desc' }
        ],
        skip: offset,
        take: limit
      }),
      db.vulnerability.count({ where })
    ])

    return NextResponse.json({
      vulnerabilities,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    return handleApiError(error)
  }
}

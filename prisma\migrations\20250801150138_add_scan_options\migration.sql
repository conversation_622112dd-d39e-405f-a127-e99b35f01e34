-- AlterTable
ALTER TABLE `scans` ADD COLUMN `exclude_templates` <PERSON><PERSON><PERSON> NULL,
    ADD COLUMN `input_type` VARCHAR(50) NULL,
    ADD COLUMN `scan_mode` VARCHAR(50) NULL,
    ADD COLUMN `scan_type` VARCHAR(50) NULL,
    ADD COLUMN `severity` J<PERSON><PERSON> NULL,
    ADD COLUMN `tags` JSO<PERSON> NULL,
    ADD COLUMN `templates` JSON NULL;

-- AlterTable
ALTER TABLE `vulnerabilities` MODIFY `description` LONGTEXT NULL,
    MODIFY `matcher` LONGTEXT NULL,
    <PERSON><PERSON><PERSON>Y `request` LONGTEXT NULL,
    MODIFY `response` LONGTEXT NULL,
    MODIFY `curl_command` LONGTEXT NULL;

-- CreateIndex
CREATE INDEX `scans_scan_type_idx` ON `scans`(`scan_type`);

-- CreateIndex
CREATE INDEX `scans_scan_mode_idx` ON `scans`(`scan_mode`);

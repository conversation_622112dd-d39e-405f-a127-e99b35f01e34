# CTB Scanner - Production Deployment Guide

## Overview

This guide covers deploying CTB Scanner to an Azure Ubuntu VM for production use. The application is a Next.js-based vulnerability scanning platform that integrates with Nuclei scanner.

## Prerequisites

### System Requirements
- **OS**: Ubuntu 20.04 LTS or later
- **RAM**: Minimum 4GB, Recommended 8GB+
- **Storage**: Minimum 20GB free space
- **CPU**: 2+ cores recommended
- **Network**: Outbound internet access for vulnerability scanning

### Required Software
- Node.js 18+ and npm
- MySQL 8.0+
- Nuclei scanner
- PM2 (for process management)
- Nginx (reverse proxy)
- SSL certificate (Let's Encrypt recommended)

## Step 1: Server Setup

### 1.1 Update System
```bash
sudo apt update && sudo apt upgrade -y
sudo apt install -y curl wget git unzip build-essential
```

### 1.2 Install Node.js
```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs
node --version  # Should be 18+
npm --version
```

### 1.3 Install MySQL
```bash
sudo apt install -y mysql-server
sudo mysql_secure_installation
```

### 1.4 Install Nuclei
```bash
# Download latest Nuclei
wget https://github.com/projectdiscovery/nuclei/releases/latest/download/nuclei_2.9.15_linux_amd64.zip
unzip nuclei_2.9.15_linux_amd64.zip
sudo mv nuclei /usr/local/bin/
sudo chmod +x /usr/local/bin/nuclei

# Install Nuclei templates
sudo mkdir -p /opt/nuclei-templates
sudo nuclei -update-templates -update-template-dir /opt/nuclei-templates
```

### 1.5 Install PM2
```bash
sudo npm install -g pm2
```

### 1.6 Install Nginx
```bash
sudo apt install -y nginx
sudo systemctl enable nginx
sudo systemctl start nginx
```

## Step 2: Application Deployment

### 2.1 Create Application User
```bash
sudo useradd -m -s /bin/bash ctbscanner
sudo usermod -aG sudo ctbscanner
```

### 2.2 Clone and Setup Application
```bash
sudo su - ctbscanner
git clone <your-repository-url> /home/<USER>/ctb-scanner
cd /home/<USER>/ctb-scanner

# Install dependencies
npm ci --production

# Copy environment configuration
cp .env.production .env
```

### 2.3 Configure Environment Variables
Edit `/home/<USER>/ctb-scanner/.env`:

```bash
# Application
NODE_ENV="production"
APP_URL="https://your-domain.com"

# Database
DATABASE_URL="mysql://ctb_user:secure_password@localhost:3306/ctb_scanner_prod"

# Security (CHANGE THESE!)
JWT_SECRET="your-production-256-bit-secret-key-here"
NEXTAUTH_URL="https://your-domain.com"
NEXTAUTH_SECRET="your-production-nextauth-secret"

# Nuclei
NUCLEI_PATH="/usr/local/bin/nuclei"
NUCLEI_TEMPLATES_DIR="/opt/nuclei-templates"

# Logging
LOG_LEVEL="WARN"
LOG_FILE_ENABLED="true"
LOG_FILE_PATH="/var/log/ctb-scanner/app.log"
```

### 2.4 Setup Database
```bash
# Create database and user
sudo mysql -u root -p
```

```sql
CREATE DATABASE ctb_scanner_prod CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'ctb_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON ctb_scanner_prod.* TO 'ctb_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

```bash
# Run database migrations
cd /home/<USER>/ctb-scanner
npx prisma generate
npx prisma db push
```

### 2.5 Setup Logging Directory
```bash
sudo mkdir -p /var/log/ctb-scanner
sudo chown ctbscanner:ctbscanner /var/log/ctb-scanner
sudo chmod 755 /var/log/ctb-scanner
```

### 2.6 Build Application
```bash
cd /home/<USER>/ctb-scanner
npm run build
```

## Step 3: Process Management with PM2

### 3.1 Create PM2 Configuration
Create `/home/<USER>/ctb-scanner/ecosystem.config.js`:

```javascript
module.exports = {
  apps: [{
    name: 'ctb-scanner',
    script: 'npm',
    args: 'start',
    cwd: '/home/<USER>/ctb-scanner',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: '/var/log/ctb-scanner/pm2-error.log',
    out_file: '/var/log/ctb-scanner/pm2-out.log',
    log_file: '/var/log/ctb-scanner/pm2-combined.log',
    time: true
  }]
};
```

### 3.2 Start Application with PM2
```bash
cd /home/<USER>/ctb-scanner
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

## Step 4: Nginx Configuration

### 4.1 Create Nginx Site Configuration
Create `/etc/nginx/sites-available/ctb-scanner`:

```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";

    # Proxy to Next.js application
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # File upload size limit
    client_max_body_size 10M;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
}
```

### 4.2 Enable Site and Restart Nginx
```bash
sudo ln -s /etc/nginx/sites-available/ctb-scanner /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## Step 5: SSL Certificate (Let's Encrypt)

### 5.1 Install Certbot
```bash
sudo apt install -y certbot python3-certbot-nginx
```

### 5.2 Obtain SSL Certificate
```bash
sudo certbot --nginx -d your-domain.com -d www.your-domain.com
```

### 5.3 Setup Auto-renewal
```bash
sudo crontab -e
# Add this line:
0 12 * * * /usr/bin/certbot renew --quiet
```

## Step 6: Firewall Configuration

```bash
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw status
```

## Step 7: Monitoring and Maintenance

### 7.1 Setup Log Rotation
Create `/etc/logrotate.d/ctb-scanner`:

```
/var/log/ctb-scanner/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 ctbscanner ctbscanner
    postrotate
        pm2 reload ctb-scanner
    endscript
}
```

### 7.2 Health Check Script
Create `/home/<USER>/health-check.sh`:

```bash
#!/bin/bash
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/api/health)
if [ $response != "200" ]; then
    echo "Health check failed with status: $response"
    pm2 restart ctb-scanner
    echo "Application restarted at $(date)" >> /var/log/ctb-scanner/restart.log
fi
```

```bash
chmod +x /home/<USER>/health-check.sh
# Add to crontab
*/5 * * * * /home/<USER>/health-check.sh
```

## Step 8: Backup Strategy

### 8.1 Database Backup Script
Create `/home/<USER>/backup-db.sh`:

```bash
#!/bin/bash
BACKUP_DIR="/home/<USER>/backups"
DATE=$(date +%Y%m%d_%H%M%S)
mkdir -p $BACKUP_DIR

mysqldump -u ctb_user -p'secure_password' ctb_scanner_prod > $BACKUP_DIR/ctb_scanner_$DATE.sql
gzip $BACKUP_DIR/ctb_scanner_$DATE.sql

# Keep only last 7 days of backups
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete
```

```bash
chmod +x /home/<USER>/backup-db.sh
# Add to crontab for daily backup at 2 AM
0 2 * * * /home/<USER>/backup-db.sh
```

## Troubleshooting

### Common Issues

1. **Application won't start**
   - Check PM2 logs: `pm2 logs ctb-scanner`
   - Verify environment variables in `.env`
   - Check database connectivity

2. **Nuclei scanner not working**
   - Verify Nuclei installation: `nuclei -version`
   - Check templates directory: `ls -la /opt/nuclei-templates`
   - Update templates: `sudo nuclei -update-templates -update-template-dir /opt/nuclei-templates`

3. **Database connection issues**
   - Check MySQL service: `sudo systemctl status mysql`
   - Verify database credentials
   - Test connection: `mysql -u ctb_user -p ctb_scanner_prod`

4. **SSL certificate issues**
   - Check certificate validity: `sudo certbot certificates`
   - Renew manually: `sudo certbot renew`

### Performance Optimization

1. **Database optimization**
   ```sql
   -- Add indexes for better performance
   CREATE INDEX idx_scans_created_at ON scans(created_at);
   CREATE INDEX idx_vulnerabilities_severity ON vulnerabilities(severity);
   ```

2. **PM2 cluster mode** (for high traffic)
   ```javascript
   // In ecosystem.config.js
   instances: 'max', // Use all CPU cores
   exec_mode: 'cluster'
   ```

## Security Checklist

- [ ] Changed all default passwords and secrets
- [ ] Enabled firewall with minimal required ports
- [ ] SSL certificate installed and auto-renewal configured
- [ ] Security headers configured in Nginx
- [ ] Database user has minimal required privileges
- [ ] Application runs as non-root user
- [ ] Log files have appropriate permissions
- [ ] Regular security updates scheduled

## Maintenance Tasks

### Daily
- Monitor application logs
- Check disk space usage
- Verify backup completion

### Weekly
- Update Nuclei templates
- Review security logs
- Check SSL certificate status

### Monthly
- Update system packages
- Review and rotate logs
- Performance monitoring review
- Security audit

## Support

For issues and support:
1. Check application logs in `/var/log/ctb-scanner/`
2. Review PM2 process status: `pm2 status`
3. Check system resources: `htop`, `df -h`
4. Verify service status: `sudo systemctl status nginx mysql`

---

**Note**: Replace `your-domain.com` and other placeholder values with your actual configuration before deployment.

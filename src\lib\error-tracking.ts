/**
 * Error Tracking and Monitoring System
 * 
 * Provides comprehensive error tracking, aggregation, and alerting
 * for production monitoring and debugging.
 */

// Client-safe logger that only uses console
const clientLogger = {
  error: (message: string, error?: Error, context?: Record<string, any>) => {
    console.error(message, error, context)
  },
  info: (message: string, context?: Record<string, any>) => {
    console.info(message, context)
  },
  warn: (message: string, context?: Record<string, any>) => {
    console.warn(message, context)
  }
}

export interface ErrorEvent {
  id: string
  timestamp: number
  error: {
    name: string
    message: string
    stack?: string
  }
  context: {
    component?: string
    userId?: string
    requestId?: string
    url?: string
    method?: string
    userAgent?: string
    ip?: string
    [key: string]: any
  }
  severity: 'low' | 'medium' | 'high' | 'critical'
  fingerprint: string
  count: number
  firstSeen: number
  lastSeen: number
}

export interface ErrorStats {
  totalErrors: number
  uniqueErrors: number
  errorsByComponent: Record<string, number>
  errorsBySeverity: Record<string, number>
  topErrors: Array<{
    fingerprint: string
    message: string
    count: number
    lastSeen: number
  }>
  recentErrors: ErrorEvent[]
}

/**
 * Error tracking system
 */
class ErrorTracker {
  private errors = new Map<string, ErrorEvent>()
  private maxErrors = 1000 // Keep last 1000 unique errors
  private maxAge = 7 * 24 * 60 * 60 * 1000 // 7 days

  /**
   * Track an error event
   */
  trackError(error: Error, context: Record<string, any> = {}, severity: 'low' | 'medium' | 'high' | 'critical' = 'medium'): string {
    const timestamp = Date.now()
    const fingerprint = this.generateFingerprint(error, context)
    
    const existingError = this.errors.get(fingerprint)
    
    if (existingError) {
      // Update existing error
      existingError.count++
      existingError.lastSeen = timestamp
      existingError.context = { ...existingError.context, ...context }
    } else {
      // Create new error event
      const errorEvent: ErrorEvent = {
        id: this.generateId(),
        timestamp,
        error: {
          name: error.name,
          message: error.message,
          stack: error.stack
        },
        context,
        severity,
        fingerprint,
        count: 1,
        firstSeen: timestamp,
        lastSeen: timestamp
      }
      
      this.errors.set(fingerprint, errorEvent)
      
      // Clean up old errors if we exceed the limit
      this.cleanup()
    }

    // Log the error
    clientLogger.error(`Error tracked: ${error.message}`, error, {
      component: 'ERROR_TRACKING',
      fingerprint,
      severity,
      count: this.errors.get(fingerprint)?.count || 1,
      ...context
    })

    // Alert on critical errors or high frequency
    this.checkForAlerts(fingerprint, severity)

    return fingerprint
  }

  /**
   * Get error statistics
   */
  getStats(): ErrorStats {
    const errors = Array.from(this.errors.values())
    
    // Calculate stats
    const totalErrors = errors.reduce((sum, error) => sum + error.count, 0)
    const uniqueErrors = errors.length
    
    const errorsByComponent: Record<string, number> = {}
    const errorsBySeverity: Record<string, number> = {}
    
    errors.forEach(error => {
      const component = error.context.component || 'unknown'
      errorsByComponent[component] = (errorsByComponent[component] || 0) + error.count
      errorsBySeverity[error.severity] = (errorsBySeverity[error.severity] || 0) + error.count
    })

    // Top errors by count
    const topErrors = errors
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)
      .map(error => ({
        fingerprint: error.fingerprint,
        message: error.error.message,
        count: error.count,
        lastSeen: error.lastSeen
      }))

    // Recent errors (last 24 hours)
    const oneDayAgo = Date.now() - 24 * 60 * 60 * 1000
    const recentErrors = errors
      .filter(error => error.lastSeen >= oneDayAgo)
      .sort((a, b) => b.lastSeen - a.lastSeen)
      .slice(0, 20)

    return {
      totalErrors,
      uniqueErrors,
      errorsByComponent,
      errorsBySeverity,
      topErrors,
      recentErrors
    }
  }

  /**
   * Get specific error details
   */
  getError(fingerprint: string): ErrorEvent | undefined {
    return this.errors.get(fingerprint)
  }

  /**
   * Get errors by component
   */
  getErrorsByComponent(component: string): ErrorEvent[] {
    return Array.from(this.errors.values())
      .filter(error => error.context.component === component)
      .sort((a, b) => b.lastSeen - a.lastSeen)
  }

  /**
   * Get errors by severity
   */
  getErrorsBySeverity(severity: 'low' | 'medium' | 'high' | 'critical'): ErrorEvent[] {
    return Array.from(this.errors.values())
      .filter(error => error.severity === severity)
      .sort((a, b) => b.lastSeen - a.lastSeen)
  }

  /**
   * Clear all errors
   */
  clear(): void {
    this.errors.clear()
    clientLogger.info('Error tracking data cleared', { component: 'ERROR_TRACKING' })
  }

  /**
   * Generate error fingerprint for deduplication
   */
  private generateFingerprint(error: Error, context: Record<string, any>): string {
    // Extract the first meaningful line from stack trace (usually the error location)
    const stackLine = error.stack?.split('\n')[1]?.trim() || ''

    const components = [
      error.name,
      error.message,
      context.component || 'unknown',
      stackLine // Include first stack line for location-based fingerprinting
    ]

    return this.hash(components.join('|'))
  }

  /**
   * Generate unique ID
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  /**
   * Simple hash function
   */
  private hash(str: string): string {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36)
  }

  /**
   * Clean up old errors
   */
  private cleanup(): void {
    const now = Date.now()
    const errors = Array.from(this.errors.entries())
    
    // Remove old errors
    const validErrors = errors.filter(([_, error]) => 
      now - error.lastSeen < this.maxAge
    )
    
    // If still too many, keep only the most recent
    if (validErrors.length > this.maxErrors) {
      validErrors.sort(([_, a], [__, b]) => b.lastSeen - a.lastSeen)
      validErrors.splice(this.maxErrors)
    }
    
    // Rebuild the map
    this.errors.clear()
    validErrors.forEach(([fingerprint, error]) => {
      this.errors.set(fingerprint, error)
    })
  }

  /**
   * Check for alert conditions
   */
  private checkForAlerts(fingerprint: string, severity: 'low' | 'medium' | 'high' | 'critical'): void {
    const error = this.errors.get(fingerprint)
    if (!error) return

    // Alert on critical errors immediately
    if (severity === 'critical') {
      this.sendAlert('critical_error', {
        message: error.error.message,
        count: error.count,
        component: error.context.component
      })
    }

    // Alert on high frequency errors (more than 10 occurrences in 5 minutes)
    if (error.count >= 10) {
      const fiveMinutesAgo = Date.now() - 5 * 60 * 1000
      if (error.firstSeen >= fiveMinutesAgo) {
        this.sendAlert('high_frequency_error', {
          message: error.error.message,
          count: error.count,
          component: error.context.component,
          timespan: '5 minutes'
        })
      }
    }
  }

  /**
   * Send alert (placeholder for actual alerting system)
   */
  private sendAlert(type: string, data: Record<string, any>): void {
    clientLogger.warn(`Error alert: ${type}`, {
      component: 'ERROR_TRACKING',
      alertType: type,
      ...data
    })

    // In production, this could send to:
    // - Email notifications
    // - Slack/Discord webhooks
    // - PagerDuty
    // - External monitoring services
    console.warn(`🚨 ERROR ALERT [${type}]:`, data)
  }
}

// Global error tracker instance
export const errorTracker = new ErrorTracker()

// Convenience functions
export function trackError(error: Error, context?: Record<string, any>, severity?: 'low' | 'medium' | 'high' | 'critical'): string {
  return errorTracker.trackError(error, context, severity)
}

export function getErrorStats(): ErrorStats {
  return errorTracker.getStats()
}

export function getError(fingerprint: string): ErrorEvent | undefined {
  return errorTracker.getError(fingerprint)
}

// Global error handlers
if (typeof window === 'undefined') {
  // Server-side error handling
  process.on('uncaughtException', (error) => {
    trackError(error, { component: 'UNCAUGHT_EXCEPTION' }, 'critical')
    console.error('Uncaught Exception:', error)
    // Don't exit in production, let the process manager handle it
  })

  process.on('unhandledRejection', (reason, promise) => {
    const error = reason instanceof Error ? reason : new Error(String(reason))
    trackError(error, { component: 'UNHANDLED_REJECTION', promise: String(promise) }, 'high')
    console.error('Unhandled Rejection:', reason)
  })
}

// Export types for external use
export type { ErrorEvent, ErrorStats }

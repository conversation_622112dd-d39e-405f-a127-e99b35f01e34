const { PrismaClient } = require('@prisma/client')
const request = require('supertest')
const express = require('express')
const bcrypt = require('bcryptjs')

// Setup for integration tests
let prisma
let app, server, baseURL

// Increase timeout for integration tests
jest.setTimeout(30000)

beforeAll(async () => {
  // Mock environment variables for tests
  process.env.NODE_ENV = 'test'
  process.env.JWT_SECRET = 'test-jwt-secret'
  process.env.DATABASE_URL = process.env.TEST_DATABASE_URL || 'mysql://root:rootroot@localhost:3306/ctb_scanner_test'

  // Initialize test database
  prisma = new PrismaClient({
    datasources: {
      db: {
        url: process.env.TEST_DATABASE_URL || 'mysql://root:rootroot@localhost:3306/ctb_scanner_test'
      }
    }
  })

  // Connect to database
  await prisma.$connect()

  // Clean database before tests
  await cleanDatabase()

  // Note: We don't clean database before each test to allow tests that need
  // to test duplicate scenarios. Individual tests should clean up if needed.

  // Setup Express app for testing (simpler than Next.js)
  app = express()
  app.use(express.json())
  app.use(require('cookie-parser')())

  // Import and setup API routes
  const createAuthRouter = require('../api-mocks/auth')
  const createScansRouter = require('../api-mocks/scans')

  app.use('/api/auth', createAuthRouter(prisma))
  app.use('/api/scans', createScansRouter(prisma))

  // Start server
  server = app.listen(0)
  const address = server.address()
  baseURL = `http://localhost:${address.port}`
})

// Note: We don't clean database before each test to allow tests that need
// to test duplicate scenarios. Individual tests should clean up if needed.

afterAll(async () => {
  // Clean up after all tests
  await cleanDatabase()
  await prisma.$disconnect()

  // Close server
  if (server) {
    server.close()
  }
})

// Helper function to make test requests
const testRequest = () => {
  return request(app)
}

// Helper function to create test user and get auth cookie
const createTestUser = async (userData = {}) => {
  const defaultUser = {
    firstName: 'Test',
    lastName: 'User',
    companyName: 'Test Company',
    country: 'US',
    email: `test-${Date.now()}@example.com`,
    password: 'testpassword123',
    confirmPassword: 'testpassword123',
    ...userData
  }

  // Create user
  const signupResponse = await request(app)
    .post('/api/auth/signup')
    .send(defaultUser)
    .expect(201)

  // Add a small delay to ensure user is fully created
  await new Promise(resolve => setTimeout(resolve, 200))

  // Login to get auth cookie
  const loginResponse = await testRequest()
    .post('/api/auth/login')
    .send({
      email: defaultUser.email,
      password: defaultUser.password
    })

  if (loginResponse.status !== 200) {
    throw new Error(`Login failed with status ${loginResponse.status}`)
  }

  const authCookie = loginResponse.headers['set-cookie']
    ?.find(cookie => cookie.startsWith('token='))
    ?.split(';')[0] // Remove additional cookie attributes

  if (!authCookie) {
    throw new Error('Auth cookie not found in login response')
  }

  return {
    user: signupResponse.body.user,
    authCookie
  }
}

// Make helper functions globally available
global.testRequest = testRequest
global.createTestUser = createTestUser



async function cleanDatabase() {
  // Delete in correct order to respect foreign key constraints
  await prisma.vulnerability.deleteMany()
  await prisma.scan.deleteMany()
  await prisma.asset.deleteMany()
  await prisma.user.deleteMany()
}

// Export helper functions
module.exports = {
  testRequest,
  createTestUser
}

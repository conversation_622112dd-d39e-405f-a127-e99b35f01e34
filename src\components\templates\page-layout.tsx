import * as React from 'react'
import { cn } from '@/lib/utils'
import { Heading } from '../atoms/heading'
import { Text } from '../atoms/text'
import { Button } from '../atoms/button'

export interface PageLayoutProps {
  title: string
  subtitle?: string
  actions?: React.ReactNode
  children: React.ReactNode
  className?: string
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full'
  padding?: 'none' | 'sm' | 'md' | 'lg'
}

const maxWidthConfig = {
  sm: 'max-w-sm',
  md: 'max-w-2xl',
  lg: 'max-w-4xl',
  xl: 'max-w-6xl',
  '2xl': 'max-w-7xl',
  full: 'max-w-full',
}

const paddingConfig = {
  none: 'px-0 py-0',
  sm: 'px-4 py-6',
  md: 'px-6 py-8',
  lg: 'px-8 py-12',
}

export const PageLayout: React.FC<PageLayoutProps> = ({
  title,
  subtitle,
  actions,
  children,
  className,
  maxWidth = 'lg',
  padding = 'md',
}) => {
  return (
    <div
      className={cn(
        'mx-auto w-full',
        maxWidthConfig[maxWidth],
        paddingConfig[padding],
        className
      )}
    >
      {/* Page Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <Heading level={1} className="mb-2">
              {title}
            </Heading>
            {subtitle && (
              <Text variant="body" color="muted">
                {subtitle}
              </Text>
            )}
          </div>
          {actions && (
            <div className="flex items-center space-x-3">
              {actions}
            </div>
          )}
        </div>
      </div>

      {/* Page Content */}
      <div>
        {children}
      </div>
    </div>
  )
}

/**
 * Unit tests for Cache system
 */

import { <PERSON><PERSON><PERSON>, CacheManager, cacheManager } from '../cache'

describe('MemoryCache', () => {
  let cache: MemoryCache<string>

  beforeEach(() => {
    cache = new MemoryCache<string>({
      maxSize: 3,
      ttl: 1000, // 1 second
      strategy: 'lru'
    })
  })

  describe('Basic Operations', () => {
    it('should set and get values', () => {
      cache.set('key1', 'value1')
      expect(cache.get('key1')).toBe('value1')
    })

    it('should return undefined for non-existent keys', () => {
      expect(cache.get('nonexistent')).toBeNull()
    })

    it('should check if key exists', () => {
      cache.set('key1', 'value1')
      expect(cache.has('key1')).toBe(true)
      expect(cache.has('nonexistent')).toBe(false)
    })

    it('should delete values', () => {
      cache.set('key1', 'value1')
      expect(cache.has('key1')).toBe(true)
      
      cache.delete('key1')
      expect(cache.has('key1')).toBe(false)
    })

    it('should clear all values', () => {
      cache.set('key1', 'value1')
      cache.set('key2', 'value2')
      expect(cache.size()).toBe(2)
      
      cache.clear()
      expect(cache.size()).toBe(0)
    })

    it('should return correct size', () => {
      expect(cache.size()).toBe(0)
      
      cache.set('key1', 'value1')
      expect(cache.size()).toBe(1)
      
      cache.set('key2', 'value2')
      expect(cache.size()).toBe(2)
    })
  })

  describe('TTL (Time To Live)', () => {
    it('should expire values after TTL', async () => {
      const shortTtlCache = new MemoryCache<string>({
        maxSize: 10,
        ttl: 50, // 50ms
        strategy: 'lru'
      })

      shortTtlCache.set('key1', 'value1')
      expect(shortTtlCache.get('key1')).toBe('value1')

      // Wait for TTL to expire
      await new Promise(resolve => setTimeout(resolve, 60))
      
      expect(shortTtlCache.get('key1')).toBeNull()
    })

    it('should allow custom TTL per item', () => {
      cache.set('key1', 'value1', 2000) // 2 seconds
      cache.set('key2', 'value2', 100)  // 100ms

      expect(cache.get('key1')).toBe('value1')
      expect(cache.get('key2')).toBe('value2')

      // After 150ms, key2 should be expired but key1 should still exist
      setTimeout(() => {
        expect(cache.get('key1')).toBe('value1')
        expect(cache.get('key2')).toBeUndefined()
      }, 150)
    })
  })

  describe('Eviction Policies', () => {
    it('should evict LRU items when cache is full', async () => {
      const lruCache = new MemoryCache<string>({
        maxSize: 2,
        ttl: 10000,
        strategy: 'lru'
      })

      lruCache.set('key1', 'value1')
      // Small delay to ensure different timestamps
      await new Promise(resolve => setTimeout(resolve, 1))
      lruCache.set('key2', 'value2')

      // Access key1 to make it more recently used
      await new Promise(resolve => setTimeout(resolve, 1))
      lruCache.get('key1')

      // Add key3, should evict key2 (least recently used)
      await new Promise(resolve => setTimeout(resolve, 1))
      lruCache.set('key3', 'value3')
      
      expect(lruCache.has('key1')).toBe(true)
      expect(lruCache.has('key2')).toBe(false)
      expect(lruCache.has('key3')).toBe(true)
    })

    it('should evict LFU items when cache is full', () => {
      const lfuCache = new MemoryCache<string>({
        maxSize: 2,
        ttl: 10000,
        strategy: 'lfu'
      })

      lfuCache.set('key1', 'value1')
      lfuCache.set('key2', 'value2')
      
      // Access key1 multiple times to increase frequency
      lfuCache.get('key1')
      lfuCache.get('key1')
      lfuCache.get('key2') // key2 accessed once, key1 accessed twice
      
      // Add key3, should evict key2 (least frequently used)
      lfuCache.set('key3', 'value3')
      
      expect(lfuCache.has('key1')).toBe(true)
      expect(lfuCache.has('key2')).toBe(false)
      expect(lfuCache.has('key3')).toBe(true)
    })

    it('should evict FIFO items when cache is full', () => {
      const fifoCache = new MemoryCache<string>({
        maxSize: 2,
        ttl: 10000,
        strategy: 'fifo'
      })

      fifoCache.set('key1', 'value1')
      fifoCache.set('key2', 'value2')
      
      // Add key3, should evict key1 (first in)
      fifoCache.set('key3', 'value3')
      
      expect(fifoCache.has('key1')).toBe(false)
      expect(fifoCache.has('key2')).toBe(true)
      expect(fifoCache.has('key3')).toBe(true)
    })
  })

  describe('Statistics', () => {
    it('should track hit and miss statistics', () => {
      cache.set('key1', 'value1')
      
      // Hit
      cache.get('key1')
      
      // Miss
      cache.get('nonexistent')
      
      const stats = cache.getStats()
      expect(stats.hits).toBe(1)
      expect(stats.misses).toBe(1)
      expect(stats.hitRate).toBeCloseTo(0.5)
    })

    it('should track evictions', () => {
      const smallCache = new MemoryCache<string>({
        maxSize: 1,
        ttl: 10000,
        strategy: 'lru'
      })

      smallCache.set('key1', 'value1')
      smallCache.set('key2', 'value2') // Should evict key1
      
      const stats = smallCache.getStats()
      expect(stats.evictions).toBe(1)
    })

    it('should reset statistics', () => {
      cache.set('key1', 'value1')
      cache.get('key1')
      cache.get('nonexistent')
      
      let stats = cache.getStats()
      expect(stats.hits).toBe(1)
      expect(stats.misses).toBe(1)
      
      cache.resetStats()
      
      stats = cache.getStats()
      expect(stats.hits).toBe(0)
      expect(stats.misses).toBe(0)
    })
  })
})

describe('CacheManager', () => {
  let manager: CacheManager

  beforeEach(() => {
    manager = new CacheManager()
  })

  describe('Cache Management', () => {
    it('should create and retrieve caches', () => {
      const cache = manager.getCache('test-cache')
      expect(cache).toBeInstanceOf(MemoryCache)
      
      // Should return the same instance
      const sameCache = manager.getCache('test-cache')
      expect(sameCache).toBe(cache)
    })

    it('should create caches with custom options', () => {
      const cache = manager.getCache('custom-cache', {
        maxSize: 100,
        ttl: 5000,
        strategy: 'lfu'
      })
      
      expect(cache).toBeInstanceOf(MemoryCache)
    })

    it('should list all cache names', () => {
      manager.getCache('cache1')
      manager.getCache('cache2')
      manager.getCache('cache3')
      
      const names = manager.getCacheNames()
      expect(names).toContain('cache1')
      expect(names).toContain('cache2')
      expect(names).toContain('cache3')
      expect(names).toHaveLength(3)
    })

    it('should remove caches', () => {
      const cache = manager.getCache('test-cache')
      cache.set('key1', 'value1')
      
      expect(manager.getCacheNames()).toContain('test-cache')
      
      manager.removeCache('test-cache')
      
      expect(manager.getCacheNames()).not.toContain('test-cache')
    })

    it('should clear all caches', () => {
      const cache1 = manager.getCache('cache1')
      const cache2 = manager.getCache('cache2')
      
      cache1.set('key1', 'value1')
      cache2.set('key2', 'value2')
      
      manager.clearAll()
      
      expect(cache1.size()).toBe(0)
      expect(cache2.size()).toBe(0)
    })
  })

  describe('Statistics', () => {
    it('should aggregate statistics from all caches', () => {
      const cache1 = manager.getCache('cache1')
      const cache2 = manager.getCache('cache2')
      
      cache1.set('key1', 'value1')
      cache2.set('key2', 'value2')
      
      cache1.get('key1') // hit
      cache1.get('nonexistent') // miss
      cache2.get('key2') // hit
      
      const allStats = manager.getAllStats()
      
      expect(allStats.cache1.hits).toBe(1)
      expect(allStats.cache1.misses).toBe(1)
      expect(allStats.cache2.hits).toBe(1)
      expect(allStats.cache2.misses).toBe(0)
    })
  })

  describe('Cleanup', () => {
    it('should cleanup expired entries in all caches', async () => {
      const cache1 = manager.getCache('cache1', { ttl: 50 })
      const cache2 = manager.getCache('cache2', { ttl: 50 })
      
      cache1.set('key1', 'value1')
      cache2.set('key2', 'value2')
      
      expect(cache1.size()).toBe(1)
      expect(cache2.size()).toBe(1)
      
      // Wait for TTL to expire
      await new Promise(resolve => setTimeout(resolve, 60))
      
      manager.cleanupAll()
      
      expect(cache1.size()).toBe(0)
      expect(cache2.size()).toBe(0)
    })
  })
})

describe('Global Cache Manager', () => {
  it('should provide a global cache manager instance', () => {
    expect(cacheManager).toBeInstanceOf(CacheManager)
  })

  it('should maintain state across imports', () => {
    const cache = cacheManager.getCache('global-test')
    cache.set('key1', 'value1')
    
    // Simulate another import getting the same instance
    const sameCache = cacheManager.getCache('global-test')
    expect(sameCache.get('key1')).toBe('value1')
  })
})

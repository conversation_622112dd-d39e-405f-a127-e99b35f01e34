import { db } from './db'
import { nucleiScanner } from './nuclei'
import { scanEventManager } from './scan-events'
import { logger, logScanStart, logScanComplete, logScanError } from './logger'

export interface ScanJob {
  id: string
  scanId: string
  targetUrl: string
  userId: string
  assetId: string
  options: {
    severity?: string[]
    tags?: string[]
    templates?: string[]
    excludeTemplates?: string[]
    scanType?: string
    scanMode?: string
    inputType?: string
  }
}

class JobQueue {
  private isProcessing = false
  private processingInterval: NodeJS.Timeout | null = null
  private isInitialized = false

  constructor() {
    // Don't start immediately - wait for explicit initialization
    logger.queue('JobQueue constructor called')
  }

  /**
   * Initialize the job queue processing
   * This should be called once when the application starts
   */
  public initialize() {
    if (this.isInitialized) {
      logger.queue('JobQueue already initialized, skipping...')
      return
    }

    logger.queue('Initializing JobQueue...')
    this.isInitialized = true
    this.startProcessing()
  }

  private startProcessing() {
    if (this.processingInterval) {
      logger.queue('Clearing existing processing interval...')
      clearInterval(this.processingInterval)
    }

    logger.queue('Starting job queue processing (every 5 seconds)...')
    // Process jobs every 5 seconds
    this.processingInterval = setInterval(() => {
      this.processJobs()
    }, 5000)

    // Process immediately on start
    setTimeout(() => this.processJobs(), 1000)
  }

  private async processJobs() {
    if (this.isProcessing) {
      logger.queue('Job processing already in progress, skipping...')
      return
    }

    this.isProcessing = true
    logger.queue('🔄 Starting job queue processing cycle...')

    try {
      // First, recover any stuck scans
      await this.recoverStuckScans()
      // Get currently running or pending scans (to enforce global concurrency limit)
      const activeScans = await db.scan.findMany({
        where: {
          status: { in: ['RUNNING', 'PENDING'] }
        },
        select: {
          id: true,
          userId: true,
          status: true,
          targetUrl: true,
          createdAt: true
        }
      })

      logger.queue(`📊 Active scans found: ${activeScans.length}`)
      activeScans.forEach(scan => {
        logger.debug(`   - ${scan.id}: ${scan.status} (${scan.targetUrl}) - Created: ${scan.createdAt}`, {
          component: 'QUEUE',
          scanId: scan.id,
          userId: scan.userId
        })
      })

      const runningScans = activeScans.filter(s => s.status === 'RUNNING')
      const pendingScansCount = activeScans.filter(s => s.status === 'PENDING').length

      // Only consider RUNNING scans for user exclusion (users can have multiple pending scans)
      const runningUserIds = new Set(runningScans.map(s => s.userId))

      logger.queue(`📈 Running: ${runningScans.length}, Pending: ${pendingScansCount}`)

      // Only allow up to 3 concurrent RUNNING scans globally
      if (runningScans.length >= 3) {
        logger.queue('⚠️ Maximum concurrent running scans reached (3), waiting...')
        this.isProcessing = false
        return
      }

      // Find up to (3 - runningScans.length) pending scans, skipping users with running scans
      const pendingScans = await db.scan.findMany({
        where: {
          status: 'PENDING',
          userId: { notIn: Array.from(runningUserIds) }
        },
        include: {
          asset: true
        },
        orderBy: {
          createdAt: 'asc'
        },
        take: 3 - runningScans.length
      })

      logger.queue(`🎯 Eligible pending scans to process: ${pendingScans.length}`)

      if (pendingScans.length === 0) {
        logger.queue('✅ No pending scans to process')
        this.isProcessing = false
        return
      }

      // Process all eligible scans concurrently
      await Promise.all(pendingScans.map(scan => this.processScan(scan)))
    } catch (error) {
      logger.error('❌ Error processing job queue:', error instanceof Error ? error : new Error(String(error)), { component: 'QUEUE' })
    } finally {
      this.isProcessing = false
      logger.queue('🏁 Job queue processing cycle completed')
    }
  }

  private async processScan(scan: any) {
    try {
      logScanStart(scan.id, scan.targetUrl, scan.userId)

      // Emit initial status
      await scanEventManager.emitStatus(scan.id, 'RUNNING', 'Initializing scan...')

      // Perform quick Nuclei health check before scan with timeout
      console.log('🔍 Performing Nuclei health check before scan...')

      try {
        // Add timeout to health check to prevent hanging
        const healthCheckPromise = nucleiScanner.performQuickHealthCheck()
        const timeoutPromise = new Promise<{ healthy: boolean; details: string }>((_, reject) => {
          setTimeout(() => reject(new Error('Health check timeout')), 15000) // 15 second timeout
        })

        const healthCheck = await Promise.race([healthCheckPromise, timeoutPromise])

        if (!healthCheck.healthy) {
          console.warn('⚠️ Nuclei health check failed, but proceeding with scan:', healthCheck.details)
          // Don't fail the scan, just log the warning and continue
          await scanEventManager.emitStatus(scan.id, 'RUNNING', 'Health check failed, but proceeding with scan...')
        } else {
          console.log('✅ Nuclei health check passed')
        }
      } catch (error) {
        console.warn('⚠️ Nuclei health check timed out or failed, but proceeding with scan:', error instanceof Error ? error.message : 'Unknown error')
        await scanEventManager.emitStatus(scan.id, 'RUNNING', 'Health check timed out, but proceeding with scan...')
      }

      console.log('✅ Nuclei health check passed, proceeding with scan')

      logger.scan('Using real Nuclei scanner', scan.id, { action: 'NUCLEI_CHECK' })
      scanEventManager.emitProgress(scan.id, 'Using Nuclei scanner')

      const startTime = Date.now()

      // Execute scan with configuration from database
      const scanResult = await nucleiScanner.scan({
        target: scan.targetUrl,
        severity: scan.severity as string[] || ['critical', 'high', 'medium', 'low', 'info', 'unknown'],
        tags: scan.tags as string[],
        templates: scan.templates as string[],
        excludeTemplates: scan.excludeTemplates as string[],
        // Dynamic scan options from database
        scanType: scan.scanType || 'web-api',
        scanMode: scan.scanMode || 'basic',
        inputType: scan.inputType || 'single'
        // No artificial constraints - let Nuclei run with its built-in settings
      }, scan.id)

      if (scanResult.success) {
        const duration = Math.round((Date.now() - startTime) / 1000)

        // Emit completion event (vulnerabilities were already streamed in real-time)
        await scanEventManager.emitComplete(scan.id, duration)

        // Update scan with final metadata
        await db.scan.update({
          where: { id: scan.id },
          data: {
            nucleiVersion: scanResult.stats.nucleiVersion,
            templateCount: scanResult.stats.totalTemplates
          }
        })

        // Update asset last scanned
        if (scan.assetId) {
          await db.asset.update({
            where: { id: scan.assetId },
            data: {
              lastScanned: new Date()
            }
          })
        }

        logScanComplete(scan.id, duration, scanResult.stats.totalVulnerabilities)
      } else {
        const duration = Math.round((Date.now() - startTime) / 1000)

        // Emit error event
        await scanEventManager.emitStatus(scan.id, 'FAILED', scanResult.error)

        // Update scan with error
        await db.scan.update({
          where: { id: scan.id },
          data: {
            status: 'FAILED',
            completedAt: new Date(),
            duration,
            errorMessage: scanResult.error
          }
        })

        logScanError(scan.id, new Error(scanResult.error))
      }
    } catch (error) {
      const scanError = error instanceof Error ? error : new Error(String(error))
      logScanError(scan.id, scanError)

      // Emit error event
      await scanEventManager.emitStatus(scan.id, 'FAILED', scanError.message)

      // Update scan with error
      try {
        await db.scan.update({
          where: { id: scan.id },
          data: {
            status: 'FAILED',
            completedAt: new Date(),
            errorMessage: scanError.message
          }
        })
      } catch (updateError) {
        logger.error('Failed to update scan status', updateError instanceof Error ? updateError : new Error(String(updateError)), { scanId: scan.id })
      }
    }
  }

  private normalizeSeverity(severity: string): string {
    const normalized = severity.toUpperCase()
    const validSeverities = ['CRITICAL', 'HIGH', 'MEDIUM', 'LOW', 'INFO', 'UNKNOWN']
    return validSeverities.includes(normalized) ? normalized : 'UNKNOWN'
  }

  async addScanJob(job: ScanJob): Promise<void> {
    // Jobs are automatically picked up by the processing loop
    // No need to maintain a separate queue since we use the database
    logger.queue(`📝 Scan job added for ${job.targetUrl} (ID: ${job.scanId})`, { scanId: job.scanId })
    logger.debug(`   Options: ${JSON.stringify(job.options)}`, { scanId: job.scanId, component: 'QUEUE' })

    // Ensure job queue is initialized
    if (!this.isInitialized) {
      logger.warn('⚠️ Job queue not initialized, initializing now...', { component: 'QUEUE' })
      this.initialize()
    }
  }

  /**
   * Detect and recover stuck scans
   * A scan is considered stuck if it's been in RUNNING state for more than 30 minutes
   */
  async recoverStuckScans(): Promise<void> {
    try {
      const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000)

      const stuckScans = await db.scan.findMany({
        where: {
          status: 'RUNNING',
          startedAt: {
            lt: thirtyMinutesAgo
          }
        },
        select: {
          id: true,
          targetUrl: true,
          userId: true,
          startedAt: true
        }
      })

      if (stuckScans.length === 0) {
        logger.debug('No stuck scans found', { component: 'RECOVERY' })
        return
      }

      logger.warn(`Found ${stuckScans.length} stuck scans`, { component: 'RECOVERY' })

      for (const scan of stuckScans) {
        const stuckDuration = Math.round((Date.now() - (scan.startedAt?.getTime() || 0)) / 1000 / 60)
        logger.warn(`Recovering stuck scan: ${scan.id} (stuck for ${stuckDuration} minutes)`, {
          component: 'RECOVERY',
          scanId: scan.id,
          targetUrl: scan.targetUrl
        })

        // Update scan status to FAILED
        await db.scan.update({
          where: { id: scan.id },
          data: {
            status: 'FAILED',
            completedAt: new Date(),
            errorMessage: `Scan timed out after ${stuckDuration} minutes`
          }
        })

        // Emit failure event
        scanEventManager.emitStatus(scan.id, 'FAILED', `Scan timed out after ${stuckDuration} minutes`)
      }

      logger.info(`Recovered ${stuckScans.length} stuck scans`, { component: 'RECOVERY' })
    } catch (error) {
      logger.error('Error recovering stuck scans', error instanceof Error ? error : new Error(String(error)), { component: 'RECOVERY' })
    }
  }

  async getQueueStatus(): Promise<{
    pending: number
    running: number
    completed: number
    failed: number
  }> {
    const [pending, running, completed, failed] = await Promise.all([
      db.scan.count({ where: { status: 'PENDING' } }),
      db.scan.count({ where: { status: 'RUNNING' } }),
      db.scan.count({ where: { status: 'COMPLETED' } }),
      db.scan.count({ where: { status: 'FAILED' } })
    ])

    return { pending, running, completed, failed }
  }

  stop() {
    console.log('🛑 Stopping job queue...')
    if (this.processingInterval) {
      clearInterval(this.processingInterval)
      this.processingInterval = null
    }
    this.isInitialized = false
  }

  /**
   * Get detailed queue status for debugging
   */
  async getDetailedStatus() {
    const status = await this.getQueueStatus()

    // Get stuck scans (pending for more than 10 minutes)
    const stuckScans = await db.scan.findMany({
      where: {
        status: 'PENDING',
        createdAt: {
          lt: new Date(Date.now() - 10 * 60 * 1000) // 10 minutes ago
        }
      },
      select: {
        id: true,
        targetUrl: true,
        createdAt: true,
        userId: true
      }
    })

    return {
      ...status,
      stuckScans,
      isProcessing: this.isProcessing,
      isInitialized: this.isInitialized,
      hasInterval: this.processingInterval !== null
    }
  }
}

// Singleton instance
export const jobQueue = new JobQueue()

// Initialize job queue when module is imported in production
if (process.env.NODE_ENV === 'production') {
  jobQueue.initialize()
}

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('Shutting down job queue...')
  jobQueue.stop()
})

process.on('SIGINT', () => {
  console.log('Shutting down job queue...')
  jobQueue.stop()
})

/**
 * Unit tests for Logger utility
 */

import { logger } from '../logger'
import fs from 'fs'
import path from 'path'

// Mock fs module
jest.mock('fs', () => ({
  appendFileSync: jest.fn(),
  existsSync: jest.fn(),
  mkdirSync: jest.fn(),
}))

const mockFs = fs as jest.Mocked<typeof fs>

describe('Logger', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    // Reset console mocks
    jest.spyOn(console, 'log').mockImplementation()
    jest.spyOn(console, 'warn').mockImplementation()
    jest.spyOn(console, 'error').mockImplementation()
    jest.spyOn(console, 'info').mockImplementation()
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  describe('Basic Logging', () => {
    it('should log info messages to console', () => {
      const consoleSpy = jest.spyOn(console, 'log')
      
      logger.info('Test info message')
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('[INFO] Test info message')
      )
    })

    it('should log warning messages to console', () => {
      const consoleSpy = jest.spyOn(console, 'warn')
      
      logger.warn('Test warning message')
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('[WARN] Test warning message')
      )
    })

    it('should log error messages to console', () => {
      const consoleSpy = jest.spyOn(console, 'error')
      
      logger.error('Test error message', new Error('Test error'))
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Test error message - Test error')
      )
    })

    it('should log debug messages when debug is enabled', () => {
      const consoleSpy = jest.spyOn(console, 'log')
      
      logger.debug('Test debug message')
      
      // Debug should be logged in test environment
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('[DEBUG] Test debug message')
      )
    })
  })

  describe('Contextual Logging', () => {
    it('should include context in log messages', () => {
      const consoleSpy = jest.spyOn(console, 'log')
      
      logger.info('Test message with context', { userId: '123', action: 'test' })
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Test message with context')
      )
    })

    it('should handle error objects in context', () => {
      const consoleSpy = jest.spyOn(console, 'error')
      const testError = new Error('Test error')
      
      logger.error('Error occurred', testError, { component: 'test' })
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Error occurred - Test error')
      )
    })
  })

  describe('Component-specific Logging', () => {
    it('should log scanner messages', () => {
      const consoleSpy = jest.spyOn(console, 'log')
      
      logger.nuclei('Scanner started', { scanId: 'scan-123' })
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Scanner started')
      )
    })

    it('should log database messages', () => {
      const consoleSpy = jest.spyOn(console, 'log')
      
      logger.db('Query executed', { action: 'SELECT' })
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Query executed')
      )
    })

    it('should log security events', () => {
      const consoleSpy = jest.spyOn(console, 'log')

      logger.security('Suspicious activity detected', 'high', { ip: '***********' })

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Security Event: Suspicious activity detected')
      )
    })

    it('should log API requests', () => {
      const consoleSpy = jest.spyOn(console, 'log')
      
      logger.api('GET /api/scans', { action: 'GET', requestId: 'req-123' })
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('GET /api/scans')
      )
    })
  })

  describe('File Logging', () => {
    beforeEach(() => {
      // Mock file system operations
      mockFs.existsSync.mockReturnValue(true)
      mockFs.mkdirSync.mockImplementation()
      mockFs.appendFileSync.mockImplementation()
    })

    it('should write to file when file logging is enabled', () => {
      // Set environment variable to enable file logging
      process.env.LOG_FILE_ENABLED = 'true'
      process.env.LOG_FILE_PATH = '/tmp/test.log'

      // Reconfigure logger to pick up new environment variables
      ;(logger as any).configure()

      logger.info('Test file logging')
      
      expect(mockFs.appendFileSync).toHaveBeenCalledWith(
        '/tmp/test.log',
        expect.stringContaining('Test file logging'),
        'utf8'
      )
    })

    it('should create log directory if it does not exist', () => {
      mockFs.existsSync.mockReturnValue(false)
      process.env.LOG_FILE_ENABLED = 'true'
      process.env.LOG_FILE_PATH = '/tmp/logs/test.log'

      // Reconfigure logger to pick up new environment variables
      ;(logger as any).configure()

      logger.info('Test directory creation')
      
      expect(mockFs.mkdirSync).toHaveBeenCalledWith(
        '/tmp/logs',
        { recursive: true }
      )
    })
  })

  describe('Log Formatting', () => {
    it('should format messages with timestamp', () => {
      const consoleSpy = jest.spyOn(console, 'log')
      
      logger.info('Test timestamp')
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringMatching(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z \[INFO\] Test timestamp/)
      )
    })

    it('should handle different log formats', () => {
      process.env.LOG_FORMAT = 'json'
      process.env.LOG_FILE_ENABLED = 'true'
      process.env.LOG_FILE_PATH = '/tmp/test.log'

      // Reconfigure logger to pick up new environment variables
      ;(logger as any).configure()

      logger.info('Test JSON format', { action: 'test' })
      
      expect(mockFs.appendFileSync).toHaveBeenCalledWith(
        '/tmp/test.log',
        expect.stringContaining('"message":"Test JSON format"'),
        'utf8'
      )
    })
  })

  describe('Error Handling', () => {
    it('should handle file write errors gracefully', () => {
      mockFs.appendFileSync.mockImplementation(() => {
        throw new Error('File write error')
      })

      process.env.LOG_FILE_ENABLED = 'true'
      process.env.LOG_FILE_PATH = '/tmp/test.log'

      // Reconfigure logger to pick up new environment variables
      ;(logger as any).configure()

      // Should not throw an error
      expect(() => {
        logger.info('Test error handling')
      }).not.toThrow()
    })

    it('should handle missing context gracefully', () => {
      const consoleSpy = jest.spyOn(console, 'log')
      
      logger.info('Test without context')
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('[INFO] Test without context')
      )
    })
  })
})

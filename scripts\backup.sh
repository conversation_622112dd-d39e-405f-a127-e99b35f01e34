#!/bin/bash

# CTB Scanner Backup Script
# Automated backup system for database, logs, and configuration

set -euo pipefail

# =============================================================================
# Configuration
# =============================================================================
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
BACKUP_BASE_DIR="/opt/ctb-scanner/backups"
LOG_FILE="/var/log/ctb-scanner/backup.log"

# Backup retention (days)
RETENTION_DAYS=30

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# =============================================================================
# Logging Functions
# =============================================================================
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a "$LOG_FILE"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}" | tee -a "$LOG_FILE"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a "$LOG_FILE"
}

# =============================================================================
# Utility Functions
# =============================================================================
create_backup_directory() {
    local backup_date=$(date +%Y%m%d_%H%M%S)
    BACKUP_DIR="$BACKUP_BASE_DIR/$backup_date"
    
    mkdir -p "$BACKUP_DIR"/{database,logs,config,volumes}
    log "Created backup directory: $BACKUP_DIR"
}

load_environment() {
    if [[ -f "$PROJECT_DIR/.env" ]]; then
        set -a
        source "$PROJECT_DIR/.env"
        set +a
        log "Environment variables loaded."
    else
        error ".env file not found. Cannot proceed with backup."
    fi
}

backup_database() {
    log "Starting database backup..."
    
    local db_backup_file="$BACKUP_DIR/database/ctb_scanner.sql"
    
    # Check if MySQL container is running
    if ! docker-compose -f "$PROJECT_DIR/docker-compose.yml" ps mysql | grep -q "Up"; then
        warn "MySQL container is not running. Skipping database backup."
        return
    fi
    
    # Create database dump
    docker-compose -f "$PROJECT_DIR/docker-compose.yml" exec -T mysql mysqldump \
        -u root -p"${MYSQL_ROOT_PASSWORD}" \
        --single-transaction \
        --routines \
        --triggers \
        --add-drop-database \
        --databases ctb_scanner > "$db_backup_file"
    
    if [[ $? -eq 0 ]]; then
        # Compress the backup
        gzip "$db_backup_file"
        log "Database backup completed: ${db_backup_file}.gz"
        
        # Get backup size
        local backup_size=$(du -h "${db_backup_file}.gz" | cut -f1)
        info "Database backup size: $backup_size"
    else
        error "Database backup failed."
    fi
}

backup_logs() {
    log "Starting logs backup..."
    
    local logs_backup_dir="$BACKUP_DIR/logs"
    
    # Copy application logs
    if [[ -d "/var/log/ctb-scanner" ]]; then
        cp -r /var/log/ctb-scanner/* "$logs_backup_dir/" 2>/dev/null || true
        log "Application logs backed up."
    fi
    
    # Copy Docker container logs
    local containers=("ctb-scanner-app" "ctb-scanner-mysql" "ctb-scanner-redis" "ctb-scanner-nginx")
    
    for container in "${containers[@]}"; do
        if docker ps --format "table {{.Names}}" | grep -q "$container"; then
            docker logs "$container" > "$logs_backup_dir/${container}.log" 2>&1
            info "Backed up logs for container: $container"
        fi
    done
    
    # Compress logs
    tar -czf "$logs_backup_dir.tar.gz" -C "$BACKUP_DIR" logs
    rm -rf "$logs_backup_dir"
    
    log "Logs backup completed: ${logs_backup_dir}.tar.gz"
}

backup_configuration() {
    log "Starting configuration backup..."
    
    local config_backup_dir="$BACKUP_DIR/config"
    
    # Copy important configuration files
    local config_files=(
        "$PROJECT_DIR/.env"
        "$PROJECT_DIR/docker-compose.yml"
        "$PROJECT_DIR/nginx/nginx.conf"
        "$PROJECT_DIR/prisma/schema.prisma"
        "$PROJECT_DIR/package.json"
        "$PROJECT_DIR/package-lock.json"
    )
    
    for file in "${config_files[@]}"; do
        if [[ -f "$file" ]]; then
            cp "$file" "$config_backup_dir/"
            info "Backed up: $(basename "$file")"
        fi
    done
    
    # Copy nginx configuration directory
    if [[ -d "$PROJECT_DIR/nginx" ]]; then
        cp -r "$PROJECT_DIR/nginx" "$config_backup_dir/"
        info "Backed up nginx configuration."
    fi
    
    # Copy SSL certificates if they exist
    if [[ -d "/opt/ctb-scanner/ssl" ]]; then
        cp -r /opt/ctb-scanner/ssl "$config_backup_dir/"
        info "Backed up SSL certificates."
    fi
    
    log "Configuration backup completed."
}

backup_docker_volumes() {
    log "Starting Docker volumes backup..."
    
    local volumes_backup_dir="$BACKUP_DIR/volumes"
    
    # Get list of CTB Scanner volumes
    local volumes=$(docker volume ls --format "table {{.Name}}" | grep "ctb-scanner" || true)
    
    if [[ -n "$volumes" ]]; then
        while IFS= read -r volume; do
            if [[ -n "$volume" ]]; then
                info "Backing up volume: $volume"
                
                # Create a temporary container to access the volume
                docker run --rm \
                    -v "$volume":/source:ro \
                    -v "$volumes_backup_dir":/backup \
                    alpine:latest \
                    tar -czf "/backup/${volume}.tar.gz" -C /source .
                
                info "Volume backup completed: ${volume}.tar.gz"
            fi
        done <<< "$volumes"
    else
        info "No CTB Scanner volumes found to backup."
    fi
    
    log "Docker volumes backup completed."
}

create_backup_manifest() {
    log "Creating backup manifest..."
    
    local manifest_file="$BACKUP_DIR/backup_manifest.json"
    
    cat > "$manifest_file" << EOF
{
    "backup_date": "$(date -Iseconds)",
    "backup_type": "full",
    "application": "CTB Scanner",
    "version": "$(git -C "$PROJECT_DIR" describe --tags --always 2>/dev/null || echo 'unknown')",
    "components": {
        "database": $([ -f "$BACKUP_DIR/database/ctb_scanner.sql.gz" ] && echo "true" || echo "false"),
        "logs": $([ -f "$BACKUP_DIR/logs.tar.gz" ] && echo "true" || echo "false"),
        "configuration": $([ -d "$BACKUP_DIR/config" ] && echo "true" || echo "false"),
        "volumes": $([ -d "$BACKUP_DIR/volumes" ] && echo "true" || echo "false")
    },
    "backup_size": "$(du -sh "$BACKUP_DIR" | cut -f1)",
    "retention_until": "$(date -d "+$RETENTION_DAYS days" -Iseconds)"
}
EOF
    
    log "Backup manifest created: $manifest_file"
}

cleanup_old_backups() {
    log "Cleaning up old backups..."
    
    # Find and remove backups older than retention period
    find "$BACKUP_BASE_DIR" -maxdepth 1 -type d -name "20*" -mtime +$RETENTION_DAYS -exec rm -rf {} \; 2>/dev/null || true
    
    # Count remaining backups
    local backup_count=$(find "$BACKUP_BASE_DIR" -maxdepth 1 -type d -name "20*" | wc -l)
    log "Cleanup completed. $backup_count backups retained."
}

verify_backup() {
    log "Verifying backup integrity..."
    
    local errors=0
    
    # Check database backup
    if [[ -f "$BACKUP_DIR/database/ctb_scanner.sql.gz" ]]; then
        if gzip -t "$BACKUP_DIR/database/ctb_scanner.sql.gz"; then
            info "Database backup integrity: OK"
        else
            error "Database backup is corrupted!"
            ((errors++))
        fi
    fi
    
    # Check logs backup
    if [[ -f "$BACKUP_DIR/logs.tar.gz" ]]; then
        if tar -tzf "$BACKUP_DIR/logs.tar.gz" >/dev/null 2>&1; then
            info "Logs backup integrity: OK"
        else
            warn "Logs backup may be corrupted."
            ((errors++))
        fi
    fi
    
    # Check volume backups
    if [[ -d "$BACKUP_DIR/volumes" ]]; then
        for volume_backup in "$BACKUP_DIR/volumes"/*.tar.gz; do
            if [[ -f "$volume_backup" ]]; then
                if tar -tzf "$volume_backup" >/dev/null 2>&1; then
                    info "Volume backup integrity: OK - $(basename "$volume_backup")"
                else
                    warn "Volume backup may be corrupted: $(basename "$volume_backup")"
                    ((errors++))
                fi
            fi
        done
    fi
    
    if [[ $errors -eq 0 ]]; then
        log "Backup verification completed successfully."
    else
        warn "Backup verification completed with $errors warnings."
    fi
}

send_notification() {
    local status="$1"
    local message="$2"
    
    # Log the notification
    if [[ "$status" == "success" ]]; then
        log "$message"
    else
        error "$message"
    fi
    
    # Send email notification if configured
    if [[ -n "${BACKUP_EMAIL:-}" ]] && command -v mail >/dev/null 2>&1; then
        echo "$message" | mail -s "CTB Scanner Backup $status" "$BACKUP_EMAIL"
    fi
    
    # Send Slack notification if webhook is configured
    if [[ -n "${SLACK_WEBHOOK_URL:-}" ]] && command -v curl >/dev/null 2>&1; then
        local emoji="✅"
        [[ "$status" != "success" ]] && emoji="❌"
        
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"$emoji CTB Scanner Backup $status: $message\"}" \
            "$SLACK_WEBHOOK_URL" >/dev/null 2>&1 || true
    fi
}

# =============================================================================
# Main Backup Function
# =============================================================================
main() {
    log "Starting CTB Scanner backup process..."
    
    # Create log directory if it doesn't exist
    mkdir -p "$(dirname "$LOG_FILE")"
    
    # Ensure backup base directory exists
    mkdir -p "$BACKUP_BASE_DIR"
    
    cd "$PROJECT_DIR"
    
    load_environment
    create_backup_directory
    
    # Perform backup operations
    backup_database
    backup_logs
    backup_configuration
    backup_docker_volumes
    create_backup_manifest
    verify_backup
    cleanup_old_backups
    
    # Calculate total backup size
    local total_size=$(du -sh "$BACKUP_DIR" | cut -f1)
    
    send_notification "success" "CTB Scanner backup completed successfully. Backup size: $total_size, Location: $BACKUP_DIR"
    
    log "CTB Scanner backup process completed successfully!"
    log "Backup location: $BACKUP_DIR"
    log "Backup size: $total_size"
}

# =============================================================================
# Script Execution
# =============================================================================
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

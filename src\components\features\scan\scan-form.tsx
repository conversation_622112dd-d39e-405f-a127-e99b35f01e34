import React, { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { scanUrlSchema, type ScanUrlInput } from '@/lib/validations'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { SeveritySelector } from './severity-selector'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import {
  Globe,
  Zap,
  Network,
  Code,
  FileText,
  Layers,
  Target,
  List,
  Shield,
  Clock,
  Cpu
} from 'lucide-react'

interface ScanFormProps {
  onSubmit: (data: ScanUrlInput) => Promise<void>
  isLoading?: boolean
  error?: string | null
  onReset?: () => void
  showResetButton?: boolean
  className?: string
}

export const ScanForm: React.FC<ScanFormProps> = ({
  onSubmit,
  isLoading = false,
  error,
  onReset,
  showResetButton = false,
  className,
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
  } = useForm<ScanUrlInput>({
    resolver: zodResolver(scanUrlSchema),
    defaultValues: {
      severity: ['critical', 'high', 'medium', 'low', 'info', 'unknown']
    }
  })

  const severityValue = watch('severity') || []

  const handleFormSubmit = async (data: ScanUrlInput) => {
    try {
      await onSubmit(data)
    } catch (err) {
      // Error handling is done by parent component
    }
  }

  const handleReset = () => {
    reset()
    onReset?.()
  }

  return (
    <div className={className}>
      {error && (
        <Alert variant="error" className="mb-6">
          {error}
        </Alert>
      )}

      <Card className="shadow-xl border-0 bg-white">
        <CardContent className="p-8">
          <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-8">
            {/* URL Input */}
            <div className="space-y-3">
              <Label htmlFor="url" className="text-base font-semibold text-gray-900 flex items-center">
                <Globe className="h-5 w-5 mr-2 text-blue-600" />
                Website URL
              </Label>
              <Input
                id="url"
                type="url"
                placeholder="https://example.com"
                {...register('url')}
                className={`h-14 text-lg border-2 rounded-xl transition-all duration-200 ${
                  errors.url
                    ? 'border-red-300 focus:border-red-500 focus:ring-red-200'
                    : 'border-gray-200 focus:border-blue-500 focus:ring-blue-200'
                } ${isLoading ? 'bg-gray-50 cursor-not-allowed' : 'bg-white'}`}
                disabled={isLoading}
              />
              {errors.url && (
                <p className="mt-2 text-sm text-red-600 flex items-center">
                  <span className="w-1 h-1 bg-red-600 rounded-full mr-2"></span>
                  {errors.url.message}
                </p>
              )}
            </div>

            {/* Severity Selection */}
            <div className="space-y-3">
              <Label className="text-base font-semibold text-gray-900">
                Severity Levels
              </Label>
              <SeveritySelector
                value={severityValue}
                onChange={(value) => setValue('severity', value)}
                disabled={isLoading}
              />
              {errors.severity && (
                <p className="mt-2 text-sm text-red-600 flex items-center">
                  <span className="w-1 h-1 bg-red-600 rounded-full mr-2"></span>
                  {errors.severity.message}
                </p>
              )}
            </div>

            {/* Submit Button */}
            <div className="pt-4">
              <Button
                type="submit"
                disabled={isLoading}
                className="w-full h-14 text-lg font-semibold bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 border-0 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
              >
                {isLoading ? (
                  <>
                    <LoadingSpinner size="sm" className="mr-3" />
                    Scanning...
                  </>
                ) : (
                  <>
                    <Zap className="h-5 w-5 mr-3" />
                    Start Security Scan
                  </>
                )}
              </Button>

              {showResetButton && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleReset}
                  className="w-full h-12 mt-4 border-2 border-gray-200 hover:border-gray-300 text-gray-700 font-medium rounded-xl transition-all duration-200"
                >
                  New Scan
                </Button>
              )}
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}

import * as React from 'react'
import { cn } from '@/lib/utils'

export interface TextProps extends React.HTMLAttributes<HTMLParagraphElement> {
  variant?: 'body' | 'caption' | 'small' | 'muted'
  weight?: 'normal' | 'medium' | 'semibold' | 'bold'
  color?: 'default' | 'muted' | 'success' | 'warning' | 'error'
  as?: 'p' | 'span' | 'div'
}

const textVariants = {
  body: 'text-base',
  caption: 'text-sm',
  small: 'text-xs',
  muted: 'text-sm text-gray-500'
}

const textWeights = {
  normal: 'font-normal',
  medium: 'font-medium',
  semibold: 'font-semibold',
  bold: 'font-bold'
}

const textColors = {
  default: 'text-gray-900',
  muted: 'text-gray-500',
  success: 'text-green-600',
  warning: 'text-yellow-600',
  error: 'text-red-600'
}

export const Text = React.forwardRef<HTMLParagraphElement, TextProps>(
  ({ 
    className, 
    variant = 'body', 
    weight = 'normal',
    color = 'default',
    as: Component = 'p',
    ...props 
  }, ref) => {
    return (
      <Component
        ref={ref}
        className={cn(
          textVariants[variant],
          textWeights[weight],
          textColors[color],
          className
        )}
        {...props}
      />
    )
  }
)

Text.displayName = 'Text'

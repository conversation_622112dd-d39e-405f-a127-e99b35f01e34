import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { scanUrlSchema, type ScanUrlInput } from '@/lib/validations'
import { Card, CardHeader, CardContent } from '../molecules/card'
import { FormField } from '../molecules/form-field'
import { Button } from '../atoms/button'
import { Select } from '../atoms/select'
import { Alert } from '../molecules/alert'
import { Badge } from '../atoms/badge'
import { Heading } from '../atoms/heading'
import { Text } from '../atoms/text'
import { Globe, Zap, Network, Code, FileText, Layers } from 'lucide-react'

export interface ScanFormProps {
  onSubmit: (data: ScanUrlInput) => Promise<void>
  isLoading?: boolean
  error?: string | null
  onReset?: () => void
  showResetButton?: boolean
  className?: string
}

const scanTypeOptions = [
  { value: 'quick', label: 'Quick Scan' },
  { value: 'deep', label: 'Deep Scan' },
  { value: 'custom', label: 'Custom Scan' }
]

const severityOptions = [
  { value: 'info,low,medium,high,critical', label: 'All Severities' },
  { value: 'low,medium,high,critical', label: 'Low to Critical' },
  { value: 'medium,high,critical', label: 'Medium to Critical' },
  { value: 'high,critical', label: 'High & Critical Only' },
  { value: 'critical', label: 'Critical Only' }
]

const scanTypeConfig = {
  quick: {
    icon: Zap,
    description: 'Fast scan with essential vulnerability checks',
    estimatedTime: '2-5 minutes',
    color: 'success'
  },
  deep: {
    icon: Layers,
    description: 'Comprehensive scan with all available templates',
    estimatedTime: '10-30 minutes',
    color: 'warning'
  },
  custom: {
    icon: Code,
    description: 'Customizable scan with specific templates',
    estimatedTime: 'Variable',
    color: 'default'
  }
}

export const ScanForm: React.FC<ScanFormProps> = ({
  onSubmit,
  isLoading = false,
  error,
  onReset,
  showResetButton = false,
  className,
}) => {
  const [selectedScanType, setSelectedScanType] = useState<string>('quick')

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
  } = useForm<ScanUrlInput>({
    resolver: zodResolver(scanUrlSchema),
    defaultValues: {
      url: '',
      scanType: 'quick',
      severity: 'info,low,medium,high,critical',
    },
  })

  const watchedScanType = watch('scanType')

  React.useEffect(() => {
    setSelectedScanType(watchedScanType || 'quick')
  }, [watchedScanType])

  const handleFormSubmit = async (data: ScanUrlInput) => {
    try {
      await onSubmit(data)
    } catch (err) {
      console.error('Form submission error:', err)
    }
  }

  const handleReset = () => {
    reset()
    onReset?.()
  }

  const currentConfig = scanTypeConfig[selectedScanType as keyof typeof scanTypeConfig]

  return (
    <Card className={className}>
      <CardHeader
        title="Start Vulnerability Scan"
        subtitle="Enter a URL to scan for security vulnerabilities"
      />
      
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {error && (
            <Alert variant="error" dismissible>
              {error}
            </Alert>
          )}

          {/* URL Input */}
          <FormField
            label="Target URL"
            required
            placeholder="https://example.com"
            error={errors.url?.message}
            leftIcon={<Globe className="w-4 h-4" />}
            {...register('url')}
          />

          {/* Scan Type Selection */}
          <FormField
            label="Scan Type"
            required
            error={errors.scanType?.message}
          >
            <Select
              options={scanTypeOptions}
              value={selectedScanType}
              onChange={(value) => {
                setValue('scanType', value as any)
                setSelectedScanType(value)
              }}
              fullWidth
            />
          </FormField>

          {/* Scan Type Info */}
          {currentConfig && (
            <div className="p-4 bg-gray-50 rounded-lg border">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <currentConfig.icon className="w-5 h-5 text-gray-600 mt-0.5" />
                </div>
                <div className="flex-1">
                  <Text variant="body" weight="medium" className="mb-1">
                    {scanTypeOptions.find(opt => opt.value === selectedScanType)?.label}
                  </Text>
                  <Text variant="caption" color="muted" className="mb-2">
                    {currentConfig.description}
                  </Text>
                  <Badge variant={currentConfig.color as any} size="sm">
                    Est. {currentConfig.estimatedTime}
                  </Badge>
                </div>
              </div>
            </div>
          )}

          {/* Severity Filter */}
          <FormField
            label="Severity Filter"
            hint="Select which vulnerability severities to include in the scan"
            error={errors.severity?.message}
          >
            <Select
              options={severityOptions}
              {...register('severity')}
              fullWidth
            />
          </FormField>

          {/* Action Buttons */}
          <div className="flex items-center justify-between pt-4">
            <div>
              {showResetButton && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleReset}
                  disabled={isLoading}
                >
                  Reset Form
                </Button>
              )}
            </div>
            
            <Button
              type="submit"
              loading={isLoading}
              leftIcon={!isLoading ? <Network className="w-4 h-4" /> : undefined}
              className="min-w-[120px]"
            >
              {isLoading ? 'Starting Scan...' : 'Start Scan'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

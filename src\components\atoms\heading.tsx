import * as React from 'react'
import { cn } from '@/lib/utils'

export interface HeadingProps extends React.HTMLAttributes<HTMLHeadingElement> {
  level?: 1 | 2 | 3 | 4 | 5 | 6
  weight?: 'normal' | 'medium' | 'semibold' | 'bold'
  color?: 'default' | 'muted' | 'primary'
}

const headingLevels = {
  1: 'text-4xl',
  2: 'text-3xl',
  3: 'text-2xl',
  4: 'text-xl',
  5: 'text-lg',
  6: 'text-base'
}

const headingWeights = {
  normal: 'font-normal',
  medium: 'font-medium',
  semibold: 'font-semibold',
  bold: 'font-bold'
}

const headingColors = {
  default: 'text-gray-900',
  muted: 'text-gray-600',
  primary: 'text-blue-600'
}

export const Heading = React.forwardRef<HTMLHeadingElement, HeadingProps>(
  ({ 
    className, 
    level = 2, 
    weight = 'semibold',
    color = 'default',
    ...props 
  }, ref) => {
    const Component = `h${level}` as keyof JSX.IntrinsicElements

    return (
      <Component
        ref={ref}
        className={cn(
          headingLevels[level],
          headingWeights[weight],
          headingColors[color],
          'leading-tight',
          className
        )}
        {...props}
      />
    )
  }
)

Heading.displayName = 'Heading'

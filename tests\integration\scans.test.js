// Integration tests use shared setup from tests/setup/integration.setup.js
const { testRequest, createTestUser } = require('../setup/integration.setup')



describe('Scans API', () => {
  // Helper function to create a user and get auth cookie for each test
  const createUserForTest = async () => {
    const { authCookie } = await createTestUser({
      email: `scan-test-${Date.now()}-${Math.random()}@example.com`
    })
    return authCookie
  }

  describe('POST /api/scans', () => {
    it('should create a new scan successfully', async () => {
      const authCookie = await createUserForTest()

      const scanData = {
        url: 'https://example.com',
        scanType: 'quick',
        severity: 'info,low,medium,high,critical'
      }

      const response = await testRequest()
        .post('/api/scans')
        .set('Cookie', authCookie)
        .send(scanData)
        .expect(201)

      expect(response.body).toHaveProperty('message', '<PERSON><PERSON> initiated successfully')
      expect(response.body).toHaveProperty('scanId')
      expect(response.body).toHaveProperty('status', 'PENDING')
    })

    it('should reject invalid URL', async () => {
      const authCookie = await createUserForTest()

      const scanData = {
        url: 'invalid-url',
        scanType: 'quick',
        severity: 'high,critical'
      }

      const response = await testRequest()
        .post('/api/scans')
        .set('Cookie', authCookie)
        .send(scanData)
        .expect(400)

      expect(response.body).toHaveProperty('error')
    })

    it('should reject private IP addresses', async () => {
      const authCookie = await createUserForTest()

      const scanData = {
        url: 'http://***********',
        scanType: 'quick',
        severity: 'high,critical'
      }

      const response = await testRequest()
        .post('/api/scans')
        .set('Cookie', authCookie)
        .send(scanData)
        .expect(400)

      expect(response.body).toHaveProperty('error')
      expect(response.body.error).toContain('private')
    })

    it('should reject localhost URLs', async () => {
      const authCookie = await createUserForTest()

      const scanData = {
        url: 'http://localhost:3000',
        scanType: 'quick',
        severity: 'high,critical'
      }

      const response = await testRequest()
        .post('/api/scans')
        .set('Cookie', authCookie)
        .send(scanData)
        .expect(400)

      expect(response.body).toHaveProperty('error')
    })

    it('should require authentication', async () => {
      const scanData = {
        url: 'https://example.com',
        scanType: 'quick',
        severity: 'high,critical'
      }

      const response = await testRequest()
        .post('/api/scans')
        .send(scanData)
        .expect(401)

      expect(response.body).toHaveProperty('error')
    })

    it('should validate scan type', async () => {
      const authCookie = await createUserForTest()

      const scanData = {
        url: 'https://example.com',
        scanType: 'invalid-type',
        severity: 'high,critical'
      }

      const response = await testRequest()
        .post('/api/scans')
        .set('Cookie', authCookie)
        .send(scanData)
        .expect(400)

      expect(response.body).toHaveProperty('error')
    })

    it('should validate severity levels', async () => {
      const authCookie = await createUserForTest()

      const scanData = {
        url: 'https://example.com',
        scanType: 'quick',
        severity: 'invalid-severity'
      }

      const response = await testRequest()
        .post('/api/scans')
        .set('Cookie', authCookie)
        .send(scanData)
        .expect(400)

      expect(response.body).toHaveProperty('error')
    })
  })

  describe('GET /api/scans', () => {
    let authCookie

    beforeAll(async () => {
      // Create user and get auth cookie once for all tests in this block
      authCookie = await createUserForTest()
    })

    beforeEach(async () => {
      // Create a test scan for each test
      await testRequest()
        .post('/api/scans')
        .set('Cookie', authCookie)
        .send({
          url: 'https://test-scan.com',
          scanType: 'quick',
          severity: 'high,critical'
        })
    })

    it('should return user scans with pagination', async () => {
      const response = await testRequest()
        .get('/api/scans')
        .set('Cookie', authCookie)
        .expect(200)

      expect(response.body).toHaveProperty('scans')
      expect(response.body).toHaveProperty('pagination')
      expect(response.body.pagination).toHaveProperty('page')
      expect(response.body.pagination).toHaveProperty('pages')
      expect(response.body.pagination).toHaveProperty('total')
      expect(response.body.pagination).toHaveProperty('limit')
      expect(Array.isArray(response.body.scans)).toBe(true)
    })

    it('should support pagination parameters', async () => {
      const response = await testRequest()
        .get('/api/scans?page=1&limit=5')
        .set('Cookie', authCookie)
        .expect(200)

      expect(response.body.pagination.page).toBe(1)
      expect(response.body.pagination.limit).toBe(5)
    })

    it('should support status filtering', async () => {
      const response = await testRequest()
        .get('/api/scans?status=PENDING')
        .set('Cookie', authCookie)
        .expect(200)

      expect(response.body).toHaveProperty('scans')
      // All returned scans should have PENDING status
      response.body.scans.forEach(scan => {
        expect(scan.status).toBe('PENDING')
      })
    })

    it('should require authentication', async () => {
      const response = await testRequest()
        .get('/api/scans')
        .expect(401)

      expect(response.body).toHaveProperty('error')
    })

    it('should only return user own scans', async () => {
      // Create another user
      const { authCookie: anotherAuthCookie } = await createTestUser({
        email: '<EMAIL>'
      })

      // Get scans for the new user (should be empty)
      const response = await testRequest()
        .get('/api/scans')
        .set('Cookie', anotherAuthCookie)
        .expect(200)

      expect(response.body.scans).toHaveLength(0)
      expect(response.body.pagination.total).toBe(0)
    })
  })

  describe('GET /api/scans/[id]', () => {
    let scanId, authCookie

    beforeAll(async () => {
      // Create user and get auth cookie once for all tests in this block
      authCookie = await createUserForTest()
    })

    beforeEach(async () => {
      // Create a test scan for each test
      const scanResponse = await testRequest()
        .post('/api/scans')
        .set('Cookie', authCookie)
        .send({
          url: 'https://detail-test.com',
          scanType: 'quick',
          severity: 'high,critical'
        })

      scanId = scanResponse.body.scanId
    })

    it('should return scan details', async () => {
      const response = await testRequest()
        .get(`/api/scans/${scanId}`)
        .set('Cookie', authCookie)
        .expect(200)

      expect(response.body).toHaveProperty('scan')
      expect(response.body.scan).toHaveProperty('id', scanId)
      expect(response.body.scan).toHaveProperty('targetUrl', 'https://detail-test.com')
      expect(response.body.scan).toHaveProperty('status')
      expect(response.body.scan).toHaveProperty('asset')
      expect(response.body.scan).toHaveProperty('vulnerabilities')
    })

    it('should return 404 for non-existent scan', async () => {
      const response = await testRequest()
        .get('/api/scans/non-existent-id')
        .set('Cookie', authCookie)
        .expect(404)

      expect(response.body).toHaveProperty('error')
    })

    it('should require authentication', async () => {
      const response = await testRequest()
        .get(`/api/scans/${scanId}`)
        .expect(401)

      expect(response.body).toHaveProperty('error')
    })

    it('should not allow access to other users scans', async () => {
      // Create another user
      const { authCookie: anotherAuthCookie } = await createTestUser({
        email: '<EMAIL>'
      })

      // Try to access the scan with different user
      const response = await testRequest()
        .get(`/api/scans/${scanId}`)
        .set('Cookie', anotherAuthCookie)
        .expect(403)

      expect(response.body).toHaveProperty('error')
    })
  })
})

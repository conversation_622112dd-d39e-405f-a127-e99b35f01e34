# CTB Scanner - Troubleshooting Guide

## Overview

This guide provides solutions to common issues encountered when running CTB Scanner in development and production environments.

## Quick Diagnostics

### Health Check Commands
```bash
# Check application status
curl http://localhost:3000/api/health

# Check database connection
npm run db:studio

# Check Nuclei installation
nuclei -version

# Check Node.js version
node --version

# Check running processes
pm2 status  # Production
npm run dev # Development
```

## Common Issues

### 1. Application Won't Start

#### Symptoms
- Server fails to start
- Port already in use error
- Module not found errors

#### Solutions

**Port Already in Use**
```bash
# Find process using port 3000
# Windows
netstat -ano | findstr :3000
taskkill /PID <PID> /F

# macOS/Linux
lsof -i :3000
kill -9 <PID>

# Or use different port
PORT=3001 npm run dev
```

**Missing Dependencies**
```bash
# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm cache clean --force
npm install
```

**Environment Variables**
```bash
# Check .env file exists
ls -la .env

# Verify required variables
cat .env | grep -E "(DATABASE_URL|JWT_SECRET|NEXTAUTH_SECRET)"

# Copy from template if missing
cp .env.example .env
```

### 2. Database Connection Issues

#### Symptoms
- "Can't connect to MySQL server"
- "Access denied for user"
- Prisma client errors

#### Solutions

**MySQL Service Not Running**
```bash
# Windows
net start mysql80

# macOS
brew services start mysql

# Linux
sudo systemctl start mysql

# Docker
docker start mysql-ctb
```

**Wrong Credentials**
```bash
# Test connection manually
mysql -u root -p -h localhost -P 3306

# Check DATABASE_URL format
# mysql://username:password@host:port/database
DATABASE_URL="mysql://root:rootroot@localhost:3306/ctb_scanner"
```

**Database Doesn't Exist**
```bash
# Create database
mysql -u root -p
CREATE DATABASE ctb_scanner CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
EXIT;

# Run migrations
npx prisma db push
```

**Prisma Client Issues**
```bash
# Regenerate Prisma client
npx prisma generate

# Reset database (development only)
npx prisma db reset

# Check schema
npx prisma db pull
```

### 3. Nuclei Scanner Issues

#### Symptoms
- "Nuclei binary not found"
- Scans fail to start
- No vulnerabilities detected

#### Solutions

**Nuclei Not Installed**
```bash
# Check installation
nuclei -version

# Install Nuclei
# Windows (PowerShell)
Invoke-WebRequest -Uri "https://github.com/projectdiscovery/nuclei/releases/latest/download/nuclei_2.9.15_windows_amd64.zip" -OutFile "nuclei.zip"
Expand-Archive -Path "nuclei.zip" -DestinationPath "C:\tools\nuclei"

# macOS
brew install nuclei

# Linux
wget https://github.com/projectdiscovery/nuclei/releases/latest/download/nuclei_2.9.15_linux_amd64.zip
unzip nuclei_2.9.15_linux_amd64.zip
sudo mv nuclei /usr/local/bin/
```

**Templates Not Updated**
```bash
# Update templates
nuclei -update-templates

# Check templates directory
# Windows: %USERPROFILE%\.nuclei-templates
# macOS/Linux: ~/.nuclei-templates
ls ~/.nuclei-templates

# Force template update
nuclei -update-templates -update-template-dir ~/.nuclei-templates
```

**Path Issues**
```bash
# Check PATH
echo $PATH  # macOS/Linux
echo $env:PATH  # Windows PowerShell

# Set NUCLEI_PATH in .env
NUCLEI_PATH="/usr/local/bin/nuclei"  # Linux/macOS
NUCLEI_PATH="C:\tools\nuclei\nuclei.exe"  # Windows
```

**Permission Issues (Linux/macOS)**
```bash
# Make Nuclei executable
sudo chmod +x /usr/local/bin/nuclei

# Check file permissions
ls -la /usr/local/bin/nuclei
```

### 4. Authentication Issues

#### Symptoms
- Login fails
- JWT token errors
- Session not persisting

#### Solutions

**JWT Secret Issues**
```bash
# Check JWT_SECRET in .env
grep JWT_SECRET .env

# Generate new secret
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"

# Update .env
JWT_SECRET="your-new-secret-here"
```

**NextAuth Configuration**
```bash
# Check NextAuth variables
grep NEXTAUTH .env

# Ensure correct URL
NEXTAUTH_URL="http://localhost:3000"  # Development
NEXTAUTH_URL="https://your-domain.com"  # Production
```

**Password Hashing Issues**
```bash
# Check bcryptjs installation
npm list bcryptjs

# Reinstall if needed
npm install bcryptjs
npm install -D @types/bcryptjs
```

### 5. Build and Deployment Issues

#### Symptoms
- Build fails
- TypeScript errors
- Missing files in production

#### Solutions

**TypeScript Errors**
```bash
# Check TypeScript configuration
npm run type-check

# Fix common issues
npx prisma generate  # Regenerate types
rm -rf .next  # Clear Next.js cache
npm run build
```

**Missing Environment Variables**
```bash
# Check production environment
cat .env.production

# Verify all required variables are set
NODE_ENV="production"
DATABASE_URL="mysql://..."
JWT_SECRET="..."
NEXTAUTH_URL="https://..."
```

**Build Optimization Issues**
```bash
# Clear all caches
rm -rf .next node_modules package-lock.json
npm cache clean --force
npm install
npm run build
```

### 6. Performance Issues

#### Symptoms
- Slow page loads
- High memory usage
- Database timeouts

#### Solutions

**Database Performance**
```sql
-- Add indexes for common queries
CREATE INDEX idx_scans_created_at ON scans(created_at);
CREATE INDEX idx_vulnerabilities_severity ON vulnerabilities(severity);
CREATE INDEX idx_assets_domain ON assets(domain);

-- Check slow queries
SHOW PROCESSLIST;
```

**Memory Issues**
```bash
# Check memory usage
# Linux/macOS
free -h
top

# Windows
tasklist /fi "imagename eq node.exe"

# Restart application
pm2 restart ctb-scanner  # Production
# Or restart dev server
```

**Connection Pool Issues**
```bash
# Increase connection pool size in .env
DATABASE_POOL_MIN=5
DATABASE_POOL_MAX=20
DATABASE_POOL_TIMEOUT=60000
```

### 7. SSL/HTTPS Issues (Production)

#### Symptoms
- SSL certificate errors
- Mixed content warnings
- HTTPS redirect loops

#### Solutions

**Certificate Issues**
```bash
# Check certificate status
sudo certbot certificates

# Renew certificate
sudo certbot renew

# Test certificate
openssl s_client -connect your-domain.com:443
```

**Nginx Configuration**
```bash
# Test Nginx configuration
sudo nginx -t

# Reload Nginx
sudo systemctl reload nginx

# Check Nginx logs
sudo tail -f /var/log/nginx/error.log
```

## Debugging Tools

### 1. Logging
```bash
# Check application logs
tail -f logs/app.log

# Check PM2 logs (production)
pm2 logs ctb-scanner

# Enable debug logging
LOG_LEVEL="DEBUG"
```

### 2. Database Debugging
```bash
# Open Prisma Studio
npm run db:studio

# Check database queries
# Add to .env
DATABASE_URL="mysql://user:pass@host:port/db?connection_limit=5&pool_timeout=60&log=query"
```

### 3. Network Debugging
```bash
# Test API endpoints
curl -v http://localhost:3000/api/health
curl -v http://localhost:3000/api/auth/status

# Check network connectivity
ping google.com
nslookup your-domain.com
```

### 4. Process Monitoring
```bash
# Check running processes
ps aux | grep node

# Monitor system resources
htop  # Linux/macOS
Get-Process | Where-Object {$_.ProcessName -eq "node"}  # Windows PowerShell
```

## Environment-Specific Issues

### Windows

**PowerShell Execution Policy**
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

**Path Issues**
```powershell
# Add to PATH
$env:PATH += ";C:\tools\nuclei"

# Permanent PATH update
[Environment]::SetEnvironmentVariable("PATH", $env:PATH + ";C:\tools\nuclei", "User")
```

**Line Ending Issues**
```bash
# Configure Git
git config --global core.autocrlf true
```

### macOS

**Permission Issues**
```bash
# Fix npm permissions
sudo chown -R $(whoami) ~/.npm

# Xcode Command Line Tools
xcode-select --install
```

**Homebrew Issues**
```bash
# Update Homebrew
brew update && brew upgrade

# Fix permissions
sudo chown -R $(whoami) /usr/local/var/homebrew
```

### Linux

**Permission Issues**
```bash
# Fix npm permissions
sudo chown -R $USER:$USER ~/.npm

# Add user to docker group
sudo usermod -aG docker $USER
```

**Service Management**
```bash
# Enable services
sudo systemctl enable mysql nginx

# Check service status
sudo systemctl status mysql nginx pm2-ctbscanner
```

## Recovery Procedures

### 1. Database Recovery
```bash
# Backup current database
mysqldump -u root -p ctb_scanner > backup_$(date +%Y%m%d).sql

# Restore from backup
mysql -u root -p ctb_scanner < backup_20231201.sql

# Reset to clean state (development)
npx prisma db reset
```

### 2. Application Recovery
```bash
# Restart application
pm2 restart ctb-scanner  # Production
# Or kill and restart dev server

# Reset to last known good state
git stash
git checkout main
git pull origin main
npm install
npm run build
```

### 3. System Recovery
```bash
# Check disk space
df -h

# Clean up logs
sudo logrotate -f /etc/logrotate.conf

# Clean up Docker (if used)
docker system prune -a
```

## Getting Help

### 1. Log Analysis
- Check application logs in `logs/` directory
- Review PM2 logs: `pm2 logs`
- Check system logs: `/var/log/` (Linux), Event Viewer (Windows)

### 2. System Information
```bash
# Gather system info
uname -a  # Linux/macOS
systeminfo  # Windows

# Node.js environment
node --version
npm --version
npm list --depth=0
```

### 3. Configuration Validation
```bash
# Validate configuration files
npm run lint
npm run type-check
nginx -t  # If using Nginx
```

### 4. Performance Profiling
```bash
# Node.js profiling
node --inspect npm run dev

# Database profiling
SHOW PROCESSLIST;
EXPLAIN SELECT * FROM scans WHERE created_at > NOW() - INTERVAL 1 DAY;
```

## Prevention

### 1. Regular Maintenance
- Update dependencies monthly
- Update Nuclei templates weekly
- Monitor disk space and logs
- Test backups regularly

### 2. Monitoring
- Set up health checks
- Monitor error rates
- Track performance metrics
- Set up alerts for critical issues

### 3. Documentation
- Keep environment documentation updated
- Document configuration changes
- Maintain runbooks for common procedures

---

**Note**: Always test solutions in a development environment before applying to production. Keep backups of configuration files and databases before making changes.

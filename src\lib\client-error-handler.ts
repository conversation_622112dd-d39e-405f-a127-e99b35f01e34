'use client'

import React from 'react'

/**
 * Client-side error handling and reporting
 */
export interface ClientErrorContext {
  component?: string
  action?: string
  userId?: string
  url?: string
  userAgent?: string
  timestamp?: number
  [key: string]: any
}

/**
 * Client-side error tracker
 */
class ClientErrorHandler {
  private errorQueue: Array<{
    error: Error
    context: ClientErrorContext
    severity: 'low' | 'medium' | 'high' | 'critical'
  }> = []
  
  private isOnline = true
  private flushInterval: NodeJS.Timeout | null = null
  
  constructor() {
    this.setupGlobalHandlers()
    this.setupOnlineDetection()
    this.startFlushInterval()
  }
  
  /**
   * Track a client-side error
   */
  trackError(
    error: Error, 
    context: ClientErrorContext = {}, 
    severity: 'low' | 'medium' | 'high' | 'critical' = 'medium'
  ): void {
    const enrichedContext: ClientErrorContext = {
      ...context,
      url: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: Date.now(),
      component: context.component || 'UNKNOWN'
    }
    
    // Add to queue
    this.errorQueue.push({
      error,
      context: enrichedContext,
      severity
    })
    
    // Log locally
    console.error('Client error tracked:', error, enrichedContext)
    
    // Try to flush immediately for critical errors
    if (severity === 'critical') {
      this.flushErrors()
    }
  }
  
  /**
   * Setup global error handlers
   */
  private setupGlobalHandlers(): void {
    // Unhandled JavaScript errors
    window.addEventListener('error', (event) => {
      this.trackError(
        new Error(event.message),
        {
          component: 'GLOBAL_ERROR_HANDLER',
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          stack: event.error?.stack
        },
        'high'
      )
    })
    
    // Unhandled Promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      const error = event.reason instanceof Error 
        ? event.reason 
        : new Error(String(event.reason))
        
      this.trackError(
        error,
        {
          component: 'UNHANDLED_REJECTION',
          reason: String(event.reason)
        },
        'high'
      )
    })
    
    // Resource loading errors
    window.addEventListener('error', (event) => {
      if (event.target && event.target !== window) {
        const target = event.target as HTMLElement
        this.trackError(
          new Error(`Resource failed to load: ${target.tagName}`),
          {
            component: 'RESOURCE_ERROR',
            tagName: target.tagName,
            src: (target as any).src || (target as any).href,
            outerHTML: target.outerHTML?.substring(0, 200)
          },
          'medium'
        )
      }
    }, true)
  }
  
  /**
   * Setup online/offline detection
   */
  private setupOnlineDetection(): void {
    window.addEventListener('online', () => {
      this.isOnline = true
      console.log('Connection restored, flushing queued errors')
      this.flushErrors()
    })
    
    window.addEventListener('offline', () => {
      this.isOnline = false
      console.log('Connection lost, errors will be queued')
    })
    
    this.isOnline = navigator.onLine
  }
  
  /**
   * Start periodic error flushing
   */
  private startFlushInterval(): void {
    this.flushInterval = setInterval(() => {
      if (this.errorQueue.length > 0 && this.isOnline) {
        this.flushErrors()
      }
    }, 30000) // Flush every 30 seconds
  }
  
  /**
   * Flush queued errors to server
   */
  private async flushErrors(): Promise<void> {
    if (this.errorQueue.length === 0 || !this.isOnline) {
      return
    }
    
    const errorsToFlush = [...this.errorQueue]
    this.errorQueue = []
    
    try {
      const response = await fetch('/api/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          errors: errorsToFlush.map(({ error, context, severity }) => ({
            error: {
              name: error.name,
              message: error.message,
              stack: error.stack
            },
            context,
            severity
          }))
        })
      })
      
      if (!response.ok) {
        throw new Error(`Failed to report errors: ${response.status}`)
      }
      
      console.log(`Successfully reported ${errorsToFlush.length} errors`)
      
    } catch (error) {
      console.error('Failed to flush errors to server:', error)
      
      // Re-queue errors if flush failed (but limit queue size)
      this.errorQueue = [...errorsToFlush, ...this.errorQueue].slice(0, 100)
    }
  }
  
  /**
   * Clear error queue
   */
  clearQueue(): void {
    this.errorQueue = []
  }
  
  /**
   * Get queue status
   */
  getQueueStatus(): { count: number; isOnline: boolean } {
    return {
      count: this.errorQueue.length,
      isOnline: this.isOnline
    }
  }
  
  /**
   * Cleanup
   */
  destroy(): void {
    if (this.flushInterval) {
      clearInterval(this.flushInterval)
    }
  }
}

// Global instance
const clientErrorHandler = new ClientErrorHandler()

/**
 * Track a client-side error
 */
export function trackClientError(
  error: Error,
  context?: ClientErrorContext,
  severity?: 'low' | 'medium' | 'high' | 'critical'
): void {
  clientErrorHandler.trackError(error, context, severity)
}

/**
 * Async operation wrapper for client-side with error handling
 */
export async function withClientErrorHandling<T>(
  operation: () => Promise<T>,
  context: ClientErrorContext = {}
): Promise<T> {
  try {
    return await operation()
  } catch (error) {
    const errorObj = error instanceof Error ? error : new Error(String(error))
    trackClientError(errorObj, context, 'medium')
    throw error
  }
}

/**
 * React component wrapper for error handling
 */
export function withClientErrorTracking<P extends object>(
  Component: React.ComponentType<P>,
  componentName?: string
) {
  return function WrappedComponent(props: P) {
    const handleError = (error: Error, context: ClientErrorContext = {}) => {
      trackClientError(error, {
        component: componentName || Component.displayName || Component.name,
        ...context
      })
    }
    
    // Add error handler to props if component accepts it
    const enhancedProps = {
      ...props,
      onError: handleError
    } as P
    
    return <Component {...enhancedProps} />
  }
}

/**
 * Hook for manual error reporting
 */
export function useClientErrorHandler(componentName?: string) {
  return {
    trackError: (error: Error, context: ClientErrorContext = {}) => {
      trackClientError(error, {
        component: componentName,
        ...context
      })
    },
    
    withErrorHandling: async <T>(
      operation: () => Promise<T>,
      context: ClientErrorContext = {}
    ): Promise<T> => {
      return withClientErrorHandling(operation, {
        component: componentName,
        ...context
      })
    }
  }
}

/**
 * Performance monitoring
 */
export function trackPerformance(name: string, duration: number, context: ClientErrorContext = {}): void {
  // Track slow operations as low-severity errors
  if (duration > 5000) { // 5 seconds
    trackClientError(
      new Error(`Slow operation: ${name} took ${duration}ms`),
      {
        component: 'PERFORMANCE_MONITOR',
        operationName: name,
        duration,
        ...context
      },
      'low'
    )
  }
}

/**
 * Memory usage monitoring
 */
export function monitorMemoryUsage(): void {
  if ('memory' in performance) {
    const memory = (performance as any).memory
    const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024)
    const limitMB = Math.round(memory.jsHeapSizeLimit / 1024 / 1024)
    
    // Alert if memory usage is high
    if (usedMB > limitMB * 0.8) {
      trackClientError(
        new Error(`High memory usage: ${usedMB}MB / ${limitMB}MB`),
        {
          component: 'MEMORY_MONITOR',
          usedMB,
          limitMB,
          usagePercent: Math.round((usedMB / limitMB) * 100)
        },
        'medium'
      )
    }
  }
}

// Start memory monitoring
if (typeof window !== 'undefined') {
  setInterval(monitorMemoryUsage, 60000) // Check every minute
}

export { clientErrorHandler }

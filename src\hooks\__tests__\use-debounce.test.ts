import { renderHook, act } from '@testing-library/react'
import { useDebounce, useDebouncedCallback } from '../use-debounce'

// Mock timers
jest.useFakeTimers()

describe('useDebounce', () => {
  afterEach(() => {
    jest.clearAllTimers()
  })

  it('should return initial value immediately', () => {
    const { result } = renderHook(() => useDebounce('initial', 500))
    
    expect(result.current).toBe('initial')
  })

  it('should debounce value changes', () => {
    const { result, rerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      { initialProps: { value: 'initial', delay: 500 } }
    )

    expect(result.current).toBe('initial')

    // Change value
    rerender({ value: 'updated', delay: 500 })
    
    // Should still be initial value
    expect(result.current).toBe('initial')

    // Fast forward time
    act(() => {
      jest.advanceTimersByTime(500)
    })

    // Should now be updated value
    expect(result.current).toBe('updated')
  })

  it('should reset timer on rapid changes', () => {
    const { result, rerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      { initialProps: { value: 'initial', delay: 500 } }
    )

    // First change
    rerender({ value: 'change1', delay: 500 })
    
    // Advance time partially
    act(() => {
      jest.advanceTimersByTime(300)
    })

    // Second change before first completes
    rerender({ value: 'change2', delay: 500 })

    // Advance time to when first change would have completed
    act(() => {
      jest.advanceTimersByTime(300)
    })

    // Should still be initial value
    expect(result.current).toBe('initial')

    // Advance remaining time for second change
    act(() => {
      jest.advanceTimersByTime(200)
    })

    // Should now be the second change
    expect(result.current).toBe('change2')
  })

  it('should handle delay changes', () => {
    const { result, rerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      { initialProps: { value: 'initial', delay: 500 } }
    )

    rerender({ value: 'updated', delay: 1000 })

    // Advance by original delay
    act(() => {
      jest.advanceTimersByTime(500)
    })

    // Should still be initial (new delay is 1000ms)
    expect(result.current).toBe('initial')

    // Advance by remaining time
    act(() => {
      jest.advanceTimersByTime(500)
    })

    // Should now be updated
    expect(result.current).toBe('updated')
  })

  it('should handle zero delay', () => {
    const { result, rerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      { initialProps: { value: 'initial', delay: 0 } }
    )

    rerender({ value: 'updated', delay: 0 })

    // With zero delay, should update immediately
    expect(result.current).toBe('updated')
  })
})

describe('useDebouncedCallback', () => {
  afterEach(() => {
    jest.clearAllTimers()
  })

  it('should debounce callback execution', () => {
    const callback = jest.fn()
    const { result } = renderHook(() => useDebouncedCallback(callback, 500))

    // Call multiple times rapidly
    act(() => {
      result.current('arg1')
      result.current('arg2')
      result.current('arg3')
    })

    // Callback should not have been called yet
    expect(callback).not.toHaveBeenCalled()

    // Advance time
    act(() => {
      jest.advanceTimersByTime(500)
    })

    // Callback should have been called once with last arguments
    expect(callback).toHaveBeenCalledTimes(1)
    expect(callback).toHaveBeenCalledWith('arg3')
  })

  it('should cancel previous calls', () => {
    const callback = jest.fn()
    const { result } = renderHook(() => useDebouncedCallback(callback, 500))

    // First call
    act(() => {
      result.current('first')
    })

    // Advance time partially
    act(() => {
      jest.advanceTimersByTime(300)
    })

    // Second call before first completes
    act(() => {
      result.current('second')
    })

    // Advance time to when first call would have completed
    act(() => {
      jest.advanceTimersByTime(300)
    })

    // Callback should not have been called yet
    expect(callback).not.toHaveBeenCalled()

    // Advance remaining time for second call
    act(() => {
      jest.advanceTimersByTime(200)
    })

    // Callback should have been called once with second arguments
    expect(callback).toHaveBeenCalledTimes(1)
    expect(callback).toHaveBeenCalledWith('second')
  })

  it('should handle multiple arguments', () => {
    const callback = jest.fn()
    const { result } = renderHook(() => useDebouncedCallback(callback, 500))

    act(() => {
      result.current('arg1', 'arg2', { key: 'value' })
    })

    act(() => {
      jest.advanceTimersByTime(500)
    })

    expect(callback).toHaveBeenCalledWith('arg1', 'arg2', { key: 'value' })
  })

  it('should provide cancel function', () => {
    const callback = jest.fn()
    const { result } = renderHook(() => useDebouncedCallback(callback, 500))

    act(() => {
      result.current('test')
    })

    // Cancel the pending call
    act(() => {
      result.current.cancel()
    })

    // Advance time
    act(() => {
      jest.advanceTimersByTime(500)
    })

    // Callback should not have been called
    expect(callback).not.toHaveBeenCalled()
  })

  it('should provide flush function', () => {
    const callback = jest.fn()
    const { result } = renderHook(() => useDebouncedCallback(callback, 500))

    act(() => {
      result.current('test')
    })

    // Flush immediately
    act(() => {
      result.current.flush()
    })

    // Callback should have been called immediately
    expect(callback).toHaveBeenCalledWith('test')

    // Advancing time should not call it again
    act(() => {
      jest.advanceTimersByTime(500)
    })

    expect(callback).toHaveBeenCalledTimes(1)
  })

  it('should handle callback changes', () => {
    const callback1 = jest.fn()
    const callback2 = jest.fn()
    
    const { result, rerender } = renderHook(
      ({ callback, delay }) => useDebouncedCallback(callback, delay),
      { initialProps: { callback: callback1, delay: 500 } }
    )

    act(() => {
      result.current('test')
    })

    // Change callback before execution
    rerender({ callback: callback2, delay: 500 })

    act(() => {
      jest.advanceTimersByTime(500)
    })

    // New callback should be called
    expect(callback1).not.toHaveBeenCalled()
    expect(callback2).toHaveBeenCalledWith('test')
  })

  it('should cleanup on unmount', () => {
    const callback = jest.fn()
    const { result, unmount } = renderHook(() => useDebouncedCallback(callback, 500))

    act(() => {
      result.current('test')
    })

    // Unmount before execution
    unmount()

    act(() => {
      jest.advanceTimersByTime(500)
    })

    // Callback should not have been called
    expect(callback).not.toHaveBeenCalled()
  })
})

/**
 * Application Startup Manager
 * 
 * This module handles the initialization and health checking of all critical
 * services during application startup, with special focus on Nuclei scanner
 * reliability and Windows compatibility.
 */

import { nucleiScanner } from './nuclei'
import { jobQueue } from './job-queue'
import { initializeServices } from './init'
import { appProcessManager } from './app-process-manager'
import { logger } from './logger'

interface StartupResult {
  success: boolean
  services: {
    nuclei: { healthy: boolean; details: string }
    jobQueue: { healthy: boolean; details: string }
    database: { healthy: boolean; details: string }
  }
  errors: string[]
  warnings: string[]
}

class StartupManager {
  private isInitialized = false
  private lastHealthCheck: Date | null = null
  private healthCheckInterval: NodeJS.Timeout | null = null

  /**
   * Perform comprehensive application startup with health checks
   */
  async startup(options: {
    skipNucleiCheck?: boolean
    retries?: number
    healthCheckInterval?: number
  } = {}): Promise<StartupResult> {
    const {
      skipNucleiCheck = false,
      retries = 3,
      healthCheckInterval = 300000 // 5 minutes
    } = options

    console.log('🚀 Starting CTB Scanner application...')
    console.log('=' .repeat(50))

    const result: StartupResult = {
      success: false,
      services: {
        nuclei: { healthy: false, details: 'Not checked' },
        jobQueue: { healthy: false, details: 'Not checked' },
        database: { healthy: false, details: 'Not checked' }
      },
      errors: [],
      warnings: []
    }

    try {
      // Step 1: Initialize core services
      console.log('📋 Step 1: Initializing core services...')
      await this.initializeCoreServices(result)

      // Step 2: Check database connectivity
      console.log('📋 Step 2: Checking database connectivity...')
      await this.checkDatabaseHealth(result)

      // Step 3: Check Nuclei scanner (with option to skip)
      if (!skipNucleiCheck) {
        console.log('📋 Step 3: Checking Nuclei scanner health...')
        await this.checkNucleiHealth(result, retries)
      } else {
        console.log('⚠️ Skipping Nuclei health check (skipNucleiCheck=true)')
        result.warnings.push('Nuclei health check was skipped')
        result.services.nuclei = { healthy: true, details: 'Skipped by configuration' }
      }

      // Step 4: Start background services
      console.log('📋 Step 4: Starting background services...')
      await this.startBackgroundServices(result)

      // Step 5: Initialize error handling and process management
      console.log('🛡️ Step 5: Initializing error handling and process management...')
      await this.initializeErrorHandling(result)

      // Step 6: Initialize performance monitoring
      console.log('📊 Step 6: Initializing performance monitoring...')
      await this.initializePerformanceMonitoring(result)

      // Determine overall success
      const allServicesHealthy = Object.values(result.services).every(service => service.healthy)
      result.success = allServicesHealthy && result.errors.length === 0

      if (result.success) {
        console.log('✅ Application startup completed successfully')
        this.isInitialized = true
        this.lastHealthCheck = new Date()

        // Start periodic health checks
        this.startPeriodicHealthChecks(healthCheckInterval)

        // Start recovery service for automatic service recovery
        try {
          const { recoveryService } = await import('./recovery')
          recoveryService.start()
          console.log('🔧 Automatic recovery service started')
        } catch (error) {
          console.warn('⚠️ Failed to start recovery service:', error)
          result.warnings.push('Recovery service failed to start')
        }
      } else {
        console.error('❌ Application startup completed with issues')
        console.error('Errors:', result.errors)
        console.warn('Warnings:', result.warnings)
      }

    } catch (error) {
      console.error('💥 Critical startup error:', error)
      result.errors.push(`Critical startup error: ${error instanceof Error ? error.message : 'Unknown error'}`)
      result.success = false
    }

    console.log('=' .repeat(50))
    return result
  }

  /**
   * Initialize core application services
   */
  private async initializeCoreServices(result: StartupResult): Promise<void> {
    try {
      initializeServices()
      result.services.jobQueue.healthy = true
      result.services.jobQueue.details = 'Core services initialized successfully'
      console.log('✅ Core services initialized')
    } catch (error) {
      const message = `Failed to initialize core services: ${error instanceof Error ? error.message : 'Unknown error'}`
      result.errors.push(message)
      result.services.jobQueue.healthy = false
      result.services.jobQueue.details = message
      console.error('❌', message)
    }
  }

  /**
   * Check database connectivity and health
   */
  private async checkDatabaseHealth(result: StartupResult): Promise<void> {
    try {
      // Import db here to avoid circular dependencies
      const { db } = await import('./db')
      
      // Simple connectivity test
      await db.$queryRaw`SELECT 1 as test`
      
      result.services.database.healthy = true
      result.services.database.details = 'Database connection successful'
      console.log('✅ Database connectivity verified')
    } catch (error) {
      const message = `Database health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      result.errors.push(message)
      result.services.database.healthy = false
      result.services.database.details = message
      console.error('❌', message)
    }
  }

  /**
   * Check Nuclei scanner health with retry logic
   */
  private async checkNucleiHealth(result: StartupResult, retries: number): Promise<void> {
    try {
      console.log(`🔍 Performing Nuclei health check (${retries} retries)...`)
      
      const healthCheck = await nucleiScanner.performHealthCheck(retries, 3000)
      
      result.services.nuclei.healthy = healthCheck.healthy
      result.services.nuclei.details = healthCheck.details

      if (healthCheck.healthy) {
        console.log('✅ Nuclei scanner is healthy and ready')
      } else {
        const message = `Nuclei health check failed: ${healthCheck.details}`
        result.errors.push(message)
        console.error('❌', message)
        
        // Add Windows-specific troubleshooting advice
        if (process.platform === 'win32') {
          result.warnings.push('Windows detected: If Nuclei issues persist, try restarting the application or running as administrator')
        }
      }
    } catch (error) {
      const message = `Nuclei health check error: ${error instanceof Error ? error.message : 'Unknown error'}`
      result.errors.push(message)
      result.services.nuclei.healthy = false
      result.services.nuclei.details = message
      console.error('❌', message)
    }
  }

  /**
   * Start background services and job processing
   */
  private async startBackgroundServices(result: StartupResult): Promise<void> {
    try {
      // Ensure job queue is properly initialized
      if (!jobQueue.isInitialized) {
        jobQueue.initialize()
      }

      // Recover any stuck scans from previous sessions
      await jobQueue.recoverStuckScans()

      console.log('✅ Background services started')
    } catch (error) {
      const message = `Failed to start background services: ${error instanceof Error ? error.message : 'Unknown error'}`
      result.warnings.push(message)
      console.warn('⚠️', message)
    }
  }

  /**
   * Initialize error handling and process management
   */
  private async initializeErrorHandling(result: StartupResult): Promise<void> {
    try {
      // Process manager is conditionally initialized based on environment
      if (appProcessManager) {
        logger.info('Error handling and process management initialized', {
          component: 'STARTUP',
          processStatus: appProcessManager.getStatus()
        })
      } else {
        logger.info('Error handling skipped in development/test environment', {
          component: 'STARTUP'
        })
      }

      console.log('✅ Error handling and process management initialized')
    } catch (error) {
      const message = `Failed to initialize error handling: ${error instanceof Error ? error.message : 'Unknown error'}`
      result.warnings.push(message)
      console.warn('⚠️', message)
    }
  }

  /**
   * Initialize performance monitoring and caching
   */
  private async initializePerformanceMonitoring(result: StartupResult): Promise<void> {
    try {
      const { startPerformanceMonitoring } = await import('./performance')
      const { startCacheCleanup } = await import('./cache')

      // Start performance monitoring (every minute)
      startPerformanceMonitoring(60000)

      // Start cache cleanup (every 5 minutes)
      startCacheCleanup(300000)

      console.log('✅ Performance monitoring and caching initialized')
    } catch (error) {
      const message = `Failed to initialize performance monitoring: ${error instanceof Error ? error.message : 'Unknown error'}`
      result.warnings.push(message)
      console.warn('⚠️', message)
    }
  }

  /**
   * Start periodic health checks
   */
  private startPeriodicHealthChecks(intervalMs: number): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval)
    }

    this.healthCheckInterval = setInterval(async () => {
      console.log('🔄 Performing periodic health check...')
      
      try {
        const healthCheck = await nucleiScanner.performHealthCheck(1, 1000)
        this.lastHealthCheck = new Date()
        
        if (!healthCheck.healthy) {
          console.warn('⚠️ Periodic health check failed:', healthCheck.details)
        } else {
          console.log('✅ Periodic health check passed')
        }
      } catch (error) {
        console.error('❌ Periodic health check error:', error)
      }
    }, intervalMs)

    console.log(`🔄 Periodic health checks started (every ${intervalMs / 1000}s)`)
  }

  /**
   * Graceful shutdown
   */
  async shutdown(): Promise<void> {
    console.log('🛑 Shutting down application...')

    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval)
      this.healthCheckInterval = null
    }

    try {
      // Stop recovery service
      try {
        const { recoveryService } = await import('./recovery')
        recoveryService.stop()
        console.log('🔧 Recovery service stopped')
      } catch (error) {
        console.warn('⚠️ Error stopping recovery service:', error)
      }

      // Stop job processing
      jobQueue.stop()

      // Kill any running Nuclei processes
      const { processManager } = await import('./process-manager')
      processManager.killAllProcesses()

      this.isInitialized = false
      console.log('✅ Application shutdown completed')
    } catch (error) {
      console.error('❌ Error during shutdown:', error)
    }
  }

  /**
   * Get current application status
   */
  getStatus(): {
    initialized: boolean
    lastHealthCheck: Date | null
    uptime: number
  } {
    return {
      initialized: this.isInitialized,
      lastHealthCheck: this.lastHealthCheck,
      uptime: this.isInitialized ? Date.now() - (this.lastHealthCheck?.getTime() || Date.now()) : 0
    }
  }

  /**
   * Force re-initialization (useful for recovery)
   */
  async reinitialize(): Promise<StartupResult> {
    console.log('🔄 Force re-initializing application...')
    
    // Reset state
    this.isInitialized = false
    this.lastHealthCheck = null
    
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval)
      this.healthCheckInterval = null
    }

    // Perform startup again
    return await this.startup()
  }
}

// Export singleton instance
export const startupManager = new StartupManager()

// Export convenience functions
export const performStartup = (options?: Parameters<typeof startupManager.startup>[0]) => 
  startupManager.startup(options)

export const getApplicationStatus = () => startupManager.getStatus()

export const reinitializeApplication = () => startupManager.reinitialize()

export const shutdownApplication = () => startupManager.shutdown()

# CTB Scanner - Windows PowerShell Maintenance Script
# This script performs routine maintenance tasks for the CTB Scanner application on Windows

param(
    [string]$Action = "full",
    [string]$ConfigFile = ".env"
)

# Configuration
$AppDir = Split-Path -Parent $PSScriptRoot
$BackupDir = Join-Path $AppDir "backups"
$LogDir = Join-Path $AppDir "logs"
$DbName = "ctb_scanner_prod"
$DbUser = "ctb_user"
$DbPass = "secure_password"

# Create directories if they don't exist
if (!(Test-Path $BackupDir)) { New-Item -ItemType Directory -Path $BackupDir -Force | Out-Null }
if (!(Test-Path $LogDir)) { New-Item -ItemType Directory -Path $LogDir -Force | Out-Null }

# Logging functions
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $color = switch ($Level) {
        "ERROR" { "Red" }
        "WARNING" { "Yellow" }
        "SUCCESS" { "Green" }
        "INFO" { "Cyan" }
        default { "White" }
    }
    
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
}

function Write-Success { param([string]$Message) Write-Log $Message "SUCCESS" }
function Write-Error { param([string]$Message) Write-Log $Message "ERROR" }
function Write-Warning { param([string]$Message) Write-Log $Message "WARNING" }
function Write-Info { param([string]$Message) Write-Log $Message "INFO" }

# Check if MySQL is available
function Test-MySQL {
    try {
        $null = Get-Command mysql -ErrorAction Stop
        return $true
    }
    catch {
        Write-Warning "MySQL not found in PATH"
        return $false
    }
}

# Database backup function
function Backup-Database {
    Write-Log "Creating database backup..."
    
    if (!(Test-MySQL)) {
        Write-Error "MySQL not available"
        return $false
    }
    
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $backupFile = Join-Path $BackupDir "ctb_scanner_$timestamp.sql"
    
    try {
        $env:MYSQL_PWD = $DbPass
        & mysqldump -u $DbUser $DbName | Out-File -FilePath $backupFile -Encoding UTF8
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Database backup created: $backupFile"
            
            # Compress backup if 7-Zip is available
            if (Get-Command 7z -ErrorAction SilentlyContinue) {
                & 7z a "$backupFile.7z" $backupFile
                if ($LASTEXITCODE -eq 0) {
                    Remove-Item $backupFile -Force
                    Write-Success "Backup compressed: $backupFile.7z"
                }
            }
            
            # Clean old backups (keep last 7 days)
            $cutoffDate = (Get-Date).AddDays(-7)
            Get-ChildItem $BackupDir -Filter "*.sql*" | Where-Object { $_.CreationTime -lt $cutoffDate } | Remove-Item -Force
            Write-Success "Old backups cleaned up"
            
            return $true
        }
        else {
            Write-Error "Database backup failed"
            return $false
        }
    }
    catch {
        Write-Error "Database backup failed: $($_.Exception.Message)"
        return $false
    }
    finally {
        Remove-Item Env:MYSQL_PWD -ErrorAction SilentlyContinue
    }
}

# Update Nuclei templates
function Update-NucleiTemplates {
    Write-Log "Updating Nuclei templates..."
    
    try {
        $null = Get-Command nuclei -ErrorAction Stop
        & nuclei -update-templates -silent
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Nuclei templates updated successfully"
            return $true
        }
        else {
            Write-Warning "Failed to update Nuclei templates"
            return $false
        }
    }
    catch {
        Write-Warning "Nuclei not found in PATH"
        return $false
    }
}

# Clean up log files
function Clear-LogFiles {
    Write-Log "Cleaning up log files..."
    
    try {
        # Remove large log files (>100MB)
        Get-ChildItem $LogDir -Filter "*.log" -Recurse | Where-Object { $_.Length -gt 104857600 } | ForEach-Object {
            Write-Info "Rotating large log file: $($_.FullName)"
            Move-Item $_.FullName "$($_.FullName).old" -Force
            New-Item -ItemType File -Path $_.FullName -Force | Out-Null
        }
        
        # Remove old compressed logs (older than 30 days)
        $cutoffDate = (Get-Date).AddDays(-30)
        Get-ChildItem $LogDir -Filter "*.gz" -Recurse | Where-Object { $_.CreationTime -lt $cutoffDate } | Remove-Item -Force
        Get-ChildItem $LogDir -Filter "*.7z" -Recurse | Where-Object { $_.CreationTime -lt $cutoffDate } | Remove-Item -Force
        
        Write-Success "Log cleanup completed"
        return $true
    }
    catch {
        Write-Error "Log cleanup failed: $($_.Exception.Message)"
        return $false
    }
}

# Check disk space
function Test-DiskSpace {
    Write-Log "Checking disk space..."
    
    try {
        $drive = (Get-Item $AppDir).PSDrive
        $freeSpace = [math]::Round($drive.Free / 1GB, 2)
        $totalSpace = [math]::Round(($drive.Free + $drive.Used) / 1GB, 2)
        $usedPercent = [math]::Round((($drive.Used / ($drive.Free + $drive.Used)) * 100), 2)
        
        Write-Info "Drive $($drive.Name): $freeSpace GB free of $totalSpace GB total ($usedPercent% used)"
        
        if ($freeSpace -lt 1) {
            Write-Warning "Low disk space: Only $freeSpace GB remaining"
        }
        
        return $true
    }
    catch {
        Write-Error "Disk space check failed: $($_.Exception.Message)"
        return $false
    }
}

# Check application health
function Test-ApplicationHealth {
    Write-Log "Checking application health..."
    
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:3000/api/health" -TimeoutSec 10 -UseBasicParsing
        
        if ($response.StatusCode -eq 200) {
            Write-Success "Application health check: OK"
            return $true
        }
        else {
            Write-Error "Application health check failed (HTTP $($response.StatusCode))"
            
            # Try to restart with PM2 if available
            if (Get-Command pm2 -ErrorAction SilentlyContinue) {
                Write-Warning "Attempting to restart application..."
                & pm2 restart ctb-scanner
                Start-Sleep -Seconds 10
                
                # Check again
                $response2 = Invoke-WebRequest -Uri "http://localhost:3000/api/health" -TimeoutSec 10 -UseBasicParsing
                if ($response2.StatusCode -eq 200) {
                    Write-Success "Application restarted successfully"
                    return $true
                }
                else {
                    Write-Error "Application restart failed"
                    return $false
                }
            }
            return $false
        }
    }
    catch {
        Write-Error "Application health check failed: $($_.Exception.Message)"
        return $false
    }
}

# Check SSL certificate
function Test-SSLCertificate {
    param([string]$Domain = "your-domain.com")
    
    Write-Log "Checking SSL certificate for $Domain..."
    
    try {
        $uri = "https://$Domain"
        $request = [System.Net.WebRequest]::Create($uri)
        $request.Timeout = 10000
        $response = $request.GetResponse()
        $response.Close()
        
        Write-Success "SSL certificate check: OK"
        return $true
    }
    catch {
        Write-Warning "SSL certificate check failed: $($_.Exception.Message)"
        return $false
    }
}

# Generate maintenance report
function New-MaintenanceReport {
    Write-Log "Generating maintenance report..."
    
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $reportFile = Join-Path $LogDir "maintenance_report_$timestamp.txt"
    
    try {
        $report = @"
CTB Scanner Maintenance Report
Generated: $(Get-Date)
================================

System Information:
- Computer: $env:COMPUTERNAME
- OS: $((Get-WmiObject Win32_OperatingSystem).Caption)
- User: $env:USERNAME
- PowerShell Version: $($PSVersionTable.PSVersion)

Disk Space:
$(Get-WmiObject Win32_LogicalDisk | Where-Object {$_.DriveType -eq 3} | ForEach-Object {
    "- Drive $($_.DeviceID) $([math]::Round($_.FreeSpace/1GB,2)) GB free of $([math]::Round($_.Size/1GB,2)) GB total"
})

Application Status:
$(if (Get-Command pm2 -ErrorAction SilentlyContinue) {
    & pm2 status 2>&1 | Out-String
} else {
    "PM2 not available"
})

Database Status:
$(if (Get-Service MySQL* -ErrorAction SilentlyContinue) {
    "MySQL: Running"
} else {
    "MySQL: Not running or not installed as service"
})

Recent Backups:
$(if (Test-Path $BackupDir) {
    Get-ChildItem $BackupDir -Filter "*.sql*" | Sort-Object CreationTime -Descending | Select-Object -First 5 | ForEach-Object { $_.Name }
} else {
    "No backup directory found"
})

Log Files:
$(if (Test-Path $LogDir) {
    Get-ChildItem $LogDir -Filter "*.log" | Select-Object -First 10 | ForEach-Object { $_.Name }
} else {
    "No log files found"
})
"@
        
        $report | Out-File -FilePath $reportFile -Encoding UTF8
        Write-Success "Maintenance report saved: $reportFile"
        return $true
    }
    catch {
        Write-Error "Failed to generate maintenance report: $($_.Exception.Message)"
        return $false
    }
}

# Main maintenance function
function Start-Maintenance {
    Write-Log "Starting CTB Scanner maintenance..."
    
    $success = $true
    
    if (!(Backup-Database)) { $success = $false }
    if (!(Update-NucleiTemplates)) { $success = $false }
    if (!(Clear-LogFiles)) { $success = $false }
    if (!(Test-DiskSpace)) { $success = $false }
    if (!(Test-ApplicationHealth)) { $success = $false }
    if (!(Test-SSLCertificate)) { $success = $false }
    if (!(New-MaintenanceReport)) { $success = $false }
    
    if ($success) {
        Write-Success "Maintenance completed successfully!"
    }
    else {
        Write-Warning "Maintenance completed with some issues. Check the logs for details."
    }
    
    return $success
}

# Help function
function Show-Help {
    Write-Host @"
CTB Scanner Maintenance Script (PowerShell)

Usage: .\maintenance.ps1 [-Action <action>] [-ConfigFile <file>]

Actions:
  full          Run full maintenance (default)
  backup        Database backup only
  update        Update Nuclei templates only
  cleanup       Log cleanup only
  health        Health check only
  ssl           SSL certificate check only
  report        Generate report only
  help          Show this help message

Examples:
  .\maintenance.ps1                    # Run full maintenance
  .\maintenance.ps1 -Action backup     # Backup database only
  .\maintenance.ps1 -Action health     # Check application health
"@
}

# Main script logic
switch ($Action.ToLower()) {
    "backup" { 
        if (Test-MySQL) { Backup-Database }
    }
    "update" { Update-NucleiTemplates }
    "cleanup" { Clear-LogFiles }
    "health" { Test-ApplicationHealth }
    "ssl" { Test-SSLCertificate }
    "report" { New-MaintenanceReport }
    "help" { Show-Help }
    "full" { 
        if (Test-MySQL) { Start-Maintenance }
    }
    default {
        Write-Error "Unknown action: $Action"
        Show-Help
        exit 1
    }
}

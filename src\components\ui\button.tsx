import * as React from 'react'
import { cn } from '@/lib/utils'

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = 'default', size = 'default', ...props }, ref) => {
    return (
      <button
        className={cn(
          'inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium transition-all duration-200 ease-in-out focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 active:scale-[0.98] shadow-sm',
          {
            // Default: Dark blue gradient
            'bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700 text-white hover:from-blue-800 hover:via-blue-700 hover:to-blue-600':
              variant === 'default',

            // Destructive: Gradient red
            'bg-gradient-to-br from-red-600 to-red-700 text-white hover:from-red-500 hover:to-red-600':
              variant === 'destructive',

            // Outline: Neutral with border
            'border border-gray-300 bg-white text-gray-900 hover:bg-gray-100':
              variant === 'outline',

            // Secondary: Light gray background
            'bg-gray-100 text-gray-900 hover:bg-gray-200':
              variant === 'secondary',

            // Ghost: Text only with hover effect
            'text-gray-900 hover:bg-gray-100':
              variant === 'ghost',

            // Link: Styled like a link
            'text-blue-600 underline underline-offset-4 hover:text-blue-700':
              variant === 'link',
          },
          {
            'h-10 px-4 py-2': size === 'default',
            'h-9 rounded-md px-3': size === 'sm',
            'h-11 rounded-md px-8': size === 'lg',
            'h-10 w-10': size === 'icon',
          },
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)

Button.displayName = 'Button'

export { Button }

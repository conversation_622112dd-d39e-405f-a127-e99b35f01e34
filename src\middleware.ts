/**
 * Next.js Middleware for Authentication and Route Protection
 *
 * This middleware runs on every request to handle authentication state
 * and route protection. It ensures users are properly authenticated
 * before accessing protected routes and redirects appropriately.
 *
 * Key Features:
 * - JWT token verification from HTTP-only cookies
 * - Automatic redirection based on authentication state
 * - Protection of dashboard and admin routes
 * - Prevention of authenticated users accessing auth pages
 * - Security headers injection for enhanced protection
 *
 * Route Categories:
 * - Public: Accessible without authentication (/, /login, /signup)
 * - Auth: Login/signup pages (redirect to dashboard if authenticated)
 * - Protected: Require authentication (/dashboard/*)
 *
 * Security Headers Applied:
 * - X-Frame-Options: Prevents clickjacking attacks
 * - X-Content-Type-Options: Prevents MIME type sniffing
 * - Referrer-Policy: Controls referrer information
 * - X-XSS-Protection: Enables XSS filtering
 *
 * <AUTHOR> Scanner Team
 * @version 1.0.0
 */

import { NextRequest, NextResponse } from 'next/server'
import { verifyToken } from './lib/auth'
import { addSecurityHeaders, rateLimits, getClientIP, logSecurityEvent } from './lib/security'

/**
 * Apply rate limiting for API routes
 */
function applyApiRateLimit(request: NextRequest, pathname: string): { allowed: boolean; remaining: number; resetTime: number; retryAfter?: number } {
  // Determine rate limit type based on route
  let limitType: keyof typeof rateLimits = 'api'

  if (pathname.startsWith('/api/scans') || pathname.startsWith('/api/scan')) {
    limitType = 'scan'
  } else if (pathname.startsWith('/api/auth') || pathname.startsWith('/api/login') || pathname.startsWith('/api/signup')) {
    limitType = 'auth'
  } else if (pathname.startsWith('/api/health') || pathname.startsWith('/api/system')) {
    limitType = 'health'
  }

  // Apply rate limiting
  const rateLimit = rateLimits[limitType]
  return rateLimit(request)
}

/**
 * Main middleware function that handles authentication and routing
 *
 * Processes every incoming request to determine authentication state
 * and apply appropriate access controls and redirections.
 *
 * @param {NextRequest} request - Incoming HTTP request
 * @returns {Promise<NextResponse>} Response with potential redirects and security headers
 */
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  const method = request.method
  const startTime = Date.now()

  // Log request
  console.log(`🌐 ${method} ${pathname} - ${getClientIP(request)}`)

  // ============================================================================
  // SECURITY CHECKS
  // ============================================================================

  // Apply rate limiting for API routes
  if (pathname.startsWith('/api/')) {
    const rateLimitResult = applyApiRateLimit(request, pathname)
    if (!rateLimitResult.allowed) {
      logSecurityEvent('RATE_LIMIT_EXCEEDED', {
        path: pathname,
        remaining: rateLimitResult.remaining,
        resetTime: rateLimitResult.resetTime
      }, request)

      const rateLimitResponse = NextResponse.json({
        error: 'Rate limit exceeded',
        message: `Too many requests. Try again in ${rateLimitResult.retryAfter || 60} seconds.`,
        retryAfter: rateLimitResult.retryAfter || 60
      }, { status: 429 })

      rateLimitResponse.headers.set('Retry-After', (rateLimitResult.retryAfter || 60).toString())
      return addSecurityHeaders(rateLimitResponse)
    }
  }

  // ============================================================================
  // ROUTE CONFIGURATION
  // ============================================================================

  // Define protected routes that require authentication
  const protectedRoutes = ['/dashboard']

  // Define auth routes that should redirect authenticated users
  const authRoutes = ['/login', '/signup']

  // ============================================================================
  // ROUTE CLASSIFICATION
  // ============================================================================

  // Check if the current path is a protected route
  const isProtectedRoute = protectedRoutes.some(route =>
    pathname.startsWith(route)
  )

  // Check if the current path is an auth route
  const isAuthRoute = authRoutes.some(route =>
    pathname.startsWith(route)
  )

  // ============================================================================
  // AUTHENTICATION VERIFICATION
  // ============================================================================

  // Get the auth token from HTTP-only cookies
  const token = request.cookies.get('auth-token')?.value

  // Verify the token if it exists and determine authentication state
  let isAuthenticated = false
  if (token) {
    const payload = await verifyToken(token)
    isAuthenticated = !!payload
  }

  // ============================================================================
  // REDIRECT LOGIC
  // ============================================================================

  if (isProtectedRoute && !isAuthenticated) {
    // Redirect unauthenticated users to login page
    return NextResponse.redirect(new URL('/login', request.url))
  }

  if (isAuthRoute && isAuthenticated) {
    // Redirect authenticated users away from auth pages to dashboard
    return NextResponse.redirect(new URL('/dashboard', request.url))
  }

  // ============================================================================
  // SECURITY HEADERS INJECTION
  // ============================================================================

  // Create response and add comprehensive security headers
  const response = NextResponse.next()

  // Apply all security headers
  const secureResponse = addSecurityHeaders(response)

  // Add response time header
  const responseTime = Date.now() - startTime
  secureResponse.headers.set('X-Response-Time', `${responseTime}ms`)

  // Log response
  console.log(`✅ ${method} ${pathname} - ${secureResponse.status || 200} (${responseTime}ms)`)

  return secureResponse
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
}

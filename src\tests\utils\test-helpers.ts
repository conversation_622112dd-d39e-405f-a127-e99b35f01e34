/**
 * Test helper utilities for creating mock data and API responses
 */

export interface MockScan {
  id: string
  targetUrl: string
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED'
  startedAt?: string
  completedAt?: string
  duration?: number
  totalVulns?: number
  criticalVulns?: number
  highVulns?: number
  mediumVulns?: number
  lowVulns?: number
  infoVulns?: number
  unknownVulns?: number
  nucleiVersion?: string
  templateCount?: number
  userId?: string
  createdAt?: string
  updatedAt?: string
}

export interface MockAsset {
  id: string
  url: string
  domain: string
  title?: string
  description?: string
  technology?: string[]
  status: 'ACTIVE' | 'INACTIVE' | 'ARCHIVED'
  lastScanned?: string
  totalScans?: number
  totalVulnerabilities?: number
  criticalVulns?: number
  highVulns?: number
  mediumVulns?: number
  lowVulns?: number
  infoVulns?: number
  unknownVulns?: number
  userId?: string
  createdAt?: string
  updatedAt?: string
}

export interface MockVulnerability {
  id: string
  templateId: string
  name: string
  severity: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW' | 'INFO' | 'UNKNOWN'
  description?: string
  host: string
  matchedAt?: string
  tags?: string[]
  reference?: string[]
  matcher?: string
  extractedResults?: string[]
  request?: string
  response?: string
  curlCommand?: string
  timestamp: string
  scanId: string
  assetId?: string
}

export interface MockUser {
  id: string
  firstName: string
  lastName: string
  email: string
  companyName?: string
  country?: string
  createdAt?: string
  updatedAt?: string
}

export interface MockApiResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: any
  }
  message?: string
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

/**
 * Create a mock scan object with default values
 */
export function createMockScan(overrides: Partial<MockScan> = {}): MockScan {
  const defaultScan: MockScan = {
    id: 'scan_' + Math.random().toString(36).substr(2, 9),
    targetUrl: 'https://example.com',
    status: 'COMPLETED',
    startedAt: new Date().toISOString(),
    completedAt: new Date().toISOString(),
    duration: 300,
    totalVulns: 5,
    criticalVulns: 1,
    highVulns: 2,
    mediumVulns: 1,
    lowVulns: 1,
    infoVulns: 0,
    unknownVulns: 0,
    nucleiVersion: 'v2.9.15',
    templateCount: 4500,
    userId: 'user_123',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }

  return { ...defaultScan, ...overrides }
}

/**
 * Create a mock asset object with default values
 */
export function createMockAsset(overrides: Partial<MockAsset> = {}): MockAsset {
  const defaultAsset: MockAsset = {
    id: 'asset_' + Math.random().toString(36).substr(2, 9),
    url: 'https://example.com',
    domain: 'example.com',
    title: 'Example Website',
    description: 'A test website',
    technology: ['nginx', 'php'],
    status: 'ACTIVE',
    lastScanned: new Date().toISOString(),
    totalScans: 3,
    totalVulnerabilities: 5,
    criticalVulns: 1,
    highVulns: 2,
    mediumVulns: 1,
    lowVulns: 1,
    infoVulns: 0,
    unknownVulns: 0,
    userId: 'user_123',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }

  return { ...defaultAsset, ...overrides }
}

/**
 * Create a mock vulnerability object with default values
 */
export function createMockVulnerability(overrides: Partial<MockVulnerability> = {}): MockVulnerability {
  const defaultVuln: MockVulnerability = {
    id: 'vuln_' + Math.random().toString(36).substr(2, 9),
    templateId: 'CVE-2023-1234',
    name: 'Test Vulnerability',
    severity: 'HIGH',
    description: 'A test vulnerability description',
    host: 'https://example.com',
    matchedAt: '/test',
    tags: ['test', 'cve'],
    reference: ['https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-1234'],
    matcher: 'Test matcher',
    extractedResults: ['test result'],
    request: 'GET /test HTTP/1.1\nHost: example.com',
    response: 'HTTP/1.1 200 OK\nContent-Type: text/html',
    curlCommand: 'curl -X GET https://example.com/test',
    timestamp: new Date().toISOString(),
    scanId: 'scan_123',
    assetId: 'asset_456',
  }

  return { ...defaultVuln, ...overrides }
}

/**
 * Create a mock user object with default values
 */
export function createMockUser(overrides: Partial<MockUser> = {}): MockUser {
  const defaultUser: MockUser = {
    id: 'user_' + Math.random().toString(36).substr(2, 9),
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    companyName: 'Test Company',
    country: 'United States',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }

  return { ...defaultUser, ...overrides }
}

/**
 * Create a mock API response with success status
 */
export function createMockApiResponse<T>(
  data: T,
  overrides: Partial<MockApiResponse<T>> = {}
): MockApiResponse<T> {
  const defaultResponse: MockApiResponse<T> = {
    success: true,
    data,
    message: 'Operation completed successfully',
  }

  return { ...defaultResponse, ...overrides }
}

/**
 * Create a mock API error response
 */
export function createMockApiError(
  code: string = 'INTERNAL_ERROR',
  message: string = 'An error occurred',
  details?: any
): MockApiResponse {
  return {
    success: false,
    error: {
      code,
      message,
      details,
    },
  }
}

/**
 * Create a mock paginated API response
 */
export function createMockPaginatedResponse<T>(
  data: T[],
  page: number = 1,
  limit: number = 10,
  total?: number
): MockApiResponse<T[]> {
  const actualTotal = total ?? data.length
  const totalPages = Math.ceil(actualTotal / limit)

  return {
    success: true,
    data,
    pagination: {
      page,
      limit,
      total: actualTotal,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    },
  }
}

/**
 * Create multiple mock scans
 */
export function createMockScans(count: number, overrides: Partial<MockScan> = {}): MockScan[] {
  return Array.from({ length: count }, (_, index) =>
    createMockScan({
      ...overrides,
      id: `scan_${index + 1}`,
      targetUrl: `https://example${index + 1}.com`,
    })
  )
}

/**
 * Create multiple mock assets
 */
export function createMockAssets(count: number, overrides: Partial<MockAsset> = {}): MockAsset[] {
  return Array.from({ length: count }, (_, index) =>
    createMockAsset({
      ...overrides,
      id: `asset_${index + 1}`,
      url: `https://example${index + 1}.com`,
      domain: `example${index + 1}.com`,
    })
  )
}

/**
 * Create multiple mock vulnerabilities
 */
export function createMockVulnerabilities(count: number, overrides: Partial<MockVulnerability> = {}): MockVulnerability[] {
  return Array.from({ length: count }, (_, index) =>
    createMockVulnerability({
      ...overrides,
      id: `vuln_${index + 1}`,
      name: `Test Vulnerability ${index + 1}`,
    })
  )
}

/**
 * Sleep utility for async tests
 */
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * Mock fetch response
 */
export function createMockFetchResponse<T>(
  data: T,
  status: number = 200,
  statusText: string = 'OK'
): Response {
  return new Response(JSON.stringify(data), {
    status,
    statusText,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

/**
 * Mock fetch error
 */
export function createMockFetchError(message: string = 'Network error'): Promise<never> {
  return Promise.reject(new Error(message))
}

/**
 * Create a mock JWT token
 */
export function createMockJwtToken(payload: any = {}): string {
  const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }))
  const body = btoa(JSON.stringify({
    sub: 'user_123',
    email: '<EMAIL>',
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 3600,
    ...payload,
  }))
  const signature = 'mock_signature'
  
  return `${header}.${body}.${signature}`
}

/**
 * Mock localStorage for tests
 */
export function mockLocalStorage() {
  const store: { [key: string]: string } = {}
  
  return {
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value
    }),
    removeItem: jest.fn((key: string) => {
      delete store[key]
    }),
    clear: jest.fn(() => {
      Object.keys(store).forEach(key => delete store[key])
    }),
  }
}

/**
 * Mock sessionStorage for tests
 */
export function mockSessionStorage() {
  return mockLocalStorage() // Same implementation
}

/**
 * Create a mock file for file upload tests
 */
export function createMockFile(
  name: string = 'test.txt',
  content: string = 'test content',
  type: string = 'text/plain'
): File {
  const blob = new Blob([content], { type })
  return new File([blob], name, { type })
}

/**
 * Wait for element to appear in DOM
 */
export async function waitForElement(
  getElement: () => HTMLElement | null,
  timeout: number = 1000
): Promise<HTMLElement> {
  const startTime = Date.now()
  
  while (Date.now() - startTime < timeout) {
    const element = getElement()
    if (element) {
      return element
    }
    await sleep(50)
  }
  
  throw new Error('Element not found within timeout')
}

/**
 * Mock console methods for testing
 */
export function mockConsole() {
  return {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    info: jest.fn(),
    debug: jest.fn(),
  }
}

/**
 * Jest tests for authentication utilities
 */

// Mock jose module to avoid ES module issues
jest.mock('jose', () => ({
  SignJWT: jest.fn().mockImplementation(() => ({
    setProtectedHeader: jest.fn().mockReturnThis(),
    setIssuedAt: jest.fn().mockReturnThis(),
    setExpirationTime: jest.fn().mockReturnThis(),
    sign: jest.fn().mockResolvedValue('mock-jwt-token'),
  })),
  jwtVerify: jest.fn().mockImplementation((token) => {
    if (token === 'mock-jwt-token') {
      return Promise.resolve({
        payload: {
          userId: 'test-user-id',
          email: '<EMAIL>'
        }
      })
    }
    throw new Error('Invalid token')
  }),
}))

// Mock next/headers
jest.mock('next/headers', () => ({
  cookies: jest.fn(() => ({
    get: jest.fn(),
    set: jest.fn(),
    delete: jest.fn(),
  })),
}))

import { hashPassword, verifyPassword, createToken, verifyToken } from '../auth'

describe('Authentication Utilities', () => {
  describe('Password Hashing', () => {
    it('should hash passwords correctly', async () => {
      const password = 'TestPassword123!'
      const hashedPassword = await hashPassword(password)

      expect(hashedPassword).toBeDefined()
      expect(hashedPassword).not.toBe(password)
      expect(typeof hashedPassword).toBe('string')
    })

    it('should verify correct passwords', async () => {
      const password = 'TestPassword123!'
      const hashedPassword = await hashPassword(password)

      const isValid = await verifyPassword(password, hashedPassword)
      expect(isValid).toBe(true)
    })

    it('should reject incorrect passwords', async () => {
      const password = 'TestPassword123!'
      const hashedPassword = await hashPassword(password)

      const isInvalid = await verifyPassword('WrongPassword', hashedPassword)
      expect(isInvalid).toBe(false)
    })
  })

  describe('JWT Token Management', () => {
    it('should create JWT tokens', async () => {
      const payload = {
        userId: 'test-user-id',
        email: '<EMAIL>'
      }

      const token = await createToken(payload)
      expect(token).toBeDefined()
      expect(typeof token).toBe('string')
      expect(token).toBe('mock-jwt-token')
    })

    it('should verify valid JWT tokens', async () => {
      const payload = {
        userId: 'test-user-id',
        email: '<EMAIL>'
      }

      const token = await createToken(payload)
      const verifiedPayload = await verifyToken(token)

      expect(verifiedPayload).toBeDefined()
      expect(verifiedPayload?.userId).toBe(payload.userId)
      expect(verifiedPayload?.email).toBe(payload.email)
    })

    it('should reject invalid JWT tokens', async () => {
      const invalidToken = await verifyToken('invalid-token')
      expect(invalidToken).toBeNull()
    })
  })
})

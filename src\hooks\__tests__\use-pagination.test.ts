import { renderHook, act } from '@testing-library/react'
import { usePagination } from '../use-pagination'

describe('usePagination', () => {
  it('should initialize with default values', () => {
    const { result } = renderHook(() => usePagination())

    expect(result.current.pagination.page).toBe(1)
    expect(result.current.pagination.limit).toBe(10)
    expect(result.current.getOffset()).toBe(0)
  })

  it('should initialize with custom values', () => {
    const { result } = renderHook(() =>
      usePagination({ initialPage: 3, initialLimit: 20 })
    )

    expect(result.current.pagination.page).toBe(3)
    expect(result.current.pagination.limit).toBe(20)
    expect(result.current.getOffset()).toBe(40)
  })

  it('should update page correctly', () => {
    const { result } = renderHook(() => usePagination())

    // First set the total to allow page 5
    act(() => {
      result.current.setTotal(100)
    })

    // Then set the page
    act(() => {
      result.current.setPage(5)
    })

    expect(result.current.pagination.page).toBe(5)
    expect(result.current.getOffset()).toBe(40)
  })

  it('should update limit correctly', () => {
    const { result } = renderHook(() => usePagination())

    act(() => {
      result.current.setLimit(25)
    })

    expect(result.current.pagination.limit).toBe(25)
    expect(result.current.getOffset()).toBe(0) // Should reset to first page
    expect(result.current.pagination.page).toBe(1)
  })

  it('should go to next page', () => {
    const { result } = renderHook(() => usePagination())

    // Set total to allow multiple pages
    act(() => {
      result.current.setTotal(50)
    })

    act(() => {
      result.current.nextPage()
    })

    expect(result.current.pagination.page).toBe(2)
    expect(result.current.getOffset()).toBe(10)
  })

  it('should go to previous page', () => {
    const { result } = renderHook(() => usePagination({ initialPage: 3, total: 50 }))

    act(() => {
      result.current.prevPage()
    })

    expect(result.current.pagination.page).toBe(2)
    expect(result.current.getOffset()).toBe(10)
  })

  it('should not go below page 1', () => {
    const { result } = renderHook(() => usePagination())

    act(() => {
      result.current.prevPage()
    })

    expect(result.current.pagination.page).toBe(1)
    expect(result.current.getOffset()).toBe(0)
  })

  it('should reset to first page', () => {
    const { result } = renderHook(() => usePagination({ initialPage: 5, total: 100 }))

    // First change to a different page
    act(() => {
      result.current.setPage(3)
    })

    // Then reset
    act(() => {
      result.current.reset()
    })

    expect(result.current.pagination.page).toBe(5) // Should reset to initialPage
    expect(result.current.getOffset()).toBe(40) // (5-1) * 10
  })

  it('should calculate total pages correctly', () => {
    const { result } = renderHook(() => usePagination())

    act(() => {
      result.current.setTotal(95)
    })

    expect(result.current.pagination.pages).toBe(10)
  })

  it('should handle zero total correctly', () => {
    const { result } = renderHook(() => usePagination())

    act(() => {
      result.current.setTotal(0)
    })

    expect(result.current.pagination.pages).toBe(1)
  })

  it('should check if has next page correctly', () => {
    const { result } = renderHook(() => usePagination())

    act(() => {
      result.current.setTotal(25)
      result.current.setPage(2)
    })

    expect(result.current.canNextPage).toBe(true)

    act(() => {
      result.current.setPage(3)
    })

    expect(result.current.canNextPage).toBe(false)
  })

  it('should check if has previous page correctly', () => {
    const { result } = renderHook(() => usePagination())

    expect(result.current.canPrevPage).toBe(false)

    // Set total to allow multiple pages
    act(() => {
      result.current.setTotal(50)
    })

    act(() => {
      result.current.setPage(2)
    })

    expect(result.current.canPrevPage).toBe(true)
  })

  it('should provide pagination info object', () => {
    const { result } = renderHook(() => usePagination())

    act(() => {
      result.current.setTotal(100)
    })

    act(() => {
      result.current.setPage(3)
    })

    const paginationInfo = result.current.pagination

    expect(paginationInfo).toEqual({
      page: 3,
      limit: 10,
      total: 100,
      pages: 10,
    })
    expect(result.current.canNextPage).toBe(true)
    expect(result.current.canPrevPage).toBe(true)
    expect(result.current.getOffset()).toBe(20)
  })

  it('should handle page bounds correctly when total changes', () => {
    const { result } = renderHook(() => usePagination())

    act(() => {
      result.current.setTotal(100)
    })

    act(() => {
      result.current.setPage(10)
    })

    expect(result.current.pagination.page).toBe(10)

    // Reduce total so current page is out of bounds
    act(() => {
      result.current.setTotal(50)
    })

    // Should adjust to last valid page
    expect(result.current.pagination.page).toBe(5)
  })

  it('should handle navigation correctly', () => {
    const { result } = renderHook(() => usePagination())

    act(() => {
      result.current.setTotal(100)
    })

    act(() => {
      result.current.setPage(3)
    })

    expect(result.current.pagination.page).toBe(3)
    expect(result.current.canNextPage).toBe(true)
    expect(result.current.canPrevPage).toBe(true)
    expect(result.current.getOffset()).toBe(20)
  })

  it('should reset pagination correctly', () => {
    const { result } = renderHook(() => usePagination({ initialPage: 2, initialLimit: 20 }))

    act(() => {
      result.current.setTotal(100)
      result.current.setPage(5)
      result.current.setLimit(50)
    })

    act(() => {
      result.current.reset()
    })

    expect(result.current.pagination.page).toBe(2)
    expect(result.current.pagination.limit).toBe(20)
    expect(result.current.pagination.total).toBe(0)
  })
})

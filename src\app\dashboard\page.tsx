/**
 * Dashboard Page - Main Security Overview
 *
 * The primary dashboard page that provides a comprehensive overview of the
 * organization's security posture. Displays real-time metrics, trends,
 * and actionable insights for security teams.
 *
 * Key Features:
 * - Real-time security metrics and KPIs
 * - Interactive charts and visualizations
 * - Recent scan activity monitoring
 * - Top vulnerable assets identification
 * - Security posture scoring and trends
 * - Quick action buttons for common tasks
 *
 * Data Sources:
 * - Vulnerability scan results
 * - Asset inventory data
 * - Scan history and trends
 * - Risk assessment calculations
 *
 * Charts and Visualizations:
 * - Vulnerability severity distribution (doughnut chart)
 * - Scan activity timeline (line chart)
 * - Asset risk distribution (bar chart)
 * - Security posture trends (line chart)
 *
 * <AUTHOR> Scanner Team
 * @version 1.0.0
 */

'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/auth-context'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js'
import { Line, Bar, Doughnut } from 'react-chartjs-2'
import {
  Shield,
  Search,
  Database,
  Clock,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Activity,
  Target,
  Zap,
  Eye
} from 'lucide-react'
import { PageContainer, PageHeader } from '@/components/layout'
import { Card, SeverityBadge, ScanStatusBadge } from '@/components/ui'
import Link from 'next/link'

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
)

interface DashboardStats {
  overview: {
    totalAssets: number
    totalScans: number
    totalVulnerabilities: number
    activeScans: number
    completedScans: number
    failedScans: number
    criticalVulns: number
    highVulns: number
    mediumVulns: number
    lowVulns: number
  }
  vulnerabilitySeverityCount: Record<string, number>
  scanStatusCount: Record<string, number>
  recentScans: any[]
  topVulnerableAssets: any[]
  activityData: any[]
  vulnerabilityTrends: any[]
  riskScore: number
  securityPosture: {
    score: number
    trend: 'up' | 'down' | 'stable'
    recommendations: string[]
  }
}

export default function DashboardPage() {
  const { user } = useAuth()
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchDashboardStats()
  }, [])

  const fetchDashboardStats = async () => {
    try {
      const response = await fetch('/api/dashboard/stats')
      if (response.ok) {
        const data = await response.json()
        setStats(data)
      }
    } catch (error) {
      console.error('Failed to fetch dashboard stats:', error)
    } finally {
      setLoading(false)
    }
  }

  if (!user) return null

  if (loading) {
    return (
      <PageContainer>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </PageContainer>
    )
  }

  // Chart configurations
  const severityColors = {
    CRITICAL: '#dc2626',
    HIGH: '#ea580c',
    MEDIUM: '#d97706',
    LOW: '#2563eb',
    INFO: '#059669',
    UNKNOWN: '#6b7280'
  }

  // Vulnerability severity doughnut chart data
  const severityChartData = {
    labels: Object.keys(stats?.vulnerabilitySeverityCount || {}),
    datasets: [
      {
        data: Object.values(stats?.vulnerabilitySeverityCount || {}),
        backgroundColor: Object.keys(stats?.vulnerabilitySeverityCount || {}).map(
          severity => severityColors[severity as keyof typeof severityColors]
        ),
        borderWidth: 2,
        borderColor: '#ffffff',
        hoverBorderWidth: 3,
      },
    ],
  }

  // Activity line chart data
  const activityChartData = {
    labels: stats?.activityData?.map(item =>
      new Date(item.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
    ) || [],
    datasets: [
      {
        label: 'Scans',
        data: stats?.activityData?.map(item => item.scans) || [],
        borderColor: '#3b82f6',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
        fill: true,
      },
      {
        label: 'Vulnerabilities',
        data: stats?.activityData?.map(item => item.vulnerabilities) || [],
        borderColor: '#ef4444',
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        tension: 0.4,
        fill: true,
      },
    ],
  }

  // Scan status bar chart data
  const scanStatusChartData = {
    labels: Object.keys(stats?.scanStatusCount || {}),
    datasets: [
      {
        label: 'Scans',
        data: Object.values(stats?.scanStatusCount || {}),
        backgroundColor: [
          '#f59e0b', // PENDING
          '#3b82f6', // RUNNING
          '#10b981', // COMPLETED
          '#ef4444', // FAILED
          '#6b7280', // CANCELLED
        ],
        borderRadius: 6,
        borderSkipped: false,
      },
    ],
  }

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
        labels: {
          padding: 20,
          usePointStyle: true,
        },
      },
    },
  }

  return (
    <PageContainer maxWidth="2xl">
      <PageHeader
        title={`Welcome back, ${user.firstName}!`}
        description="Security overview and vulnerability scanning dashboard"
      />

      <div className="space-y-8">

        {/* Enhanced Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Assets</p>
                <p className="text-3xl font-bold text-gray-900">{stats?.overview.totalAssets || 0}</p>
                <p className="text-xs text-gray-500 mt-1">Protected domains</p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <Database className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Scans</p>
                <p className="text-3xl font-bold text-gray-900">{stats?.overview.totalScans || 0}</p>
                <p className="text-xs text-green-600 mt-1">
                  <TrendingUp className="inline h-3 w-3 mr-1" />
                  {stats?.overview.completedScans || 0} completed
                </p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <Search className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Vulnerabilities</p>
                <p className="text-3xl font-bold text-gray-900">{stats?.overview.totalVulnerabilities || 0}</p>
                <div className="flex space-x-2 mt-1">
                  <span className="text-xs text-red-600">{stats?.overview.criticalVulns || 0} critical</span>
                  <span className="text-xs text-orange-600">{stats?.overview.highVulns || 0} high</span>
                </div>
              </div>
              <div className="p-3 bg-red-100 rounded-full">
                <Shield className="h-6 w-6 text-red-600" />
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Scans</p>
                <p className="text-3xl font-bold text-gray-900">{stats?.overview.activeScans || 0}</p>
                <p className="text-xs text-yellow-600 mt-1">
                  <Activity className="inline h-3 w-3 mr-1" />
                  Currently running
                </p>
              </div>
              <div className="p-3 bg-yellow-100 rounded-full">
                <Clock className="h-6 w-6 text-yellow-600" />
              </div>
            </div>
          </Card>
        </div>

        {/* Enhanced Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Vulnerability Severity Distribution */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Shield className="h-5 w-5 mr-2 text-red-500" />
              Vulnerability Severity
            </h3>
            {Object.values(stats?.vulnerabilitySeverityCount || {}).some(v => v > 0) ? (
              <div className="h-64">
                <Doughnut data={severityChartData} options={chartOptions} />
              </div>
            ) : (
              <div className="flex items-center justify-center h-64 text-gray-500">
                <div className="text-center">
                  <Shield className="mx-auto h-12 w-12 text-gray-300 mb-2" />
                  <p>No vulnerabilities found</p>
                </div>
              </div>
            )}
          </Card>

          {/* Scan Activity Trend */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <TrendingUp className="h-5 w-5 mr-2 text-blue-500" />
              Activity Trend
            </h3>
            {stats?.activityData && stats.activityData.length > 0 ? (
              <div className="h-64">
                <Line data={activityChartData} options={chartOptions} />
              </div>
            ) : (
              <div className="flex items-center justify-center h-64 text-gray-500">
                <div className="text-center">
                  <Activity className="mx-auto h-12 w-12 text-gray-300 mb-2" />
                  <p>No activity data</p>
                </div>
              </div>
            )}
          </Card>

          {/* Scan Status Distribution */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Target className="h-5 w-5 mr-2 text-green-500" />
              Scan Status
            </h3>
            {Object.values(stats?.scanStatusCount || {}).some(v => v > 0) ? (
              <div className="h-64">
                <Bar data={scanStatusChartData} options={chartOptions} />
              </div>
            ) : (
              <div className="flex items-center justify-center h-64 text-gray-500">
                <div className="text-center">
                  <Search className="mx-auto h-12 w-12 text-gray-300 mb-2" />
                  <p>No scan data</p>
                </div>
              </div>
            )}
          </Card>
        </div>

        {/* Recent Activity & Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Scans */}
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <Clock className="h-5 w-5 mr-2 text-blue-500" />
                Recent Scans
              </h3>
              <Link href="/dashboard/scans">
                <button className="text-sm text-blue-600 hover:text-blue-800 flex items-center">
                  View all <Eye className="h-4 w-4 ml-1" />
                </button>
              </Link>
            </div>
            {stats?.recentScans && stats.recentScans.length > 0 ? (
              <div className="space-y-3">
                {stats.recentScans.slice(0, 5).map((scan) => (
                  <div key={scan.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {scan.asset?.title || scan.targetUrl}
                      </p>
                      <p className="text-xs text-gray-500">{scan.asset?.domain}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <ScanStatusBadge status={scan.status} size="sm" />
                      <div className="text-xs text-gray-500">
                        {new Date(scan.createdAt).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Search className="mx-auto h-12 w-12 text-gray-300 mb-3" />
                <h3 className="text-sm font-medium text-gray-900 mb-1">No scans yet</h3>
                <p className="text-xs text-gray-500 mb-4">
                  Start by scanning your first website for vulnerabilities.
                </p>
                <Link href="/dashboard/scan">
                  <button className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <Zap className="h-4 w-4 mr-2" />
                    Start Scan
                  </button>
                </Link>
              </div>
            )}
          </Card>

          {/* Top Vulnerable Assets */}
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <AlertTriangle className="h-5 w-5 mr-2 text-orange-500" />
                Top Vulnerable Assets
              </h3>
              <Link href="/dashboard/assets">
                <button className="text-sm text-blue-600 hover:text-blue-800 flex items-center">
                  View all <Eye className="h-4 w-4 ml-1" />
                </button>
              </Link>
            </div>
            {stats?.topVulnerableAssets && stats.topVulnerableAssets.length > 0 ? (
              <div className="space-y-3">
                {stats.topVulnerableAssets.slice(0, 5).map((asset) => (
                  <div key={asset.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">{asset.title}</p>
                      <p className="text-xs text-gray-500">{asset.domain}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        {asset._count.vulnerabilities} vulns
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <CheckCircle className="mx-auto h-12 w-12 text-green-300 mb-3" />
                <h3 className="text-sm font-medium text-gray-900 mb-1">All assets secure</h3>
                <p className="text-xs text-gray-500">No vulnerabilities found in your assets.</p>
              </div>
            )}
          </Card>
        </div>
      </div>
    </PageContainer>
  )
}

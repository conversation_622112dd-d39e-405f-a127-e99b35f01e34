import { render, screen, fireEvent, cleanup } from '@testing-library/react'
import { DataTable } from '../data-table'
import { createMockScan, createMockApiResponse } from '../../../tests/utils/test-helpers'

// Mock data
const mockScans = [
  createMockScan({ id: '1', targetUrl: 'https://example1.com', status: 'COMPLETED' }),
  createMockScan({ id: '2', targetUrl: 'https://example2.com', status: 'RUNNING' }),
  createMockScan({ id: '3', targetUrl: 'https://example3.com', status: 'FAILED' }),
]

const mockColumns = [
  {
    key: 'targetUrl',
    header: 'Target URL',
    render: (scan: any) => scan.targetUrl,
  },
  {
    key: 'status',
    header: 'Status',
    render: (scan: any) => scan.status,
  },
  {
    key: 'totalVulns',
    header: 'Vulnerabilities',
    render: (scan: any) => scan.totalVulns.toString(),
  },
]

const mockPagination = {
  page: 1,
  pages: 3,
  total: 25,
  limit: 10,
}

describe('DataTable Component', () => {
  afterEach(() => {
    cleanup()
  })

  it('renders table with data', () => {
    render(
      <DataTable
        data={mockScans}
        columns={mockColumns}
        pagination={{
          ...mockPagination,
          onPageChange: jest.fn()
        }}
      />
    )

    // Check headers
    expect(screen.getByText('Target URL')).toBeInTheDocument()
    expect(screen.getByText('Status')).toBeInTheDocument()
    expect(screen.getByText('Vulnerabilities')).toBeInTheDocument()

    // Check data
    expect(screen.getByText('https://example1.com')).toBeInTheDocument()
    expect(screen.getByText('https://example2.com')).toBeInTheDocument()
    expect(screen.getByText('COMPLETED')).toBeInTheDocument()
    expect(screen.getByText('RUNNING')).toBeInTheDocument()
  })

  it('shows loading state', () => {
    render(
      <DataTable
        data={[]}
        columns={mockColumns}
        pagination={{
          ...mockPagination,
          onPageChange: jest.fn()
        }}
        loading={true}
      />
    )

    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
    expect(screen.getByRole('status')).toBeInTheDocument()
  })

  it('shows empty state when no data', () => {
    render(
      <DataTable
        data={[]}
        columns={mockColumns}
        pagination={{
          ...mockPagination,
          total: 0,
          onPageChange: jest.fn()
        }}
        emptyState={{
          title: 'No data found',
          description: 'There are no items to display'
        }}
      />
    )

    expect(screen.getByText('No data found')).toBeInTheDocument()
    expect(screen.getByText('There are no items to display')).toBeInTheDocument()
  })

  it('shows custom empty state message', () => {
    const customEmptyMessage = 'No scans available'

    render(
      <DataTable
        data={[]}
        columns={mockColumns}
        pagination={{
          ...mockPagination,
          total: 0,
          onPageChange: jest.fn()
        }}
        emptyState={{
          title: customEmptyMessage,
          description: 'Try creating a new scan'
        }}
      />
    )

    expect(screen.getByText(customEmptyMessage)).toBeInTheDocument()
    expect(screen.getByText('Try creating a new scan')).toBeInTheDocument()
  })

  it('handles pagination correctly', () => {
    const onPageChange = jest.fn()

    render(
      <DataTable
        data={mockScans}
        columns={mockColumns}
        pagination={{
          ...mockPagination,
          onPageChange
        }}
      />
    )

    // Check pagination info
    expect(screen.getByText(/Page 1 of 3/)).toBeInTheDocument()
    expect(screen.getByText(/25 results/)).toBeInTheDocument()

    // Test next page button
    const nextButton = screen.getByRole('button', { name: /next/i })
    fireEvent.click(nextButton)
    expect(onPageChange).toHaveBeenCalledWith(2)

    // Test previous page button (should be disabled on first page)
    const prevButton = screen.getByRole('button', { name: /previous/i })
    expect(prevButton).toBeDisabled()
  })

  it('handles row clicks when onRowClick is provided', () => {
    const onRowClick = jest.fn()
    
    render(
      <DataTable
        data={mockScans}
        columns={mockColumns}
        pagination={mockPagination}
        onPageChange={jest.fn()}
        onRowClick={onRowClick}
      />
    )

    // Click on first row
    const firstRow = screen.getByText('https://example1.com').closest('tr')
    fireEvent.click(firstRow!)
    
    expect(onRowClick).toHaveBeenCalledWith(mockScans[0])
  })

  it('applies custom className', () => {
    const { container } = render(
      <DataTable
        data={mockScans}
        columns={mockColumns}
        pagination={mockPagination}
        onPageChange={jest.fn()}
        className="custom-table"
      />
    )

    expect(container.firstChild).toHaveClass('custom-table')
  })

  it('renders with striped rows when specified', () => {
    render(
      <DataTable
        data={mockScans}
        columns={mockColumns}
        pagination={{
          ...mockPagination,
          onPageChange: jest.fn()
        }}
        striped={true}
      />
    )

    const rows = screen.getAllByRole('row')
    // Skip header row, check data rows
    expect(rows[1]).toHaveClass('bg-gray-50')
    expect(rows[2]).not.toHaveClass('bg-gray-50')
    expect(rows[3]).toHaveClass('bg-gray-50')
  })

  it('renders with hover effect when specified', () => {
    render(
      <DataTable
        data={mockScans}
        columns={mockColumns}
        pagination={{
          ...mockPagination,
          onPageChange: jest.fn()
        }}
      />
    )

    const rows = screen.getAllByRole('row')
    // Check that data rows have hover class
    expect(rows[1]).toHaveClass('hover:bg-gray-50')
  })

  it('handles complex column rendering', () => {
    const complexColumns = [
      {
        key: 'status',
        header: 'Status',
        render: (scan: any) => (
          <span className={`status-${scan.status.toLowerCase()}`}>
            {scan.status}
          </span>
        ),
      },
      {
        key: 'actions',
        header: 'Actions',
        render: (scan: any) => (
          <button data-testid={`action-${scan.id}`}>
            View Details
          </button>
        ),
      },
    ]

    render(
      <DataTable
        data={mockScans}
        columns={complexColumns}
        pagination={mockPagination}
        onPageChange={jest.fn()}
      />
    )

    // Check complex rendering
    expect(screen.getByText('COMPLETED')).toHaveClass('status-completed')
    expect(screen.getByTestId('action-1')).toBeInTheDocument()
    expect(screen.getByTestId('action-2')).toBeInTheDocument()
  })

  it('handles sorting when sortable columns are provided', () => {
    const onSort = jest.fn()
    const sortableColumns = [
      {
        key: 'targetUrl',
        header: 'Target URL',
        render: (scan: any) => scan.targetUrl,
        sortable: true,
      },
      {
        key: 'status',
        header: 'Status',
        render: (scan: any) => scan.status,
        sortable: true,
      },
    ]

    render(
      <DataTable
        data={mockScans}
        columns={sortableColumns}
        pagination={{
          ...mockPagination,
          onPageChange: jest.fn()
        }}
        onSort={onSort}
        sortBy="targetUrl"
        sortOrder="asc"
      />
    )

    // Click on sortable header
    const targetUrlHeader = screen.getByText('Target URL')
    fireEvent.click(targetUrlHeader)
    
    expect(onSort).toHaveBeenCalledWith('targetUrl', 'desc')
  })

  it('shows sort indicators for sorted columns', () => {
    const sortableColumns = [
      {
        key: 'targetUrl',
        header: 'Target URL',
        render: (scan: any) => scan.targetUrl,
        sortable: true,
      },
    ]

    render(
      <DataTable
        data={mockScans}
        columns={sortableColumns}
        pagination={{
          ...mockPagination,
          onPageChange: jest.fn()
        }}
        sortBy="targetUrl"
        sortOrder="asc"
      />
    )

    // Should show sort indicator
    expect(screen.getByTestId('sort-indicator')).toBeInTheDocument()
  })

  it('handles keyboard navigation for accessibility', () => {
    const onRowClick = jest.fn()
    
    render(
      <DataTable
        data={mockScans}
        columns={mockColumns}
        pagination={{
          ...mockPagination,
          onPageChange: jest.fn()
        }}
        onRowClick={onRowClick}
      />
    )

    const firstRow = screen.getByText('https://example1.com').closest('tr')
    
    // Test Enter key
    fireEvent.keyDown(firstRow!, { key: 'Enter', code: 'Enter' })
    expect(onRowClick).toHaveBeenCalledWith(mockScans[0])

    // Test Space key
    fireEvent.keyDown(firstRow!, { key: ' ', code: 'Space' })
    expect(onRowClick).toHaveBeenCalledTimes(2)
  })
})

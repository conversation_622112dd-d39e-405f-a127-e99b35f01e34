// Shared Type Definitions

import { ScanType, Severity, ScanStatus, AssetStatus } from '../constants'

// Database Models
export interface User {
  id: string
  email: string
  createdAt: Date
  updatedAt: Date
}

export interface Asset {
  id: string
  url: string
  domain: string
  status: AssetStatus
  lastScanAt?: Date
  createdAt: Date
  updatedAt: Date
  scans?: Scan[]
  vulnerabilities?: Vulnerability[]
  _count?: {
    scans: number
    vulnerabilities: number
  }
}

export interface Scan {
  id: string
  assetId: string
  status: ScanStatus
  scanType: ScanType
  severity: string
  startedAt: Date
  completedAt?: Date
  duration?: number
  message?: string
  nucleiVersion?: string
  templatesCount?: number
  createdAt: Date
  updatedAt: Date
  asset?: Asset
  vulnerabilities?: Vulnerability[]
  _count?: {
    vulnerabilities: number
  }
}

export interface Vulnerability {
  id: string
  scanId: string
  assetId: string
  templateId: string
  name: string
  severity: Severity
  description?: string
  reference?: string
  classification?: string
  remediation?: string
  request?: string
  response?: string
  curlCommand?: string
  matchedAt?: string
  extractedResults?: string
  createdAt: Date
  updatedAt: Date
  scan?: Scan
  asset?: Asset
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    pages: number
    total: number
    limit: number
  }
}

// Form Types
export interface ScanFormData {
  url: string
  scanType: ScanType
  severity: string
}

export interface AssetFormData {
  url: string
  description?: string
}

// Filter Types
export interface ScanFilters {
  status?: ScanStatus[]
  scanType?: ScanType[]
  search?: string
  dateFrom?: string
  dateTo?: string
}

export interface VulnerabilityFilters {
  severity?: Severity[]
  search?: string
  assetId?: string
  scanId?: string
}

export interface AssetFilters {
  status?: AssetStatus[]
  search?: string
  hasVulnerabilities?: boolean
}

// Chart Data Types
export interface ChartDataPoint {
  name: string
  value: number
  color?: string
}

export interface TimeSeriesDataPoint {
  date: string
  value: number
  label?: string
}

// Export Types
export interface ExportOptions {
  format: 'csv' | 'json' | 'pdf'
  filters?: Record<string, any>
  columns?: string[]
}

// Real-time Types
export interface ScanLog {
  id: string
  timestamp: Date
  level: 'info' | 'warning' | 'error' | 'success'
  message: string
  data?: any
}

export interface ScanProgress {
  scanId: string
  status: ScanStatus
  progress: number
  currentTemplate?: string
  vulnerabilitiesFound: number
  estimatedTimeRemaining?: number
}

// Component Props Types
export interface BaseComponentProps {
  className?: string
  children?: React.ReactNode
}

export interface LoadingProps extends BaseComponentProps {
  loading?: boolean
  error?: string | null
}

export interface TableColumn<T> {
  key: keyof T | string
  header: string
  render?: (item: T) => React.ReactNode
  sortable?: boolean
  className?: string
}

export interface PaginationInfo {
  page: number
  pages: number
  total: number
  limit: number
}

// Utility Types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>

// Event Types
export interface ScanEvent {
  type: 'scan_started' | 'scan_progress' | 'scan_completed' | 'scan_failed' | 'vulnerability_found'
  scanId: string
  data: any
  timestamp: Date
}

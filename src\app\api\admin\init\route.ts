import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth'
import { handleApiError } from '@/lib/errors'
import { initializeServices, areServicesInitialized } from '@/lib/init'
import { jobQueue } from '@/lib/job-queue'
import { db } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {
    // Authentication (you might want to add admin role check here)
    await requireAuth()

    const body = await request.json()
    const { action } = body

    if (action === 'recover-stuck') {
      console.log('🔧 Manual stuck scan recovery requested...')
      await jobQueue.recoverStuckScans()
      return NextResponse.json({
        message: 'Stuck scans recovery completed',
        success: true
      })
    }

    if (action === 'initialize' || !action) {
      console.log('🔧 Manual service initialization requested...')

      // Initialize services
      initializeServices()

      // Get status after initialization
      const status = await jobQueue.getDetailedStatus()

      return NextResponse.json({
        message: 'Services initialized successfully',
        initialized: areServicesInitialized(),
        queueStatus: status
      })
    }

    return NextResponse.json(
      { success: false, error: 'Invalid action' },
      { status: 400 }
    )

  } catch (error) {
    return handleApiError(error)
  }
}

export async function GET(request: NextRequest) {
  try {
    // Authentication
    await requireAuth()

    // Get current status
    const status = await jobQueue.getDetailedStatus()

    // Get recent scans for monitoring
    const recentScans = await db.scan.findMany({
      orderBy: { createdAt: 'desc' },
      take: 20,
      select: {
        id: true,
        targetUrl: true,
        status: true,
        createdAt: true,
        startedAt: true,
        completedAt: true,
        duration: true,
        totalVulns: true,
        errorMessage: true,
        userId: true,
        scanType: true,
        scanMode: true
      }
    })

    // Get running scans with timing info
    const runningScans = await db.scan.findMany({
      where: { status: 'RUNNING' },
      select: {
        id: true,
        targetUrl: true,
        startedAt: true,
        userId: true,
        scanType: true,
        scanMode: true
      }
    })

    // Calculate stuck scans (running for more than 30 minutes)
    const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000)
    const stuckScans = runningScans.filter(scan =>
      scan.startedAt && scan.startedAt < thirtyMinutesAgo
    ).map(scan => ({
      ...scan,
      stuckDuration: scan.startedAt ? Math.round((Date.now() - scan.startedAt.getTime()) / 1000 / 60) : 0
    }))

    return NextResponse.json({
      message: 'Service status retrieved',
      initialized: areServicesInitialized(),
      queueStatus: {
        ...status,
        stuck: stuckScans.length
      },
      recentScans,
      runningScans,
      stuckScans,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    return handleApiError(error)
  }
}

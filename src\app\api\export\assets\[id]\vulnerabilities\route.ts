import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { requireAuth } from '@/lib/auth'
import { handleApiError, NotFoundError, AuthorizationError } from '@/lib/errors'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authentication
    const currentUser = await requireAuth()

    // Await params
    const { id } = await params

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const severity = searchParams.get('severity')
    const search = searchParams.get('search')
    const format = searchParams.get('format') || 'json'

    // Check if asset exists and belongs to user
    const asset = await db.asset.findUnique({
      where: { id },
      select: { 
        id: true, 
        userId: true, 
        title: true, 
        url: true, 
        domain: true,
        description: true,
        status: true,
        lastScanned: true,
        createdAt: true
      }
    })

    if (!asset) {
      throw new NotFoundError('Asset not found')
    }

    if (asset.userId !== currentUser.userId) {
      throw new AuthorizationError('Access denied')
    }

    // Build where clause for vulnerabilities
    const where: any = {
      scan: {
        assetId: id,
        userId: currentUser.userId
      }
    }

    if (severity) {
      where.severity = severity.toUpperCase()
    }

    if (search) {
      where.OR = [
        { name: { contains: search } },
        { templateId: { contains: search } },
        { host: { contains: search } },
        { description: { contains: search } }
      ]
    }

    // Get ALL vulnerabilities for this asset
    const vulnerabilities = await db.vulnerability.findMany({
      where,
      include: {
        scan: {
          select: {
            id: true,
            targetUrl: true
          }
        }
      },
      orderBy: [
        { severity: 'desc' },
        { timestamp: 'desc' }
      ]
    })

    // Get asset scans for additional context
    const scans = await db.scan.findMany({
      where: {
        assetId: id,
        userId: currentUser.userId
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    const exportData = {
      asset,
      vulnerabilities,
      scans,
      statistics: {
        totalVulnerabilities: vulnerabilities.length,
        severityBreakdown: {
          critical: vulnerabilities.filter(v => v.severity === 'CRITICAL').length,
          high: vulnerabilities.filter(v => v.severity === 'HIGH').length,
          medium: vulnerabilities.filter(v => v.severity === 'MEDIUM').length,
          low: vulnerabilities.filter(v => v.severity === 'LOW').length,
          info: vulnerabilities.filter(v => v.severity === 'INFO').length,
          unknown: vulnerabilities.filter(v => v.severity === 'UNKNOWN').length
        }
      },
      exportedAt: new Date().toISOString(),
      filters: {
        severity,
        search
      }
    }

    if (format === 'csv') {
      const csvHeaders = ['Vulnerability', 'Severity', 'Host', 'Template ID', 'Scan ID', 'Timestamp']
      const csvRows = vulnerabilities.map(vuln => [
        vuln.name,
        vuln.severity,
        vuln.host,
        vuln.templateId,
        vuln.scan.id,
        new Date(vuln.timestamp).toLocaleString()
      ])

      const csvContent = [csvHeaders, ...csvRows]
        .map(row => row.map(field => `"${field}"`).join(','))
        .join('\n')

      return new NextResponse(csvContent, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="asset-${asset.id}-vulnerabilities-${new Date().toISOString().split('T')[0]}.csv"`
        }
      })
    }

    // Default to JSON
    return NextResponse.json(exportData)

  } catch (error) {
    return handleApiError(error)
  }
}

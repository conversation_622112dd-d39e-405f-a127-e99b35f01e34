#!/usr/bin/env node

/**
 * Test Setup Validation Script
 * 
 * This script validates that the testing framework is properly configured
 * and all test dependencies are working correctly.
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

console.log('🧪 CTB Scanner - Test Setup Validation\n')

const checks = [
  {
    name: 'Jest Configuration',
    check: () => fs.existsSync('jest.config.js'),
    fix: 'Create jest.config.js file'
  },
  {
    name: 'Jest Setup File',
    check: () => fs.existsSync('jest.setup.js'),
    fix: 'Create jest.setup.js file'
  },
  {
    name: 'Playwright Configuration',
    check: () => fs.existsSync('playwright.config.ts'),
    fix: 'Create playwright.config.ts file'
  },
  {
    name: 'Test Environment File',
    check: () => fs.existsSync('.env.test'),
    fix: 'Create .env.test file'
  },
  {
    name: 'Testing Documentation',
    check: () => fs.existsSync('TESTING.md'),
    fix: 'Create TESTING.md file'
  },
  {
    name: 'Component Tests Directory',
    check: () => fs.existsSync('src/components/atoms/__tests__'),
    fix: 'Create component test directories'
  },
  {
    name: 'Integration Tests Directory',
    check: () => fs.existsSync('tests/integration'),
    fix: 'Create tests/integration directory'
  },
  {
    name: 'E2E Tests Directory',
    check: () => fs.existsSync('e2e'),
    fix: 'Create e2e directory'
  },
  {
    name: 'Test Utilities',
    check: () => fs.existsSync('tests/utils/test-helpers.ts'),
    fix: 'Create test utilities'
  },
  {
    name: 'MSW Setup',
    check: () => fs.existsSync('tests/setup/msw.setup.ts'),
    fix: 'Create MSW setup file'
  }
]

const dependencyChecks = [
  {
    name: 'Jest',
    package: 'jest'
  },
  {
    name: 'React Testing Library',
    package: '@testing-library/react'
  },
  {
    name: 'Jest DOM',
    package: '@testing-library/jest-dom'
  },
  {
    name: 'User Event',
    package: '@testing-library/user-event'
  },
  {
    name: 'Playwright',
    package: '@playwright/test'
  },
  {
    name: 'Supertest',
    package: 'supertest'
  },
  {
    name: 'MSW',
    package: 'msw'
  },
  {
    name: 'TypeScript Jest',
    package: 'ts-jest'
  }
]

const scriptChecks = [
  'test',
  'test:watch',
  'test:coverage',
  'test:unit',
  'test:integration',
  'test:e2e',
  'test:all'
]

let allPassed = true

// Check file structure
console.log('📁 Checking File Structure...')
checks.forEach(check => {
  const passed = check.check()
  const status = passed ? '✅' : '❌'
  console.log(`${status} ${check.name}`)
  
  if (!passed) {
    console.log(`   Fix: ${check.fix}`)
    allPassed = false
  }
})

console.log()

// Check dependencies
console.log('📦 Checking Dependencies...')
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
const allDependencies = {
  ...packageJson.dependencies,
  ...packageJson.devDependencies
}

dependencyChecks.forEach(check => {
  const installed = allDependencies[check.package]
  const status = installed ? '✅' : '❌'
  console.log(`${status} ${check.name} ${installed ? `(${installed})` : '(not installed)'}`)
  
  if (!installed) {
    allPassed = false
  }
})

console.log()

// Check npm scripts
console.log('🔧 Checking NPM Scripts...')
const scripts = packageJson.scripts || {}

scriptChecks.forEach(script => {
  const exists = scripts[script]
  const status = exists ? '✅' : '❌'
  console.log(`${status} npm run ${script}`)
  
  if (!exists) {
    allPassed = false
  }
})

console.log()

// Test basic functionality
console.log('🧪 Testing Basic Functionality...')

try {
  console.log('Testing Jest configuration...')
  execSync('npx jest --version', { stdio: 'pipe' })
  console.log('✅ Jest is working')
} catch (error) {
  console.log('❌ Jest configuration issue')
  allPassed = false
}

try {
  console.log('Testing TypeScript compilation...')
  execSync('npx tsc --noEmit', { stdio: 'pipe' })
  console.log('✅ TypeScript compilation successful')
} catch (error) {
  console.log('❌ TypeScript compilation issues found')
  allPassed = false
}

try {
  console.log('Testing Playwright installation...')
  execSync('npx playwright --version', { stdio: 'pipe' })
  console.log('✅ Playwright is working')
} catch (error) {
  console.log('❌ Playwright not properly installed')
  console.log('   Run: npm run playwright:install')
  allPassed = false
}

console.log()

// Summary
if (allPassed) {
  console.log('🎉 All checks passed! Testing framework is properly configured.')
  console.log()
  console.log('Next steps:')
  console.log('1. Run: npm run test:unit')
  console.log('2. Run: npm run test:integration')
  console.log('3. Run: npm run test:e2e')
  console.log('4. Run: npm run test:coverage')
  console.log()
  console.log('For more information, see TESTING.md')
} else {
  console.log('❌ Some checks failed. Please fix the issues above.')
  console.log()
  console.log('Common fixes:')
  console.log('1. Install missing dependencies: npm install')
  console.log('2. Install Playwright browsers: npm run playwright:install')
  console.log('3. Check file structure and create missing files')
  console.log('4. Review TESTING.md for detailed setup instructions')
  process.exit(1)
}

// Additional recommendations
console.log()
console.log('💡 Recommendations:')
console.log('• Set up pre-commit hooks with Husky for automatic testing')
console.log('• Configure IDE extensions for Jest and Playwright')
console.log('• Set up continuous integration with GitHub Actions')
console.log('• Consider adding visual regression testing')
console.log('• Implement test coverage reporting in CI/CD')

console.log()
console.log('📚 Resources:')
console.log('• Jest Documentation: https://jestjs.io/docs/getting-started')
console.log('• React Testing Library: https://testing-library.com/docs/react-testing-library/intro/')
console.log('• Playwright Documentation: https://playwright.dev/docs/intro')
console.log('• Testing Best Practices: https://kentcdodds.com/blog/common-mistakes-with-react-testing-library')

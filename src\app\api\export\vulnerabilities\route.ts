import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { requireAuth } from '@/lib/auth'
import { handleApiError } from '@/lib/errors'

export async function GET(request: NextRequest) {
  try {
    // Authentication
    const currentUser = await requireAuth()

    // Get query parameters for filtering (but no pagination)
    const { searchParams } = new URL(request.url)
    const severity = searchParams.get('severity')
    const search = searchParams.get('search')
    const format = searchParams.get('format') || 'json'

    // Build where clause
    const where: any = {
      scan: {
        userId: currentUser.userId
      }
    }

    if (severity) {
      where.severity = severity.toUpperCase()
    }

    if (search) {
      where.OR = [
        { name: { contains: search } },
        { templateId: { contains: search } },
        { host: { contains: search } },
        { description: { contains: search } }
      ]
    }

    // Get ALL vulnerabilities without pagination
    const vulnerabilities = await db.vulnerability.findMany({
      where,
      include: {
        scan: {
          select: {
            id: true,
            targetUrl: true,
            asset: {
              select: {
                id: true,
                title: true,
                domain: true
              }
            }
          }
        }
      },
      orderBy: [
        { severity: 'desc' },
        { timestamp: 'desc' }
      ]
    })

    const exportData = {
      vulnerabilities,
      exportedAt: new Date().toISOString(),
      totalCount: vulnerabilities.length,
      filters: {
        severity,
        search
      }
    }

    if (format === 'csv') {
      const csvHeaders = ['Name', 'Severity', 'Host', 'Template ID', 'Asset', 'Scan ID', 'Timestamp']
      const csvRows = vulnerabilities.map((vuln: any) => [
        vuln.name,
        vuln.severity,
        vuln.host,
        vuln.templateId,
        vuln.scan.asset?.title || 'Unknown',
        vuln.scan.id,
        new Date(vuln.timestamp).toLocaleString()
      ])

      const csvContent = [csvHeaders, ...csvRows]
        .map(row => row.map((field: any) => `"${field}"`).join(','))
        .join('\n')

      return new NextResponse(csvContent, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="vulnerabilities-export-${new Date().toISOString().split('T')[0]}.csv"`
        }
      })
    }

    // Default to JSON
    return NextResponse.json(exportData)

  } catch (error) {
    return handleApiError(error)
  }
}

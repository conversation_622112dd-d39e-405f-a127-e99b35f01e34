<div align="center">

# 🛡️ CTB Scanner

**Enterprise-Grade Vulnerability Scanning Platform**

[![Next.js](https://img.shields.io/badge/Next.js-15.4.4-black?style=for-the-badge&logo=next.js)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue?style=for-the-badge&logo=typescript)](https://www.typescriptlang.org/)
[![Prisma](https://img.shields.io/badge/Prisma-5.0-2D3748?style=for-the-badge&logo=prisma)](https://www.prisma.io/)
[![MySQL](https://img.shields.io/badge/MySQL-8.0-4479A1?style=for-the-badge&logo=mysql&logoColor=white)](https://www.mysql.com/)
[![Nuclei](https://img.shields.io/badge/Nuclei-3.0-00D4AA?style=for-the-badge&logo=nuclei)](https://nuclei.projectdiscovery.io/)

[![Test Coverage](https://img.shields.io/badge/Coverage-100%25-brightgreen?style=for-the-badge)](./docs/TESTING.md)
[![Production Ready](https://img.shields.io/badge/Production-Ready-success?style=for-the-badge)](./docs/DEPLOYMENT.md)
[![License](https://img.shields.io/badge/License-Proprietary-yellow?style=for-the-badge)](#-license)

*A comprehensive, production-ready vulnerability scanning platform with real-time scanning, enterprise security, and atomic design system.*

[🚀 Quick Start](#-quick-start) • [📖 Documentation](#-documentation) • [🏗️ Architecture](#️-architecture) • [🤝 Contributing](#-contributing)

</div>

---

## ✨ Features

<table>
<tr>
<td width="50%">

### 🔍 **Vulnerability Scanning**
- **Real-time scanning** with Nuclei integration
- **Comprehensive templates** (info to critical severity)
- **Asset inventory** management
- **PDF report generation**
- **Scan history** and analytics

### 🛡️ **Enterprise Security**
- **JWT authentication** with secure sessions
- **Rate limiting** and DDoS protection
- **Input validation** and sanitization
- **CORS and CSP** headers
- **SQL injection** prevention

</td>
<td width="50%">

### 🏗️ **Modern Architecture**
- **Next.js 15** with App Router
- **TypeScript** strict mode
- **Atomic design** system
- **Prisma ORM** with MySQL
- **100% test coverage**

### 🚀 **Production Ready**
- **Docker** containerization
- **CI/CD** pipeline support
- **Error tracking** and monitoring
- **Graceful shutdown** handling
- **Auto-recovery** mechanisms

</td>
</tr>
</table>

## 🚀 Quick Start

```bash
# Clone the repository (private organization)
git clone https://github.com/your-private-org/ctb-scanner.git
cd ctb-scanner

# Install dependencies
npm install

# Setup environment
cp .env.example .env.local
# Edit .env.local with your configuration

# Setup database
npx prisma migrate dev
npx prisma generate

# Start development server
npm run dev
```

Visit [http://localhost:3000](http://localhost:3000) to see the application.

## 📖 Documentation

<table>
<tr>
<td width="33%">

### 🏗️ **Architecture**
- [📊 Database Schema](./docs/DATABASE.md)
- [🔧 API Design](./docs/API.md)
- [🎨 Component Guide](./docs/COMPONENT_GUIDE.md)
- [🛡️ Security Architecture](./docs/SECURITY.md)

</td>
<td width="33%">

### 🚀 **Deployment**
- [🐳 Docker Setup](./docs/DEPLOYMENT.md)
- [⚙️ Environment Config](./docs/ENVIRONMENT.md)
- [🔧 Development Setup](./docs/DEVELOPMENT.md)
- [🐛 Troubleshooting](./docs/TROUBLESHOOTING.md)

</td>
<td width="33%">

### 🧪 **Testing & Quality**
- [✅ Testing Guide](./docs/TESTING.md)
- [📈 Performance](./docs/PERFORMANCE.md)
- [🔍 Nuclei Templates](./docs/NUCLEI_TEMPLATE_DESIGN.md)
- [📝 Contributing](./docs/CONTRIBUTING.md)

</td>
</tr>
</table>

## 🏗️ Architecture

<div align="center">

```mermaid
graph TB
    A[Client Browser] --> B[Next.js Frontend]
    B --> C[API Routes]
    C --> D[Authentication Layer]
    C --> E[Rate Limiting]
    C --> F[Validation Layer]
    F --> G[Business Logic]
    G --> H[Database Layer]
    G --> I[Nuclei Scanner]
    H --> J[(MySQL Database)]
    I --> K[Vulnerability Reports]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style J fill:#fff3e0
    style I fill:#fce4ec
```

</div>

### Core Components

| Component | Description | Documentation |
|-----------|-------------|---------------|
| 🎨 **Frontend** | Next.js 15 with atomic design system | [Component Guide](./docs/COMPONENT_GUIDE.md) |
| 🔌 **API Layer** | RESTful APIs with comprehensive validation | [API Documentation](./docs/API.md) |
| 🗄️ **Database** | MySQL with Prisma ORM | [Database Schema](./docs/DATABASE.md) |
| 🔍 **Scanner** | Nuclei integration for vulnerability scanning | [Nuclei Guide](./docs/NUCLEI_TEMPLATE_DESIGN.md) |
| 🛡️ **Security** | Multi-layer security with JWT and rate limiting | [Security Guide](./docs/SECURITY.md) |

## 🛠️ Tech Stack

<div align="center">

| Category | Technologies |
|----------|-------------|
| **Frontend** | Next.js 15, React 18, TypeScript, Tailwind CSS |
| **Backend** | Node.js, Next.js API Routes, Prisma ORM |
| **Database** | MySQL 8.0, Redis (caching) |
| **Security** | JWT, bcrypt, Rate limiting, CORS, CSP |
| **Scanner** | Nuclei 3.0, Custom templates |
| **Testing** | Jest, React Testing Library, Playwright |
| **DevOps** | Docker, GitHub Actions, ESLint, Prettier |

</div>

## 📁 Project Structure

For detailed project structure and organization, see [Development Guide](./docs/DEVELOPMENT.md).

## 🎯 Usage Guide

For detailed usage instructions, scanning best practices, and security considerations, see [Development Guide](./docs/DEVELOPMENT.md).

## 🎨 Component Architecture

For comprehensive component documentation and UI guidelines, see [Component Guide](./docs/COMPONENT_GUIDE.md).

## 🚀 Deployment

For production deployment, Docker setup, and scaling considerations, see [Deployment Guide](./docs/DEPLOYMENT.md).

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](./docs/CONTRIBUTING.md) for details.

## 📝 License

This project is proprietary software owned by [Your Organization Name]. All rights reserved.

## 🆘 Support

For support, please contact the development team or open an internal issue in the organization's repository.

---

<div align="center">

**Built with ❤️ by the CTB Scanner Team**

*Securing digital assets through comprehensive vulnerability scanning*

</div>

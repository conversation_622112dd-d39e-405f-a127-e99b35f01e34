import React, { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { 
  Globe, 
  Zap, 
  Network, 
  FileText, 
  Layers, 
  Target, 
  List,
  Shield,
  Clock,
  Cpu
} from 'lucide-react'

// Types
type ScanType = 'web-api' | 'network'
type ScanMode = 'basic' | 'advanced'
type InputType = 'single' | 'bulk'

interface ScanUrlInput {
  target: string
  scanType: ScanType
  scanMode: ScanMode
  inputType: InputType
  severity: ('critical' | 'high' | 'medium' | 'low' | 'info' | 'unknown')[]
  url?: string
}

interface DynamicScanFormProps {
  onSubmit: (data: ScanUrlInput) => Promise<void>
  isLoading?: boolean
  error?: string | null
  onReset?: () => void
  showResetButton?: boolean
  className?: string
}

import { z } from 'zod'

// Zod schema for form validation
const scanUrlSchema = z.object({
  target: z.string().min(1, 'Target is required'),
  scanType: z.enum(['web-api', 'network']),
  scanMode: z.enum(['basic', 'advanced']),
  inputType: z.enum(['single', 'bulk']),
  severity: z.array(z.enum(['critical', 'high', 'medium', 'low', 'info', 'unknown'])),
  url: z.string().optional(),
})

interface LoadingSpinnerProps {
  size?: 'sm' | 'lg'
  className?: string
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ size, className }) => (
  <div className={`animate-spin rounded-full border-2 border-white border-t-transparent ${size === 'sm' ? 'w-4 h-4' : 'w-6 h-6'} ${className || ''}`}></div>
)

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'outline' | 'default'
  children: React.ReactNode
}

const Button: React.FC<ButtonProps> = ({ children, className, disabled, type, onClick, variant, ...props }) => (
  <button 
    type={type} 
    disabled={disabled} 
    onClick={onClick}
    className={`inline-flex items-center justify-center transition-all duration-200 ${className || ''} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
    {...props}
  >
    {children}
  </button>
)

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {}

const Input: React.FC<InputProps> = ({ className, disabled, ...props }) => (
  <input 
    className={`w-full transition-all duration-200 ${className || ''} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
    disabled={disabled}
    {...props}
  />
)

interface LabelProps extends React.LabelHTMLAttributes<HTMLLabelElement> {
  children: React.ReactNode
}

const Label: React.FC<LabelProps> = ({ children, className, ...props }) => (
  <label className={`block ${className || ''}`} {...props}>
    {children}
  </label>
)

interface AlertProps {
  children: React.ReactNode
  variant?: 'error' | 'default'
  className?: string
}

const Alert: React.FC<AlertProps> = ({ children, variant, className }) => (
  <div className={`rounded-lg p-3 ${variant === 'error' ? 'bg-red-100 text-red-800 border border-red-200' : 'bg-blue-100 text-blue-800 border border-blue-200'} ${className || ''}`}>
    {children}
  </div>
)

interface BadgeProps {
  children: React.ReactNode
  variant?: 'outline' | 'secondary'
  className?: string
}

const Badge: React.FC<BadgeProps> = ({ children, variant, className }) => (
  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${variant === 'outline' ? 'border' : 'bg-gray-100'} ${className || ''}`}>
    {children}
  </span>
)

export const DynamicScanForm: React.FC<DynamicScanFormProps> = ({
  onSubmit,
  isLoading = false,
  error = null,
  onReset,
  showResetButton = false,
  className = '',
}) => {
  const [scanType, setScanType] = useState<ScanType>('web-api')
  const [scanMode, setScanMode] = useState<ScanMode>('basic')
  const [inputType, setInputType] = useState<InputType>('single')

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
  } = useForm<ScanUrlInput>({
    resolver: zodResolver(scanUrlSchema),
    defaultValues: {
      target: '',
      scanType: 'web-api',
      scanMode: 'basic',
      inputType: 'single',
      severity: ['critical', 'high', 'medium', 'low', 'info', 'unknown']
    }
  })

  useEffect(() => {
    setValue('scanType', scanType)
    setValue('scanMode', scanMode)
    setValue('inputType', inputType)
  }, [scanType, scanMode, inputType, setValue])

  const handleFormSubmit = async (data: ScanUrlInput) => {
    try {
      if (scanType === 'web-api' && data.target) {
        data.url = data.target
      }
      await onSubmit(data)
    } catch (error) {
      console.error('Form submission error:', error)
    }
  }

  const getTemplateInfo = () => {
    if (scanType === 'web-api') {
      if (scanMode === 'basic') {
        return {
          templates: ['HTTP Vulnerabilities', 'Configuration Issues', 'Exposed Panels', 'Information Disclosure', 'Default Credentials', 'SSL/TLS Security', 'Technology Detection', 'Subdomain Takeovers', 'DNS Enumeration', 'File Vulnerabilities', 'JavaScript Security', 'DAST Testing', 'Code Exposure', 'Network Checks', 'IoT Detection', 'CVE Database', 'Token Attacks', 'Generic Detections', 'OSINT Gathering', 'Fuzzing Detection', 'Browser-based Checks', 'Multi-step Workflows', 'China CVE Database'],
          description: 'COMPREHENSIVE security assessment with 24+ template categories covering ALL vulnerability types and severity levels. Maximum detection capability with no artificial constraints.',
          duration: 'No time limits - runs until complete',
          businessValue: 'Maximum vulnerability coverage for thorough security assessment'
        }
      } else {
        return {
          templates: ['ALL Quick Scan Templates (24+)', 'Authentication Bypass', 'Backup Files', 'Brute Force', 'CMS Vulnerabilities', 'Command Injection', 'CORS Issues', 'CSRF', 'Deserialization', 'Directory Traversal', 'File Upload', 'GraphQL', 'All Injection Types', 'LFI/RFI', 'NoSQL Injection', 'Open Redirect', 'Prototype Pollution', 'Race Conditions', 'RCE', 'SQL Injection', 'SSRF', 'SSTI', 'XSS', 'XXE', 'Cloud Security (AWS/Azure/GCP)', 'Database Security', 'Web Server Security', 'API Security', 'Mobile Security', 'Malware Detection', 'Phishing Detection', 'Cryptocurrency', 'Blockchain', '80+ Additional Categories'],
          description: 'MAXIMUM VULNERABILITY DETECTION with 100+ template categories. Includes ALL quick scan templates PLUS comprehensive deep-dive analysis covering every possible vulnerability type. No time constraints or artificial limits.',
          duration: 'Unlimited - comprehensive scanning',
          businessValue: 'Ultimate security assessment with maximum possible vulnerability coverage'
        }
      }
    } else {
      return {
        templates: ['All CVE Vulnerabilities', 'Network Services', 'Configuration Security', 'Information Exposure', 'DNS Security', 'SSL/TLS', 'FTP/SSH/Telnet', 'SMTP/SNMP/RDP', 'Database Services', 'IoT Devices', 'SCADA/ICS', 'Network Printers', 'Routers/Switches', 'Firewalls', 'Load Balancers', 'VPN/Wireless', 'Bluetooth/ZigBee', 'Industrial Protocols (Modbus/BACnet/DNP3/IEC61850/OPC UA)', 'Communication Protocols (CoAP/MQTT/AMQP/XMPP/SIP/RTSP)', 'Network Infrastructure (NTP/DHCP/TFTP/Kerberos/LDAP/RADIUS)', 'File Sharing (SMB/NFS/RPC)', 'Discovery Protocols (UPnP/mDNS/LLMNR/NBNS)', '40+ Network Categories'],
        description: 'COMPREHENSIVE network infrastructure security assessment covering ALL network protocols, services, and devices. Maximum detection across all network layers and industrial systems.',
        duration: 'Unlimited - thorough network scanning',
        businessValue: 'Complete network security coverage including IoT, SCADA, and industrial systems'
      }
    }
  }

  const templateInfo = getTemplateInfo()

  return (
    <div className={`${className} min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100`}>
      <div className="flex flex-col min-h-screen">

        {/* Premium Header */}
        <div className="bg-gradient-to-r from-slate-900 via-blue-900 to-indigo-900 text-white px-8 py-6 shadow-2xl">
          <div className="max-w-7xl mx-auto">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="p-2 bg-blue-500/20 rounded-xl backdrop-blur-sm">
                  <Shield className="h-10 w-10 text-blue-300" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
                    CTB Security Scanner
                  </h1>
                  <p className="text-blue-200 text-base font-medium">Enterprise-grade vulnerability assessment platform</p>
                </div>
              </div>
              {error && (
                <Alert variant="error" className="bg-red-500/20 border-red-400 text-red-100 backdrop-blur-sm max-w-md">
                  {error}
                </Alert>
              )}
            </div>
          </div>
        </div>

        {/* Main Content - Premium Layout */}
        <div className="flex-1 p-8">
          <div className="max-w-7xl mx-auto">

            {/* Enhanced Two-Column Layout */}
            <div className="grid grid-cols-3 gap-8">

              {/* Left Column - Configuration */}
              <div className="col-span-2 space-y-6">

                {/* Target Input Section - Enhanced */}
                <div className="bg-white/95 backdrop-blur-md rounded-2xl p-8 shadow-xl border border-white/60 hover:shadow-2xl transition-all duration-300">
                  <Label htmlFor="target" className="text-xl font-bold text-slate-800 flex items-center mb-6">
                    <div className="p-2 bg-blue-100 rounded-lg mr-3">
                      {scanType === 'web-api' ? (
                        <Globe className="h-6 w-6 text-blue-600" />
                      ) : (
                        <Network className="h-6 w-6 text-blue-600" />
                      )}
                    </div>
                    {inputType === 'single'
                      ? (scanType === 'web-api' ? 'Target Website or Application' : 'Target IP Address')
                      : (scanType === 'web-api' ? 'Multiple Target URLs' : 'Multiple IP Addresses')
                    }
                  </Label>

                  {inputType === 'single' ? (
                    <div className="relative">
                      <Input
                        id="target"
                        type="text"
                        placeholder={
                          scanType === 'web-api'
                            ? "https://yourcompany.com"
                            : "*************"
                        }
                        className="h-14 text-lg border-2 border-slate-200 focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20 rounded-xl bg-white/80 focus:bg-white px-6 shadow-sm hover:shadow-md transition-all duration-200"
                        disabled={isLoading}
                        {...register('target')}
                      />
                      <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                        <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                      </div>
                    </div>
                  ) : (
                    <div className="relative">
                      <textarea
                        id="target"
                        rows={4}
                        placeholder={
                          scanType === 'web-api'
                            ? "https://app1.yourcompany.com\nhttps://api.yourcompany.com\nhttps://admin.yourcompany.com"
                            : "*************\n192.168.1.101\n192.168.1.102"
                        }
                        className="w-full p-6 text-lg border-2 border-slate-200 focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20 rounded-xl bg-white/80 focus:bg-white resize-none shadow-sm hover:shadow-md transition-all duration-200"
                        disabled={isLoading}
                        {...register('target')}
                      />
                      <div className="absolute right-4 top-4">
                        <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                      </div>
                    </div>
                  )}

                  {errors.target && (
                    <p className="text-sm text-red-600 font-medium mt-2 flex items-center">
                      <span className="w-2 h-2 bg-red-600 rounded-full mr-2"></span>
                      {errors.target.message}
                    </p>
                  )}
                </div>

                {/* Enhanced Configuration Grid */}
                <div className="grid grid-cols-3 gap-6">

                  {/* Asset Type - Enhanced */}
                  <div className="bg-white/95 backdrop-blur-md rounded-2xl p-6 shadow-xl border border-white/60 hover:shadow-2xl transition-all duration-300">
                    <Label className="text-lg font-bold text-slate-800 flex items-center mb-4">
                      <div className="p-2 bg-purple-100 rounded-lg mr-3">
                        <Layers className="h-5 w-5 text-purple-600" />
                      </div>
                      Asset Type
                    </Label>
                    <div className="space-y-3">
                      <button
                        type="button"
                        onClick={() => setScanType('web-api')}
                        className={`w-full p-4 rounded-xl border-2 transition-all duration-300 transform hover:scale-105 ${
                          scanType === 'web-api'
                            ? 'border-blue-500 bg-gradient-to-br from-blue-50 to-blue-100 text-blue-700 shadow-lg'
                            : 'border-slate-200 hover:border-slate-300 text-slate-700 hover:shadow-lg bg-white/80'
                        }`}
                      >
                        <Globe className="h-8 w-8 mx-auto mb-2" />
                        <div className="font-bold text-sm">Web Application</div>
                        <div className="text-xs opacity-75">Websites & APIs</div>
                      </button>

                      <button
                        type="button"
                        onClick={() => setScanType('network')}
                        className={`w-full p-4 rounded-xl border-2 transition-all duration-300 transform hover:scale-105 ${
                          scanType === 'network'
                            ? 'border-blue-500 bg-gradient-to-br from-blue-50 to-blue-100 text-blue-700 shadow-lg'
                            : 'border-slate-200 hover:border-slate-300 text-slate-700 hover:shadow-lg bg-white/80'
                        }`}
                      >
                        <Network className="h-8 w-8 mx-auto mb-2" />
                        <div className="font-bold text-sm">Network Infrastructure</div>
                        <div className="text-xs opacity-75">Servers & Devices</div>
                      </button>
                    </div>
                  </div>

                  {/* Assessment Depth - Enhanced */}
                  <div className="bg-white/95 backdrop-blur-md rounded-2xl p-6 shadow-xl border border-white/60 hover:shadow-2xl transition-all duration-300">
                    <Label className="text-lg font-bold text-slate-800 flex items-center mb-4">
                      <div className="p-2 bg-yellow-100 rounded-lg mr-3">
                        <Zap className="h-5 w-5 text-yellow-600" />
                      </div>
                      Scan Depth
                    </Label>
                    <div className="space-y-3">
                      <button
                        type="button"
                        onClick={() => setScanMode('basic')}
                        className={`w-full p-4 rounded-xl border-2 transition-all duration-300 transform hover:scale-105 ${
                          scanMode === 'basic'
                            ? 'border-green-500 bg-gradient-to-br from-green-50 to-green-100 text-green-700 shadow-lg'
                            : 'border-slate-200 hover:border-slate-300 text-slate-700 hover:shadow-lg bg-white/80'
                        }`}
                      >
                        <Clock className="h-8 w-8 mx-auto mb-2" />
                        <div className="font-bold text-sm">Quick Assessment</div>
                        <div className="text-xs opacity-75">Essential checks</div>
                      </button>

                      <button
                        type="button"
                        onClick={() => setScanMode('advanced')}
                        className={`w-full p-4 rounded-xl border-2 transition-all duration-300 transform hover:scale-105 ${
                          scanMode === 'advanced'
                            ? 'border-green-500 bg-gradient-to-br from-green-50 to-green-100 text-green-700 shadow-lg'
                            : 'border-slate-200 hover:border-slate-300 text-slate-700 hover:shadow-lg bg-white/80'
                        }`}
                      >
                        <Cpu className="h-8 w-8 mx-auto mb-2" />
                        <div className="font-bold text-sm">Comprehensive</div>
                        <div className="text-xs opacity-75">Deep analysis</div>
                      </button>
                    </div>
                  </div>

                  {/* Target Scope - Enhanced */}
                  <div className="bg-white/95 backdrop-blur-md rounded-2xl p-6 shadow-xl border border-white/60 hover:shadow-2xl transition-all duration-300">
                    <Label className="text-lg font-bold text-slate-800 flex items-center mb-4">
                      <div className="p-2 bg-indigo-100 rounded-lg mr-3">
                        <Target className="h-5 w-5 text-indigo-600" />
                      </div>
                      Target Scope
                    </Label>
                    <div className="space-y-3">
                      <button
                        type="button"
                        onClick={() => setInputType('single')}
                        className={`w-full p-4 rounded-xl border-2 transition-all duration-300 transform hover:scale-105 ${
                          inputType === 'single'
                            ? 'border-indigo-500 bg-gradient-to-br from-indigo-50 to-indigo-100 text-indigo-700 shadow-lg'
                            : 'border-slate-200 hover:border-slate-300 text-slate-700 hover:shadow-lg bg-white/80'
                        }`}
                      >
                        <FileText className="h-8 w-8 mx-auto mb-2" />
                        <div className="font-bold text-sm">Single Asset</div>
                        <div className="text-xs opacity-75">One target</div>
                      </button>

                      <button
                        type="button"
                        onClick={() => setInputType('bulk')}
                        className={`w-full p-4 rounded-xl border-2 transition-all duration-300 transform hover:scale-105 ${
                          inputType === 'bulk'
                            ? 'border-indigo-500 bg-gradient-to-br from-indigo-50 to-indigo-100 text-indigo-700 shadow-lg'
                            : 'border-slate-200 hover:border-slate-300 text-slate-700 hover:shadow-lg bg-white/80'
                        }`}
                      >
                        <List className="h-8 w-8 mx-auto mb-2" />
                        <div className="font-bold text-sm">Multiple Assets</div>
                        <div className="text-xs opacity-75">Bulk assessment</div>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Right Column - Enhanced Summary & Action */}
              <div className="flex flex-col space-y-6">

                {/* Assessment Summary - Premium */}
                <div className="bg-gradient-to-br from-blue-50/95 to-indigo-50/95 backdrop-blur-md rounded-2xl p-6 shadow-xl border border-blue-200/60 hover:shadow-2xl transition-all duration-300">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-xl font-bold text-slate-800">Overview</h3>
                    <Badge variant="outline" className="bg-white/90 text-blue-700 border-blue-300 px-3 py-1.5 text-sm font-semibold rounded-lg">
                      {templateInfo.duration}
                    </Badge>
                  </div>

                  <p className="text-base text-slate-700 mb-6 leading-relaxed">{templateInfo.description}</p>

                  <div className="space-y-4">
                    <div>
                      <div className="text-base font-bold text-slate-700 mb-3">Security Checks:</div>
                      <div className="flex flex-wrap gap-2">
                        {templateInfo.templates.slice(0, 3).map((template, index) => (
                          <Badge key={index} variant="secondary" className="text-sm bg-white/90 px-3 py-1.5 rounded-lg shadow-sm">
                            {template}
                          </Badge>
                        ))}
                        {templateInfo.templates.length > 3 && (
                          <Badge variant="secondary" className="text-sm bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-700 px-3 py-1.5 rounded-lg shadow-sm font-semibold">
                            +{templateInfo.templates.length - 3} more
                          </Badge>
                        )}
                      </div>
                    </div>

                    <div className="bg-white/90 rounded-xl p-4 border border-blue-200/60 shadow-sm">
                      <div className="text-base font-bold text-slate-700 flex items-center mb-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                        Business Value
                      </div>
                      <div className="text-sm text-slate-600 leading-relaxed">{templateInfo.businessValue}</div>
                    </div>
                  </div>
                </div>

                {/* Enhanced Action Buttons */}
                <div className="space-y-4">
                  <Button
                    type="submit"
                    disabled={isLoading}
                    onClick={handleSubmit(handleFormSubmit)}
                    className="w-full h-18 bg-gradient-to-r from-slate-800 via-blue-800 to-indigo-800 hover:from-slate-900 hover:via-blue-900 hover:to-indigo-900 text-white font-bold text-lg rounded-2xl shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-[1.02] hover:-translate-y-1"
                  >
                    {isLoading ? (
                      <div className="flex items-center justify-center py-2">
                        <LoadingSpinner size="sm" className="mr-4" />
                        <div className="text-center">
                          <div className="text-lg font-bold">Initializing Security Assessment...</div>
                          <div className="text-sm opacity-90">Please wait while we prepare your scan</div>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center justify-center py-2">
                        <div className="p-2 bg-white/20 rounded-lg mr-4">
                          <Shield className="h-8 w-8" />
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-bold">Scan</div>
                          {/* <div className="text-sm opacity-90">
                            {scanMode === 'basic' ? 'Quick Assessment' : 'Comprehensive Analysis'} • {templateInfo.duration}
                          </div> */}
                        </div>
                      </div>
                    )}
                  </Button>

                  {showResetButton && onReset && (
                    <Button
                      type="button"
                      variant="outline"
                      onClick={onReset}
                      disabled={isLoading}
                      className="w-full h-14 border-2 border-slate-300 hover:border-slate-400 text-slate-700 font-bold text-base rounded-2xl transition-all duration-300 hover:shadow-lg bg-white/90 backdrop-blur-md transform hover:scale-[1.01]"
                    >
                      Reset Configuration
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
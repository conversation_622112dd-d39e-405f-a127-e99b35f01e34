/**
 * Authentication Context Provider
 *
 * Provides global authentication state management for the entire application.
 * Handles user login, logout, and session persistence across browser
 * refreshes and navigation.
 *
 * Key Features:
 * - Centralized authentication state management
 * - Automatic session restoration on app load
 * - JWT token handling with HTTP-only cookies
 * - User data persistence and updates
 * - Loading states for async operations
 * - Error handling for auth operations
 *
 * Security Features:
 * - Secure token storage in HTTP-only cookies
 * - Automatic logout on token expiration
 * - CSRF protection via SameSite cookies
 * - Input validation for auth operations
 *
 * Usage:
 * - Wrap app with AuthProvider
 * - Use useAuth hook in components
 * - Access user data and auth methods
 *
 * <AUTHOR> Scanner Team
 * @version 1.0.0
 */

'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'

/**
 * User data interface
 *
 * Represents the authenticated user's information
 * stored in the application state.
 */
export interface User {
  id: string          // Unique user identifier
  firstName: string   // User's first name
  lastName: string    // User's last name
  companyName: string // User's company/organization
  country: string     // User's country
  email: string       // User's email address
  createdAt: string   // Account creation timestamp
  updatedAt: string   // Last update timestamp
}

/**
 * Authentication context interface
 *
 * Defines the shape of the authentication context
 * that components can access via useAuth hook.
 */
interface AuthContextType {
  user: User | null                                           // Current user data or null
  isLoading: boolean                                          // Loading state for auth operations
  login: (email: string, password: string) => Promise<void>   // Login function
  logout: () => Promise<void>                                 // Logout function
  refreshUser: () => Promise<void>                            // Refresh user data function
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Check if user is authenticated on mount
  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      const response = await fetch('/api/auth/me')
      if (response.ok) {
        const data = await response.json()
        setUser(data.user)
      }
    } catch (error) {
      console.error('Auth check failed:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const login = async (email: string, password: string) => {
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password }),
    })

    const data = await response.json()

    if (!response.ok) {
      throw new Error(data.error || 'Login failed')
    }

    setUser(data.user)
  }

  const logout = async () => {
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
      })
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      setUser(null)
    }
  }

  const refreshUser = async () => {
    try {
      const response = await fetch('/api/auth/me')
      if (response.ok) {
        const data = await response.json()
        setUser(data.user)
      } else {
        setUser(null)
      }
    } catch (error) {
      console.error('Refresh user failed:', error)
      setUser(null)
    }
  }

  const value = {
    user,
    isLoading,
    login,
    logout,
    refreshUser,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Integration tests use shared setup from tests/setup/integration.setup.js
const { testRequest, createTestUser } = require('../setup/integration.setup')

describe('Authentication API', () => {
  const getTestUser = () => ({
    firstName: 'John',
    lastName: 'Doe',
    companyName: 'Test Company',
    country: 'United States',
    email: `test-${Date.now()}-${Math.random()}@example.com`,
    password: 'TestPassword123!',
    confirmPassword: 'TestPassword123!'
  })

  describe('POST /api/auth/signup', () => {
    it('should create a new user successfully', async () => {
      const testUser = getTestUser()
      const response = await testRequest()
        .post('/api/auth/signup')
        .send(testUser)
        .expect(201)

      expect(response.body).toHaveProperty('message', 'User created successfully')
      expect(response.body).toHaveProperty('user')
      expect(response.body.user).toHaveProperty('email', testUser.email)
      expect(response.body.user).not.toHaveProperty('password')
    })

    it('should reject duplicate email', async () => {
      const testUser = getTestUser()

      // First signup
      await testRequest()
        .post('/api/auth/signup')
        .send(testUser)
        .expect(201)

      // Second signup with same email
      const response = await testRequest()
        .post('/api/auth/signup')
        .send(testUser)
        .expect(409)

      expect(response.body).toHaveProperty('error')
    })

    it('should validate required fields', async () => {
      const invalidUser = { email: '<EMAIL>' }

      const response = await testRequest()
        .post('/api/auth/signup')
        .send(invalidUser)
        .expect(400)

      expect(response.body).toHaveProperty('error')
    })

    it('should validate email format', async () => {
      const testUser = getTestUser()
      const invalidUser = {
        ...testUser,
        email: 'invalid-email'
      }

      const response = await testRequest()
        .post('/api/auth/signup')
        .send(invalidUser)
        .expect(400)

      expect(response.body).toHaveProperty('error')
    })

    it('should validate password strength', async () => {
      const testUser = getTestUser()
      const weakPasswordUser = {
        ...testUser,
        email: `weak-${Date.now()}@example.com`,
        password: '123',
        confirmPassword: '123'
      }

      const response = await testRequest()
        .post('/api/auth/signup')
        .send(weakPasswordUser)
        .expect(400)

      expect(response.body).toHaveProperty('error')
    })

    it('should validate password confirmation', async () => {
      const testUser = getTestUser()
      const mismatchUser = {
        ...testUser,
        email: `mismatch-${Date.now()}@example.com`,
        confirmPassword: 'DifferentPassword123!'
      }

      const response = await testRequest()
        .post('/api/auth/signup')
        .send(mismatchUser)
        .expect(400)

      expect(response.body).toHaveProperty('error')
    })
  })

  describe('POST /api/auth/login', () => {
    let testUser

    beforeEach(async () => {
      // Create a test user before each login test
      testUser = getTestUser()
      await testRequest()
        .post('/api/auth/signup')
        .send(testUser)
        .expect(201)
    })

    it('should login successfully with valid credentials', async () => {
      const response = await testRequest()
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })
        .expect(200)

      expect(response.body).toHaveProperty('message', 'Login successful')
      expect(response.body).toHaveProperty('user')
      expect(response.body.user).toHaveProperty('email', testUser.email)
      expect(response.headers['set-cookie']).toBeDefined()
    })

    it('should reject invalid email', async () => {
      const response = await testRequest()
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: testUser.password
        })
        .expect(401)

      expect(response.body).toHaveProperty('error')
    })

    it('should reject invalid password', async () => {
      const response = await testRequest()
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: 'wrongpassword'
        })
        .expect(401)

      expect(response.body).toHaveProperty('error')
    })

    it('should validate required fields', async () => {
      const response = await testRequest()
        .post('/api/auth/login')
        .send({
          email: testUser.email
          // Missing password
        })
        .expect(400)

      expect(response.body).toHaveProperty('error')
    })
  })

  describe('GET /api/auth/me', () => {
    describe('when authenticated', () => {
      let authCookie, testUser

      beforeEach(async () => {
        // Create and login user
        const { user, authCookie: cookie } = await createTestUser()
        testUser = user
        authCookie = cookie
      })

      it('should return user info when authenticated', async () => {
        const response = await testRequest()
          .get('/api/auth/me')
          .set('Cookie', authCookie)
          .expect(200)

        expect(response.body).toHaveProperty('user')
        expect(response.body.user).toHaveProperty('email', testUser.email)
        expect(response.body.user).not.toHaveProperty('password')
      })
    })

    describe('when not authenticated', () => {
      it('should reject unauthenticated requests', async () => {
        const response = await testRequest()
          .get('/api/auth/me')
          .expect(401)

        expect(response.body).toHaveProperty('error')
      })
    })
  })

  describe('POST /api/auth/logout', () => {
    let authCookie

    beforeEach(async () => {
      // Create and login user
      const { authCookie: cookie } = await createTestUser()
      authCookie = cookie
    })

    it('should logout successfully', async () => {
      const response = await testRequest()
        .post('/api/auth/logout')
        .set('Cookie', authCookie)
        .expect(200)

      expect(response.body).toHaveProperty('message', 'Logout successful')

      // Verify cookie is cleared
      const setCookieHeader = response.headers['set-cookie']
      expect(setCookieHeader).toBeDefined()
      expect(setCookieHeader[0]).toContain('Max-Age=0')
    })

    it('should handle logout when not authenticated', async () => {
      const response = await testRequest()
        .post('/api/auth/logout')
        .expect(200)

      expect(response.body).toHaveProperty('message', 'Logout successful')
    })
  })
})

/**
 * Unit tests for Error Tracking system
 */

import { errorTracker, trackError, getErrorStats, getError } from '../error-tracking'

// Mock logger
jest.mock('../logger', () => ({
  logger: {
    error: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
    security: jest.fn(),
  }
}))

describe('Error Tracking', () => {
  beforeEach(() => {
    // Clear error tracker before each test
    errorTracker.clear()
    jest.clearAllMocks()
  })

  describe('Basic Error Tracking', () => {
    it('should track a simple error', () => {
      const error = new Error('Test error')
      const fingerprint = trackError(error)
      
      expect(fingerprint).toBeDefined()
      expect(typeof fingerprint).toBe('string')
      
      const trackedError = getError(fingerprint)
      expect(trackedError).toBeDefined()
      expect(trackedError?.error.message).toBe('Test error')
      expect(trackedError?.count).toBe(1)
    })

    it('should track error with context', () => {
      const error = new Error('Test error with context')
      const context = { userId: '123', component: 'test' }
      
      const fingerprint = trackError(error, context)
      const trackedError = getError(fingerprint)
      
      expect(trackedError?.context.userId).toBe('123')
      expect(trackedError?.context.component).toBe('test')
    })

    it('should track error with severity', () => {
      const error = new Error('Critical error')
      const fingerprint = trackError(error, {}, 'critical')
      
      const trackedError = getError(fingerprint)
      expect(trackedError?.severity).toBe('critical')
    })

    it('should default to medium severity', () => {
      const error = new Error('Default severity error')
      const fingerprint = trackError(error)
      
      const trackedError = getError(fingerprint)
      expect(trackedError?.severity).toBe('medium')
    })
  })

  describe('Error Deduplication', () => {
    it('should deduplicate identical errors', () => {
      // Create errors with the same stack trace for proper deduplication testing
      function createTestError() {
        return new Error('Duplicate error')
      }

      const error1 = createTestError()
      const error2 = createTestError()

      const fingerprint1 = trackError(error1, { component: 'test' })
      const fingerprint2 = trackError(error2, { component: 'test' })

      expect(fingerprint1).toBe(fingerprint2)

      const trackedError = getError(fingerprint1)
      expect(trackedError?.count).toBe(2)
    })

    it('should create different fingerprints for different errors', () => {
      const error1 = new Error('Error 1')
      const error2 = new Error('Error 2')
      
      const fingerprint1 = trackError(error1)
      const fingerprint2 = trackError(error2)
      
      expect(fingerprint1).not.toBe(fingerprint2)
    })

    it('should create different fingerprints for same error in different components', () => {
      const error1 = new Error('Same error')
      const error2 = new Error('Same error')
      
      const fingerprint1 = trackError(error1, { component: 'component1' })
      const fingerprint2 = trackError(error2, { component: 'component2' })
      
      expect(fingerprint1).not.toBe(fingerprint2)
    })
  })

  describe('Error Statistics', () => {
    it('should provide accurate error statistics', () => {
      // Create error objects to reuse for proper deduplication
      const error1 = new Error('Error 1')
      const error2 = new Error('Error 2')
      const error3 = new Error('Error 3')

      // Track some errors
      trackError(error1, { component: 'comp1' }, 'high')
      trackError(error2, { component: 'comp1' }, 'medium')
      trackError(error3, { component: 'comp2' }, 'low')
      trackError(error1, { component: 'comp1' }, 'high') // Duplicate
      
      const stats = getErrorStats()
      
      expect(stats.totalErrors).toBe(4) // Total occurrences
      expect(stats.uniqueErrors).toBe(3) // Unique error types
      expect(stats.errorsByComponent.comp1).toBe(3)
      expect(stats.errorsByComponent.comp2).toBe(1)
      expect(stats.errorsBySeverity.high).toBe(2)
      expect(stats.errorsBySeverity.medium).toBe(1)
      expect(stats.errorsBySeverity.low).toBe(1)
    })

    it('should provide top errors by count', () => {
      // Create errors with different frequencies
      for (let i = 0; i < 5; i++) {
        trackError(new Error('Frequent error'))
      }
      for (let i = 0; i < 3; i++) {
        trackError(new Error('Less frequent error'))
      }
      trackError(new Error('Rare error'))
      
      const stats = getErrorStats()
      
      expect(stats.topErrors).toHaveLength(3)
      expect(stats.topErrors[0].count).toBe(5)
      expect(stats.topErrors[0].message).toBe('Frequent error')
      expect(stats.topErrors[1].count).toBe(3)
      expect(stats.topErrors[1].message).toBe('Less frequent error')
      expect(stats.topErrors[2].count).toBe(1)
      expect(stats.topErrors[2].message).toBe('Rare error')
    })

    it('should filter recent errors', () => {
      // Track an error
      trackError(new Error('Recent error'))
      
      const stats = getErrorStats()
      expect(stats.recentErrors).toHaveLength(1)
      expect(stats.recentErrors[0].error.message).toBe('Recent error')
    })
  })

  describe('Error Filtering', () => {
    beforeEach(() => {
      // Set up test data
      trackError(new Error('Component A Error'), { component: 'componentA' }, 'high')
      trackError(new Error('Component B Error'), { component: 'componentB' }, 'medium')
      trackError(new Error('Critical Error'), { component: 'componentA' }, 'critical')
    })

    it('should filter errors by component', () => {
      const componentAErrors = errorTracker.getErrorsByComponent('componentA')
      
      expect(componentAErrors).toHaveLength(2)
      expect(componentAErrors.every(error => error.context.component === 'componentA')).toBe(true)
    })

    it('should filter errors by severity', () => {
      const highSeverityErrors = errorTracker.getErrorsBySeverity('high')
      
      expect(highSeverityErrors).toHaveLength(1)
      expect(highSeverityErrors[0].severity).toBe('high')
      expect(highSeverityErrors[0].error.message).toBe('Component A Error')
    })

    it('should return empty array for non-existent component', () => {
      const nonExistentErrors = errorTracker.getErrorsByComponent('nonexistent')
      expect(nonExistentErrors).toHaveLength(0)
    })

    it('should return empty array for non-existent severity', () => {
      const nonExistentErrors = errorTracker.getErrorsBySeverity('low')
      expect(nonExistentErrors).toHaveLength(0)
    })
  })

  describe('Error Cleanup', () => {
    it('should clear all errors', () => {
      trackError(new Error('Error 1'))
      trackError(new Error('Error 2'))
      
      let stats = getErrorStats()
      expect(stats.uniqueErrors).toBe(2)
      
      errorTracker.clear()
      
      stats = getErrorStats()
      expect(stats.uniqueErrors).toBe(0)
      expect(stats.totalErrors).toBe(0)
    })
  })

  describe('Error Metadata', () => {
    it('should track first and last seen timestamps', () => {
      const error = new Error('Timestamp test')
      const startTime = Date.now()
      
      const fingerprint = trackError(error)
      
      // Track the same error again after a small delay
      setTimeout(() => {
        trackError(error)
        
        const trackedError = getError(fingerprint)
        expect(trackedError?.firstSeen).toBeGreaterThanOrEqual(startTime)
        expect(trackedError?.lastSeen).toBeGreaterThan(trackedError?.firstSeen || 0)
        expect(trackedError?.count).toBe(2)
      }, 10)
    })

    it('should generate unique IDs for each error event', () => {
      const error1 = new Error('Error 1')
      const error2 = new Error('Error 2')
      
      const fingerprint1 = trackError(error1)
      const fingerprint2 = trackError(error2)
      
      const trackedError1 = getError(fingerprint1)
      const trackedError2 = getError(fingerprint2)
      
      expect(trackedError1?.id).toBeDefined()
      expect(trackedError2?.id).toBeDefined()
      expect(trackedError1?.id).not.toBe(trackedError2?.id)
    })
  })

  describe('Error Context Merging', () => {
    it('should merge context from multiple occurrences', () => {
      const error = new Error('Context merge test')
      
      trackError(error, { userId: '123', action: 'create' })
      trackError(error, { sessionId: 'abc', action: 'update' })
      
      const stats = getErrorStats()
      const trackedError = stats.recentErrors[0]
      
      expect(trackedError.context.userId).toBe('123')
      expect(trackedError.context.sessionId).toBe('abc')
      expect(trackedError.context.action).toBe('update') // Latest value
      expect(trackedError.count).toBe(2)
    })
  })

  describe('Error Fingerprinting', () => {
    it('should create consistent fingerprints for identical errors', () => {
      const error1 = new Error('Consistent error')
      error1.stack = 'Error: Consistent error\n    at test.js:1:1'
      
      const error2 = new Error('Consistent error')
      error2.stack = 'Error: Consistent error\n    at test.js:1:1'
      
      const fingerprint1 = trackError(error1, { component: 'test' })
      const fingerprint2 = trackError(error2, { component: 'test' })
      
      expect(fingerprint1).toBe(fingerprint2)
    })

    it('should create different fingerprints for different stack traces', () => {
      const error1 = new Error('Same message')
      error1.stack = 'Error: Same message\n    at file1.js:1:1'
      
      const error2 = new Error('Same message')
      error2.stack = 'Error: Same message\n    at file2.js:1:1'
      
      const fingerprint1 = trackError(error1)
      const fingerprint2 = trackError(error2)
      
      expect(fingerprint1).not.toBe(fingerprint2)
    })
  })
})

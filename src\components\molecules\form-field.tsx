import * as React from 'react'
import { cn } from '@/lib/utils'
import { Label } from '../atoms/label'
import { Input } from '../atoms/input'
import { Text } from '../atoms/text'

export interface FormFieldProps {
  label?: string
  required?: boolean
  error?: string
  hint?: string
  className?: string
  children?: React.ReactNode
  // Input props
  type?: string
  placeholder?: string
  value?: string
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  disabled?: boolean
  id?: string
}

export const FormField: React.FC<FormFieldProps> = ({
  label,
  required = false,
  error,
  hint,
  className,
  children,
  id,
  ...inputProps
}) => {
  const fieldId = id || React.useId()
  const hasError = Boolean(error)

  return (
    <div className={cn('space-y-2', className)}>
      {label && (
        <Label 
          htmlFor={fieldId} 
          required={required}
          error={hasError}
        >
          {label}
        </Label>
      )}
      
      {children || (
        <Input
          id={fieldId}
          error={error}
          required={required}
          {...inputProps}
        />
      )}
      
      {hint && !error && (
        <Text variant="caption" color="muted">
          {hint}
        </Text>
      )}
      
      {error && (
        <Text variant="caption" color="error">
          {error}
        </Text>
      )}
    </div>
  )
}

{"template":"\\dns\\mx-fingerprint.yaml","template-url":"https://cloud.projectdiscovery.io/public/mx-fingerprint","template-id":"mx-fingerprint","template-path":"C:\\Users\\<USER>\\nuclei-templates\\dns\\mx-fingerprint.yaml","info":{"name":"MX Record Detection","author":["pdteam"],"tags":["dns","mx"],"description":"An MX record was detected. MX records direct emails to a mail exchange server.","reference":["https://www.cloudflare.com/learning/dns/dns-records/dns-mx-record/","https://mxtoolbox.com/"],"severity":"info","metadata":{"max-request":1},"classification":{"cve-id":null,"cwe-id":["cwe-200"]}},"type":"dns","host":"pentest-ground.com","matched-at":"pentest-ground.com","extracted-results":["10 mail.pentest-ground.com."],"request":";; opcode: QUERY, status: NOERROR, id: 58297\n;; flags: rd; QUERY: 1, ANSWER: 0, AUTHORITY: 0, ADDITIONAL: 1\n\n;; OPT PSEUDOSECTION:\n; EDNS: version 0; flags:; udp: 4096\n\n;; QUESTION SECTION:\n;pentest-ground.com.\tIN\t MX\n","response":";; opcode: QUERY, status: NOERROR, id: 58297\n;; flags: qr rd ra; QUERY: 1, ANSWER: 1, AUTHORITY: 0, ADDITIONAL: 1\n\n;; OPT PSEUDOSECTION:\n; EDNS: version 0; flags:; udp: 512\n\n;; QUESTION SECTION:\n;pentest-ground.com.\tIN\t MX\n\n;; ANSWER SECTION:\npentest-ground.com.\t21600\tIN\tMX\t10 mail.pentest-ground.com.\n","timestamp":"2025-08-02T18:50:34.4646732+05:30","matcher-status":true}
{"template":"\\dns\\caa-fingerprint.yaml","template-url":"https://cloud.projectdiscovery.io/public/caa-fingerprint","template-id":"caa-fingerprint","template-path":"C:\\Users\\<USER>\\nuclei-templates\\dns\\caa-fingerprint.yaml","info":{"name":"CAA Record","author":["pdteam"],"tags":["dns","caa"],"description":"A CAA record was discovered. A CAA record is used to specify which certificate authorities (CAs) are allowed to issue certificates for a domain.","reference":["https://support.dnsimple.com/articles/caa-record/#whats-a-caa-record"],"severity":"info","metadata":{"max-request":1},"classification":{"cve-id":null,"cwe-id":["cwe-200"]}},"type":"dns","host":"pentest-ground.com","matched-at":"pentest-ground.com","extracted-results":["letsencrypt.org"],"request":";; opcode: QUERY, status: NOERROR, id: 22412\n;; flags: rd; QUERY: 1, ANSWER: 0, AUTHORITY: 0, ADDITIONAL: 1\n\n;; OPT PSEUDOSECTION:\n; EDNS: version 0; flags:; udp: 4096\n\n;; QUESTION SECTION:\n;pentest-ground.com.\tIN\t CAA\n","response":";; opcode: QUERY, status: NOERROR, id: 22412\n;; flags: qr rd ra; QUERY: 1, ANSWER: 1, AUTHORITY: 0, ADDITIONAL: 1\n\n;; OPT PSEUDOSECTION:\n; EDNS: version 0; flags:; udp: 1232\n\n;; QUESTION SECTION:\n;pentest-ground.com.\tIN\t CAA\n\n;; ANSWER SECTION:\npentest-ground.com.\t86400\tIN\tCAA\t0 issue \"letsencrypt.org\"\n","timestamp":"2025-08-02T18:50:34.5530111+05:30","matcher-status":true}
{"template":"\\dns\\nameserver-fingerprint.yaml","template-url":"https://cloud.projectdiscovery.io/public/nameserver-fingerprint","template-id":"nameserver-fingerprint","template-path":"C:\\Users\\<USER>\\nuclei-templates\\dns\\nameserver-fingerprint.yaml","info":{"name":"NS Record Detection","author":["pdteam"],"tags":["dns","ns"],"description":"An NS record was detected. An NS record delegates a subdomain to a set of name servers.","severity":"info","metadata":{"max-request":1},"classification":{"cve-id":null,"cwe-id":["cwe-200"]}},"type":"dns","host":"pentest-ground.com","matched-at":"pentest-ground.com","extracted-results":["ns1.linode.com.","ns5.linode.com.","ns2.linode.com.","ns4.linode.com.","ns3.linode.com."],"request":";; opcode: QUERY, status: NOERROR, id: 12665\n;; flags: rd; QUERY: 1, ANSWER: 0, AUTHORITY: 0, ADDITIONAL: 1\n\n;; OPT PSEUDOSECTION:\n; EDNS: version 0; flags:; udp: 4096\n\n;; QUESTION SECTION:\n;pentest-ground.com.\tIN\t NS\n","response":";; opcode: QUERY, status: NOERROR, id: 12665\n;; flags: qr rd ra; QUERY: 1, ANSWER: 5, AUTHORITY: 0, ADDITIONAL: 1\n\n;; OPT PSEUDOSECTION:\n; EDNS: version 0; flags:; udp: 512\n\n;; QUESTION SECTION:\n;pentest-ground.com.\tIN\t NS\n\n;; ANSWER SECTION:\npentest-ground.com.\t21600\tIN\tNS\tns1.linode.com.\npentest-ground.com.\t21600\tIN\tNS\tns5.linode.com.\npentest-ground.com.\t21600\tIN\tNS\tns2.linode.com.\npentest-ground.com.\t21600\tIN\tNS\tns4.linode.com.\npentest-ground.com.\t21600\tIN\tNS\tns3.linode.com.\n","timestamp":"2025-08-02T18:50:36.5217256+05:30","matcher-status":true}

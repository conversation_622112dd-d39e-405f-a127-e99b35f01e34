# CTB Scanner Docker Compose Configuration
# Production-ready setup with all required services

version: '3.8'

services:
  # =============================================================================
  # CTB Scanner Application
  # =============================================================================
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ctb-scanner-app
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=mysql://ctb_user:${MYSQL_PASSWORD}@mysql:3306/ctb_scanner
      - JWT_SECRET=${JWT_SECRET}
      - NEXTAUTH_URL=${NEXTAUTH_URL}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - NUCLEI_TEMPLATES_PATH=/opt/nuclei-templates
      - LOG_LEVEL=info
      - LOG_FILE_ENABLED=true
      - LOG_FILE_PATH=/var/log/ctb-scanner/app.log
      - HEALTH_CHECK_ENABLED=true
      - PERFORMANCE_MONITORING_INTERVAL=60000
      - CACHE_CLEANUP_INTERVAL=300000
      - DATABASE_CONNECTION_LIMIT=20
      - NUCLEI_MAX_CONCURRENT_SCANS=5
    volumes:
      - nuclei_templates:/opt/nuclei-templates
      - app_logs:/var/log/ctb-scanner
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - ctb-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # =============================================================================
  # MySQL Database
  # =============================================================================
  mysql:
    image: mysql:8.0
    container_name: ctb-scanner-mysql
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=ctb_scanner
      - MYSQL_USER=ctb_user
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/mysql-init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "3306:3306"
    networks:
      - ctb-network
    command: >
      --default-authentication-plugin=mysql_native_password
      --innodb-buffer-pool-size=256M
      --max-connections=100
      --query-cache-type=1
      --query-cache-size=64M
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # =============================================================================
  # Redis Cache (Optional - for session storage and caching)
  # =============================================================================
  redis:
    image: redis:7-alpine
    container_name: ctb-scanner-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - ctb-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =============================================================================
  # Nginx Reverse Proxy
  # =============================================================================
  nginx:
    image: nginx:alpine
    container_name: ctb-scanner-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - nginx_logs:/var/log/nginx
      - ssl_certs:/etc/nginx/ssl:ro
    depends_on:
      - app
    networks:
      - ctb-network
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =============================================================================
  # Monitoring (Optional - Prometheus for metrics)
  # =============================================================================
  prometheus:
    image: prom/prometheus:latest
    container_name: ctb-scanner-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - ctb-network
    profiles:
      - monitoring

  # =============================================================================
  # Grafana Dashboard (Optional)
  # =============================================================================
  grafana:
    image: grafana/grafana:latest
    container_name: ctb-scanner-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - ctb-network
    profiles:
      - monitoring

# =============================================================================
# Networks
# =============================================================================
networks:
  ctb-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# =============================================================================
# Volumes
# =============================================================================
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  nuclei_templates:
    driver: local
  app_logs:
    driver: local
  nginx_logs:
    driver: local
  ssl_certs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

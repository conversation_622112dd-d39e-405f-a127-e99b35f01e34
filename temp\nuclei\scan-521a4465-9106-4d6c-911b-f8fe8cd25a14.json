{"template":"\\dns\\mx-fingerprint.yaml","template-url":"https://cloud.projectdiscovery.io/public/mx-fingerprint","template-id":"mx-fingerprint","template-path":"C:\\Users\\<USER>\\nuclei-templates\\dns\\mx-fingerprint.yaml","info":{"name":"MX Record Detection","author":["pdteam"],"tags":["dns","mx"],"description":"An MX record was detected. MX records direct emails to a mail exchange server.","reference":["https://www.cloudflare.com/learning/dns/dns-records/dns-mx-record/","https://mxtoolbox.com/"],"severity":"info","metadata":{"max-request":1},"classification":{"cve-id":null,"cwe-id":["cwe-200"]}},"type":"dns","host":"itsecgames.com","matched-at":"itsecgames.com","extracted-results":["5 itsecgames-com.mail.protection.outlook.com."],"request":";; opcode: QUERY, status: NOERROR, id: 45112\n;; flags: rd; QUERY: 1, ANSWER: 0, AUTHORITY: 0, ADDITIONAL: 1\n\n;; OPT PSEUDOSECTION:\n; EDNS: version 0; flags:; udp: 4096\n\n;; QUESTION SECTION:\n;itsecgames.com.\tIN\t MX\n","response":";; opcode: QUERY, status: NOERROR, id: 45112\n;; flags: qr rd ra; QUERY: 1, ANSWER: 1, AUTHORITY: 0, ADDITIONAL: 1\n\n;; OPT PSEUDOSECTION:\n; EDNS: version 0; flags:; udp: 512\n\n;; QUESTION SECTION:\n;itsecgames.com.\tIN\t MX\n\n;; ANSWER SECTION:\nitsecgames.com.\t3600\tIN\tMX\t5 itsecgames-com.mail.protection.outlook.com.\n","timestamp":"2025-08-01T22:02:30.2728492+05:30","matcher-status":true}
{"template":"\\dns\\txt-fingerprint.yaml","template-url":"https://cloud.projectdiscovery.io/public/txt-fingerprint","template-id":"txt-fingerprint","template-path":"C:\\Users\\<USER>\\nuclei-templates\\dns\\txt-fingerprint.yaml","info":{"name":"DNS TXT Record Detected","author":["pdteam"],"tags":["dns","txt"],"description":"A DNS TXT record was detected. The TXT record lets a domain admin leave notes on a DNS server.","reference":["https://www.netspi.com/blog/technical/network-penetration-testing/analyzing-dns-txt-records-to-fingerprint-service-providers/"],"severity":"info","metadata":{"max-request":1},"classification":{"cve-id":null,"cwe-id":["cwe-200"]}},"type":"dns","host":"itsecgames.com","matched-at":"itsecgames.com","extracted-results":["\"v=spf1 mx a include:spf.protection.outlook.com include:servers.mcsv.net include:mme-srv-dc1.mme.local -all\""],"request":";; opcode: QUERY, status: NOERROR, id: 23044\n;; flags: rd ad; QUERY: 1, ANSWER: 0, AUTHORITY: 0, ADDITIONAL: 1\n\n;; OPT PSEUDOSECTION:\n; EDNS: version 0; flags:; udp: 4096\n\n;; QUESTION SECTION:\n;itsecgames.com.\tIN\t TXT\n","response":";; opcode: QUERY, status: NOERROR, id: 23044\n;; flags: qr rd ra; QUERY: 1, ANSWER: 1, AUTHORITY: 0, ADDITIONAL: 1\n\n;; OPT PSEUDOSECTION:\n; EDNS: version 0; flags:; udp: 512\n\n;; QUESTION SECTION:\n;itsecgames.com.\tIN\t TXT\n\n;; ANSWER SECTION:\nitsecgames.com.\t3600\tIN\tTXT\t\"v=spf1 mx a include:spf.protection.outlook.com include:servers.mcsv.net include:mme-srv-dc1.mme.local -all\"\n","timestamp":"2025-08-01T22:02:30.2774912+05:30","matcher-status":true}
{"template":"\\dns\\mx-service-detector.yaml","template-url":"https://cloud.projectdiscovery.io/public/mx-service-detector","template-id":"mx-service-detector","template-path":"C:\\Users\\<USER>\\nuclei-templates\\dns\\mx-service-detector.yaml","info":{"name":"Email Service Detector","author":["binaryfigments"],"tags":["dns","service"],"description":"An email service was detected. Check the email service or spam filter that is used for a domain.","severity":"info","metadata":{"max-request":1},"classification":{"cve-id":null,"cwe-id":["cwe-200"]}},"matcher-name":"Office 365","type":"dns","host":"itsecgames.com","matched-at":"itsecgames.com","request":";; opcode: QUERY, status: NOERROR, id: 12911\n;; flags: rd; QUERY: 1, ANSWER: 0, AUTHORITY: 0, ADDITIONAL: 1\n\n;; OPT PSEUDOSECTION:\n; EDNS: version 0; flags:; udp: 4096\n\n;; QUESTION SECTION:\n;itsecgames.com.\tIN\t MX\n","response":";; opcode: QUERY, status: NOERROR, id: 12911\n;; flags: qr rd ra; QUERY: 1, ANSWER: 1, AUTHORITY: 0, ADDITIONAL: 1\n\n;; OPT PSEUDOSECTION:\n; EDNS: version 0; flags:; udp: 512\n\n;; QUESTION SECTION:\n;itsecgames.com.\tIN\t MX\n\n;; ANSWER SECTION:\nitsecgames.com.\t3600\tIN\tMX\t5 itsecgames-com.mail.protection.outlook.com.\n","timestamp":"2025-08-01T22:02:30.2774912+05:30","matcher-status":true}
{"template":"\\dns\\spf-record-detect.yaml","template-id":"spf-record-detect","template-path":"C:\\Users\\<USER>\\nuclei-templates\\dns\\spf-record-detect.yaml","template-encoded":"aWQ6IHNwZi1yZWNvcmQtZGV0ZWN0DQoNCmluZm86CiAgbmFtZTogU1BGIFJlY29yZCAtIERldGVjdGlvbgogIGF1dGhvcjogcnhlcml1bQogIHNldmVyaXR5OiBpbmZvCiAgZGVzY3JpcHRpb246IHwKICAgIEFuIFNQRiBUWFQgcmVjb3JkIHdhcyBkZXRlY3RlZAogIHJlZmVyZW5jZToKICAgIC0gaHR0cHM6Ly93d3cubWltZWNhc3QuY29tL2NvbnRlbnQvaG93LXRvLWNyZWF0ZS1hbi1zcGYtdHh0LXJlY29yZAogIG1ldGFkYXRhOgogICAgbWF4LXJlcXVlc3Q6IDEKICB0YWdzOiBkbnMsc3BmCmRuczoNCiAgLSBuYW1lOiAie3tGUUROfX0iDQogICAgdHlwZTogVFhUDQogICAgbWF0Y2hlcnM6DQogICAgICAtIHR5cGU6IHdvcmQNCiAgICAgICAgd29yZHM6DQogICAgICAgICAgLSAidj1zcGYxIg0KDQogICAgZXh0cmFjdG9yczoNCiAgICAgIC0gdHlwZTogcmVnZXgNCiAgICAgICAgcmVnZXg6DQogICAgICAgICAgLSAidj1zcGYxKC4rKSIKIyBkaWdlc3Q6IDRhMGEwMDQ3MzA0NTAyMjEwMDk4ZTliYzdiNWY1YTZlZmU3MzRjNDUzOWQ3NGU0Y2ExMzk0NmUxMGRlZTRlNzMxMWE1YTZiZDg4NjFkM2RkNWEwMjIwNjg4ZmM1NTkwYjM2NmRhNDkyYjAyMWFjZDE0NmIzNGIyNjdlMzE4MDY2MzM3ZDZkMTA1ZDJiZmE3MjE0MTlmMjo5MjJjNjQ1OTAyMjI3OThiYjc2MWQ1YjZkOGU3Mjk1MA==","info":{"name":"SPF Record - Detection","author":["rxerium"],"tags":["dns","spf"],"description":"An SPF TXT record was detected\n","reference":["https://www.mimecast.com/content/how-to-create-an-spf-txt-record"],"severity":"info","metadata":{"max-request":1}},"type":"dns","host":"itsecgames.com","matched-at":"itsecgames.com","extracted-results":["v=spf1 mx a include:spf.protection.outlook.com include:servers.mcsv.net include:mme-srv-dc1.mme.local -all\""],"request":";; opcode: QUERY, status: NOERROR, id: 13681\n;; flags: rd ad; QUERY: 1, ANSWER: 0, AUTHORITY: 0, ADDITIONAL: 1\n\n;; OPT PSEUDOSECTION:\n; EDNS: version 0; flags:; udp: 4096\n\n;; QUESTION SECTION:\n;itsecgames.com.\tIN\t TXT\n","response":";; opcode: QUERY, status: NOERROR, id: 13681\n;; flags: qr rd ra; QUERY: 1, ANSWER: 1, AUTHORITY: 0, ADDITIONAL: 1\n\n;; OPT PSEUDOSECTION:\n; EDNS: version 0; flags:; udp: 512\n\n;; QUESTION SECTION:\n;itsecgames.com.\tIN\t TXT\n\n;; ANSWER SECTION:\nitsecgames.com.\t3600\tIN\tTXT\t\"v=spf1 mx a include:spf.protection.outlook.com include:servers.mcsv.net include:mme-srv-dc1.mme.local -all\"\n","timestamp":"2025-08-01T22:02:30.2815403+05:30","matcher-status":true}
{"template":"\\dns\\nameserver-fingerprint.yaml","template-url":"https://cloud.projectdiscovery.io/public/nameserver-fingerprint","template-id":"nameserver-fingerprint","template-path":"C:\\Users\\<USER>\\nuclei-templates\\dns\\nameserver-fingerprint.yaml","info":{"name":"NS Record Detection","author":["pdteam"],"tags":["dns","ns"],"description":"An NS record was detected. An NS record delegates a subdomain to a set of name servers.","severity":"info","metadata":{"max-request":1},"classification":{"cve-id":null,"cwe-id":["cwe-200"]}},"type":"dns","host":"itsecgames.com","matched-at":"itsecgames.com","extracted-results":["ns53.domaincontrol.com.","ns54.domaincontrol.com."],"request":";; opcode: QUERY, status: NOERROR, id: 23073\n;; flags: rd; QUERY: 1, ANSWER: 0, AUTHORITY: 0, ADDITIONAL: 1\n\n;; OPT PSEUDOSECTION:\n; EDNS: version 0; flags:; udp: 4096\n\n;; QUESTION SECTION:\n;itsecgames.com.\tIN\t NS\n","response":";; opcode: QUERY, status: NOERROR, id: 23073\n;; flags: qr rd ra; QUERY: 1, ANSWER: 2, AUTHORITY: 0, ADDITIONAL: 1\n\n;; OPT PSEUDOSECTION:\n; EDNS: version 0; flags:; udp: 1232\n\n;; QUESTION SECTION:\n;itsecgames.com.\tIN\t NS\n\n;; ANSWER SECTION:\nitsecgames.com.\t3600\tIN\tNS\tns53.domaincontrol.com.\nitsecgames.com.\t3600\tIN\tNS\tns54.domaincontrol.com.\n","timestamp":"2025-08-01T22:02:30.312996+05:30","matcher-status":true}
{"template":"\\dns\\caa-fingerprint.yaml","template-url":"https://cloud.projectdiscovery.io/public/caa-fingerprint","template-id":"caa-fingerprint","template-path":"C:\\Users\\<USER>\\nuclei-templates\\dns\\caa-fingerprint.yaml","info":{"name":"CAA Record","author":["pdteam"],"tags":["dns","caa"],"description":"A CAA record was discovered. A CAA record is used to specify which certificate authorities (CAs) are allowed to issue certificates for a domain.","reference":["https://support.dnsimple.com/articles/caa-record/#whats-a-caa-record"],"severity":"info","metadata":{"max-request":1},"classification":{"cve-id":null,"cwe-id":["cwe-200"]}},"type":"dns","host":"itsecgames.com","matched-at":"itsecgames.com","request":";; opcode: QUERY, status: NOERROR, id: 58103\n;; flags: rd; QUERY: 1, ANSWER: 0, AUTHORITY: 0, ADDITIONAL: 1\n\n;; OPT PSEUDOSECTION:\n; EDNS: version 0; flags:; udp: 4096\n\n;; QUESTION SECTION:\n;itsecgames.com.\tIN\t CAA\n","response":";; opcode: QUERY, status: NOERROR, id: 58103\n;; flags: qr rd ra; QUERY: 1, ANSWER: 0, AUTHORITY: 1, ADDITIONAL: 1\n\n;; OPT PSEUDOSECTION:\n; EDNS: version 0; flags:; udp: 1232\n\n;; QUESTION SECTION:\n;itsecgames.com.\tIN\t CAA\n\n;; AUTHORITY SECTION:\nitsecgames.com.\t600\tIN\tSOA\tns53.domaincontrol.com. dns.jomax.net. 2025011303 28800 7200 604800 600\n","timestamp":"2025-08-01T22:02:30.312996+05:30","matcher-status":true}

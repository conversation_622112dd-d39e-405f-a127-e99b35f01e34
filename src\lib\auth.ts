/**
 * Authentication and Authorization Utilities
 *
 * This module provides comprehensive authentication services including:
 * - Password hashing and verification using bcrypt
 * - JWT token creation and verification using jose library
 * - Session management with secure HTTP-only cookies
 * - User authentication helpers for API routes
 *
 * Security Features:
 * - bcrypt with 12 salt rounds for password hashing
 * - JWT tokens with 7-day expiration
 * - Secure cookie configuration (HttpOnly, SameSite, Secure)
 * - Token verification with error handling
 *
 * <AUTHOR> Scanner Team
 * @version 1.0.0
 */

import bcrypt from 'bcryptjs'
import { SignJWT, jwtVerify } from 'jose'
import { cookies } from 'next/headers'

/**
 * JWT secret key encoded for jose library
 * Uses environment variable or fallback for development
 */
const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'fallback-secret-key'
)

/**
 * JWT payload interface defining the structure of token claims
 *
 * @interface JWTPayload
 * @property {string} userId - Unique user identifier
 * @property {string} email - User email address
 * @property {number} [iat] - Issued at timestamp (optional, auto-generated)
 * @property {number} [exp] - Expiration timestamp (optional, auto-generated)
 */
export interface JWTPayload {
  userId: string
  email: string
  iat?: number
  exp?: number
}

// ============================================================================
// PASSWORD HASHING UTILITIES
// ============================================================================

/**
 * Hash a plain text password using bcrypt with high security salt rounds
 *
 * Uses 12 salt rounds which provides strong security while maintaining
 * reasonable performance. This is considered secure for production use.
 *
 * @param {string} password - Plain text password to hash
 * @returns {Promise<string>} Hashed password string
 *
 * @example
 * const hashedPassword = await hashPassword('userPassword123!')
 * // Returns: $2a$12$...
 */
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 12 // High security level - takes ~300ms to hash
  return bcrypt.hash(password, saltRounds)
}

/**
 * Verify a plain text password against a hashed password
 *
 * Uses bcrypt's built-in comparison which is timing-attack resistant
 * and handles salt extraction automatically.
 *
 * @param {string} password - Plain text password to verify
 * @param {string} hashedPassword - Previously hashed password from database
 * @returns {Promise<boolean>} True if password matches, false otherwise
 *
 * @example
 * const isValid = await verifyPassword('userInput', storedHash)
 * if (isValid) {
 *   // Password is correct
 * }
 */
export async function verifyPassword(
  password: string,
  hashedPassword: string
): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword)
}

// ============================================================================
// JWT TOKEN UTILITIES
// ============================================================================

/**
 * Create a signed JWT token with user claims
 *
 * Generates a JWT token with HS256 algorithm, automatic issued-at timestamp,
 * and 7-day expiration. The token contains user identification information
 * for session management.
 *
 * @param {Omit<JWTPayload, 'iat' | 'exp'>} payload - User data to encode (userId, email)
 * @returns {Promise<string>} Signed JWT token string
 *
 * @example
 * const token = await createToken({
 *   userId: 'user-123',
 *   email: '<EMAIL>'
 * })
 */
export async function createToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): Promise<string> {
  return new SignJWT(payload)
    .setProtectedHeader({ alg: 'HS256' }) // HMAC SHA-256 algorithm
    .setIssuedAt() // Current timestamp
    .setExpirationTime('7d') // 7 days from now
    .sign(JWT_SECRET)
}

/**
 * Verify and decode a JWT token
 *
 * Validates the token signature, expiration, and structure.
 * Returns null if token is invalid, expired, or malformed.
 *
 * @param {string} token - JWT token string to verify
 * @returns {Promise<JWTPayload | null>} Decoded payload or null if invalid
 *
 * @example
 * const payload = await verifyToken(tokenFromCookie)
 * if (payload) {
 *   console.log('User ID:', payload.userId)
 * }
 */
export async function verifyToken(token: string): Promise<JWTPayload | null> {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET)
    return payload as unknown as JWTPayload
  } catch (error) {
    console.error('Token verification failed:', error)
    return null
  }
}

// ============================================================================
// SESSION MANAGEMENT
// ============================================================================

/**
 * Set authentication cookie with secure configuration
 *
 * Creates an HTTP-only cookie with the JWT token. The cookie is configured
 * with security best practices including SameSite protection and secure
 * flag for production environments.
 *
 * @param {string} token - JWT token to store in cookie
 * @returns {Promise<void>}
 *
 * @example
 * await setAuthCookie(jwtToken)
 * // Cookie will be automatically sent with subsequent requests
 */
export async function setAuthCookie(token: string): Promise<void> {
  const cookieStore = await cookies()
  cookieStore.set('auth-token', token, {
    httpOnly: true, // Prevents XSS attacks by making cookie inaccessible to JavaScript
    secure: process.env.NODE_ENV === 'production', // HTTPS only in production
    sameSite: 'lax', // CSRF protection while allowing normal navigation
    maxAge: 60 * 60 * 24 * 7, // 7 days in seconds
    path: '/', // Available for entire application
  })
}

/**
 * Retrieve authentication token from cookie
 *
 * Extracts the JWT token from the HTTP-only cookie if it exists.
 * Returns null if no token is found.
 *
 * @returns {Promise<string | null>} JWT token or null if not found
 *
 * @example
 * const token = await getAuthCookie()
 * if (token) {
 *   const user = await verifyToken(token)
 * }
 */
export async function getAuthCookie(): Promise<string | null> {
  const cookieStore = await cookies()
  const token = cookieStore.get('auth-token')
  return token?.value || null
}

/**
 * Remove authentication cookie (logout)
 *
 * Deletes the auth-token cookie, effectively logging out the user.
 * This is called during logout process.
 *
 * @returns {Promise<void>}
 *
 * @example
 * await removeAuthCookie()
 * // User is now logged out
 */
export async function removeAuthCookie(): Promise<void> {
  const cookieStore = await cookies()
  cookieStore.delete('auth-token')
}

// ============================================================================
// AUTHENTICATION HELPERS
// ============================================================================

/**
 * Get current authenticated user from session
 *
 * Retrieves and verifies the current user's JWT token from cookies.
 * Returns user payload if valid, null if not authenticated.
 *
 * @returns {Promise<JWTPayload | null>} Current user data or null
 *
 * @example
 * const user = await getCurrentUser()
 * if (user) {
 *   console.log('Logged in as:', user.email)
 * } else {
 *   // User not authenticated
 * }
 */
export async function getCurrentUser(): Promise<JWTPayload | null> {
  const token = await getAuthCookie()
  if (!token) return null

  return verifyToken(token)
}

/**
 * Require authentication or throw error
 *
 * Helper function that ensures a user is authenticated.
 * Throws an error if no valid session exists, otherwise returns user data.
 * Useful for protecting API routes and server actions.
 *
 * @returns {Promise<JWTPayload>} Current user data
 * @throws {Error} If user is not authenticated
 *
 * @example
 * // In API route
 * try {
 *   const user = await requireAuth()
 *   // User is authenticated, proceed with request
 * } catch (error) {
 *   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
 * }
 */
export async function requireAuth(): Promise<JWTPayload> {
  const user = await getCurrentUser()
  if (!user) {
    throw new Error('Authentication required')
  }
  return user
}

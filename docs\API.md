# CTB Scanner - API Documentation

## Overview

CTB Scanner provides a comprehensive RESTful API for vulnerability scanning, asset management, and security reporting. All API endpoints require authentication unless otherwise specified.

## Base URL

```
Development: http://localhost:3000/api
Production: https://your-domain.com/api
```

## Authentication

### JWT Token Authentication

All protected endpoints require a valid JWT token in the Authorization header or as an HTTP-only cookie.

```http
Authorization: Bearer <jwt_token>
```

### Rate Limiting

- **Authentication endpoints**: 5 requests per 15 minutes per IP
- **General endpoints**: 60 requests per minute per user
- **Scan endpoints**: 10 requests per minute per user

## Response Format

### Success Response
```json
{
  "success": true,
  "data": { ... },
  "message": "Operation completed successfully"
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": { ... }
  }
}
```

### Pagination Response
```json
{
  "success": true,
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "totalPages": 10,
    "hasNext": true,
    "hasPrev": false
  }
}
```

## Authentication Endpoints

### POST /api/auth/signup

Register a new user account.

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "companyName": "Acme Corp",
  "country": "United States",
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "confirmPassword": "SecurePass123!"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_123",
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "companyName": "Acme Corp",
      "country": "United States"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  },
  "message": "Account created successfully"
}
```

### POST /api/auth/login

Authenticate user and create session.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123!"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_123",
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  },
  "message": "Login successful"
}
```

### POST /api/auth/logout

Logout user and clear session.

**Response:**
```json
{
  "success": true,
  "message": "Logout successful"
}
```

### GET /api/auth/me

Get current authenticated user information.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "user_123",
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "companyName": "Acme Corp",
    "country": "United States",
    "createdAt": "2024-01-01T00:00:00.000Z"
  }
}
```

## Vulnerability Scanning Endpoints

### POST /api/scans

Initiate a new vulnerability scan.

**Request Body:**
```json
{
  "url": "https://example.com",
  "scanType": "quick", // "quick", "deep", "custom"
  "severity": ["critical", "high", "medium", "low", "info"],
  "tags": ["cve", "xss", "sqli"],
  "templates": ["specific-template-id"],
  "excludeTemplates": ["template-to-exclude"],
  "options": {
    "timeout": 300,
    "retries": 3,
    "rateLimit": 150
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "scanId": "scan_123",
    "status": "PENDING",
    "targetUrl": "https://example.com",
    "estimatedDuration": 300,
    "queuePosition": 2
  },
  "message": "Scan initiated successfully"
}
```

### GET /api/scans

Get list of scans with pagination and filtering.

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 10, max: 50)
- `status` (string): Filter by scan status (PENDING, RUNNING, COMPLETED, FAILED, CANCELLED)
- `search` (string): Search by target URL
- `sortBy` (string): Sort field (createdAt, status, duration)
- `sortOrder` (string): Sort order (asc, desc)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "scan_123",
      "targetUrl": "https://example.com",
      "status": "COMPLETED",
      "startedAt": "2024-01-01T10:00:00.000Z",
      "completedAt": "2024-01-01T10:05:00.000Z",
      "duration": 300,
      "totalVulns": 15,
      "criticalVulns": 2,
      "highVulns": 5,
      "mediumVulns": 6,
      "lowVulns": 2,
      "infoVulns": 0,
      "nucleiVersion": "v2.9.15",
      "templateCount": 4500
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "totalPages": 3,
    "hasNext": true,
    "hasPrev": false
  }
}
```

### GET /api/scans/[id]

Get detailed scan results including vulnerabilities.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "scan_123",
    "targetUrl": "https://example.com",
    "status": "COMPLETED",
    "startedAt": "2024-01-01T10:00:00.000Z",
    "completedAt": "2024-01-01T10:05:00.000Z",
    "duration": 300,
    "totalVulns": 15,
    "vulnerabilities": [
      {
        "id": "vuln_456",
        "templateId": "CVE-2023-1234",
        "name": "SQL Injection in Login Form",
        "severity": "HIGH",
        "description": "SQL injection vulnerability found in login form",
        "host": "https://example.com",
        "matchedAt": "/login",
        "tags": ["sqli", "cve"],
        "reference": ["https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-1234"],
        "timestamp": "2024-01-01T10:02:30.000Z"
      }
    ],
    "asset": {
      "id": "asset_789",
      "domain": "example.com",
      "title": "Example Website"
    }
  }
}
```

### DELETE /api/scans/[id]

Delete a scan and its associated vulnerabilities.

**Response:**
```json
{
  "success": true,
  "message": "Scan deleted successfully"
}
```

### GET /api/scans/[id]/events

Get real-time scan events (Server-Sent Events).

**Response:** Stream of events
```
data: {"type": "status", "status": "RUNNING", "progress": 25}

data: {"type": "vulnerability", "vulnerability": {...}}

data: {"type": "completed", "summary": {...}}
```

## Asset Management Endpoints

### GET /api/assets

Get list of assets with scan statistics.

**Query Parameters:**
- `page` (number): Page number
- `limit` (number): Items per page
- `status` (string): Filter by asset status (ACTIVE, INACTIVE, ARCHIVED)
- `search` (string): Search by URL, domain, or title
- `sortBy` (string): Sort field
- `sortOrder` (string): Sort order

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "asset_789",
      "url": "https://example.com",
      "domain": "example.com",
      "title": "Example Website",
      "description": "Main company website",
      "technology": ["nginx", "php", "mysql"],
      "status": "ACTIVE",
      "lastScanned": "2024-01-01T10:00:00.000Z",
      "totalScans": 5,
      "totalVulnerabilities": 12,
      "criticalVulns": 1,
      "highVulns": 3,
      "mediumVulns": 5,
      "lowVulns": 3,
      "createdAt": "2023-12-01T00:00:00.000Z"
    }
  ],
  "pagination": {...}
}
```

### POST /api/assets

Create a new asset manually.

**Request Body:**
```json
{
  "url": "https://example.com",
  "title": "Example Website",
  "description": "Main company website",
  "technology": ["nginx", "php", "mysql"]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "asset_789",
    "url": "https://example.com",
    "domain": "example.com",
    "title": "Example Website",
    "status": "ACTIVE",
    "createdAt": "2024-01-01T00:00:00.000Z"
  },
  "message": "Asset created successfully"
}
```

### GET /api/assets/[id]

Get detailed asset information with scan history.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "asset_789",
    "url": "https://example.com",
    "domain": "example.com",
    "title": "Example Website",
    "description": "Main company website",
    "technology": ["nginx", "php", "mysql"],
    "status": "ACTIVE",
    "lastScanned": "2024-01-01T10:00:00.000Z",
    "scans": [
      {
        "id": "scan_123",
        "status": "COMPLETED",
        "startedAt": "2024-01-01T10:00:00.000Z",
        "totalVulns": 15
      }
    ],
    "vulnerabilities": [
      {
        "id": "vuln_456",
        "name": "SQL Injection",
        "severity": "HIGH",
        "scanId": "scan_123"
      }
    ]
  }
}
```

### PUT /api/assets/[id]

Update asset information.

**Request Body:**
```json
{
  "title": "Updated Website Title",
  "description": "Updated description",
  "status": "ACTIVE"
}
```

### DELETE /api/assets/[id]

Delete an asset and all associated data.

**Response:**
```json
{
  "success": true,
  "message": "Asset deleted successfully"
}
```

## Vulnerability Endpoints

### GET /api/vulnerabilities

Get list of vulnerabilities across all assets.

**Query Parameters:**
- `page` (number): Page number
- `limit` (number): Items per page
- `severity` (string): Filter by severity (CRITICAL, HIGH, MEDIUM, LOW, INFO, UNKNOWN)
- `search` (string): Search by name, template ID, or host
- `scanId` (string): Filter by scan ID
- `assetId` (string): Filter by asset ID
- `templateId` (string): Filter by template ID

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "vuln_456",
      "templateId": "CVE-2023-1234",
      "name": "SQL Injection in Login Form",
      "severity": "HIGH",
      "description": "SQL injection vulnerability found",
      "host": "https://example.com",
      "matchedAt": "/login",
      "tags": ["sqli", "cve"],
      "reference": ["https://cve.mitre.org/..."],
      "timestamp": "2024-01-01T10:02:30.000Z",
      "scan": {
        "id": "scan_123",
        "targetUrl": "https://example.com"
      },
      "asset": {
        "id": "asset_789",
        "domain": "example.com"
      }
    }
  ],
  "pagination": {...}
}
```

### GET /api/vulnerabilities/[id]

Get detailed vulnerability information.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "vuln_456",
    "templateId": "CVE-2023-1234",
    "name": "SQL Injection in Login Form",
    "severity": "HIGH",
    "description": "Detailed vulnerability description...",
    "host": "https://example.com",
    "matchedAt": "/login",
    "tags": ["sqli", "cve"],
    "reference": ["https://cve.mitre.org/..."],
    "matcher": "SQL injection pattern detected",
    "extractedResults": ["admin' OR '1'='1"],
    "request": "POST /login HTTP/1.1...",
    "response": "HTTP/1.1 200 OK...",
    "curlCommand": "curl -X POST ...",
    "timestamp": "2024-01-01T10:02:30.000Z",
    "scan": {
      "id": "scan_123",
      "targetUrl": "https://example.com",
      "startedAt": "2024-01-01T10:00:00.000Z"
    },
    "asset": {
      "id": "asset_789",
      "domain": "example.com",
      "title": "Example Website"
    }
  }
}
```

## Dashboard Endpoints

### GET /api/dashboard/stats

Get comprehensive dashboard statistics.

**Response:**
```json
{
  "success": true,
  "data": {
    "overview": {
      "totalAssets": 25,
      "totalScans": 150,
      "totalVulnerabilities": 45,
      "activeScans": 2
    },
    "vulnerabilitySeverityCount": {
      "CRITICAL": 5,
      "HIGH": 12,
      "MEDIUM": 18,
      "LOW": 8,
      "INFO": 2,
      "UNKNOWN": 0
    },
    "recentScans": [
      {
        "id": "scan_123",
        "targetUrl": "https://example.com",
        "status": "COMPLETED",
        "totalVulns": 15,
        "completedAt": "2024-01-01T10:05:00.000Z"
      }
    ],
    "topVulnerableAssets": [
      {
        "id": "asset_789",
        "domain": "example.com",
        "totalVulnerabilities": 25,
        "criticalVulns": 3
      }
    ],
    "activityData": [
      {
        "date": "2024-01-01",
        "scans": 5,
        "vulnerabilities": 23
      }
    ],
    "scanStatusDistribution": {
      "COMPLETED": 145,
      "FAILED": 3,
      "RUNNING": 2,
      "PENDING": 0
    }
  }
}
```

## Queue Management Endpoints

### GET /api/queue/status

Get current job queue status and scan concurrency.

**Response:**
```json
{
  "success": true,
  "data": {
    "queue": {
      "pending": 3,
      "running": 1,
      "completed": 147,
      "failed": 2
    },
    "concurrency": {
      "maxConcurrentScans": 3,
      "maxConcurrentScansPerUser": 1,
      "currentRunningScans": 1
    },
    "userQueue": {
      "pendingScans": 0,
      "runningScans": 1,
      "canStartNewScan": false
    },
    "timestamp": "2024-01-01T12:00:00.000Z"
  }
}
```

## Export Endpoints

### GET /api/export/scans

Export scan data in various formats.

**Query Parameters:**
- `format` (string): Export format (csv, json, pdf)
- `scanIds` (string): Comma-separated scan IDs
- `dateFrom` (string): Start date filter
- `dateTo` (string): End date filter

**Response:** File download or JSON data

### GET /api/export/assets

Export asset inventory.

**Query Parameters:**
- `format` (string): Export format (csv, json)
- `status` (string): Filter by status
- `includeVulns` (boolean): Include vulnerability counts

### GET /api/export/vulnerabilities

Export vulnerability database.

**Query Parameters:**
- `format` (string): Export format (csv, json)
- `severity` (string): Filter by severity
- `scanIds` (string): Filter by scan IDs

### POST /api/reports/pdf

Generate PDF reports.

**Request Body:**
```json
{
  "type": "scan", // "scan", "asset", "executive"
  "scanId": "scan_123",
  "options": {
    "includeSummary": true,
    "includeDetails": true,
    "includeRecommendations": true
  }
}
```

**Response:** PDF file download

## Health Check Endpoint

### GET /api/health

Check application health status.

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2024-01-01T12:00:00.000Z",
    "version": "1.0.0",
    "services": {
      "database": "connected",
      "nuclei": "available",
      "queue": "running"
    },
    "uptime": 86400
  }
}
```

## Error Codes

| Code | Description |
|------|-------------|
| `VALIDATION_ERROR` | Invalid input data |
| `AUTHENTICATION_REQUIRED` | Missing or invalid authentication |
| `AUTHORIZATION_FAILED` | Insufficient permissions |
| `RESOURCE_NOT_FOUND` | Requested resource not found |
| `RATE_LIMIT_EXCEEDED` | Too many requests |
| `SCAN_LIMIT_EXCEEDED` | User scan limit reached |
| `INVALID_URL` | Invalid or unreachable URL |
| `NUCLEI_ERROR` | Nuclei scanner error |
| `DATABASE_ERROR` | Database operation failed |
| `INTERNAL_ERROR` | Internal server error |

## Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `429` - Too Many Requests
- `500` - Internal Server Error
- `503` - Service Unavailable

## SDK Examples

### JavaScript/Node.js

```javascript
const CTBScanner = require('ctb-scanner-sdk');

const client = new CTBScanner({
  baseURL: 'https://your-domain.com/api',
  token: 'your-jwt-token'
});

// Start a scan
const scan = await client.scans.create({
  url: 'https://example.com',
  scanType: 'quick'
});

// Get scan results
const results = await client.scans.get(scan.scanId);
```

### Python

```python
import ctb_scanner

client = ctb_scanner.Client(
    base_url='https://your-domain.com/api',
    token='your-jwt-token'
)

# Start a scan
scan = client.scans.create(
    url='https://example.com',
    scan_type='quick'
)

# Get scan results
results = client.scans.get(scan['scanId'])
```

---

For more information, see the [Development Guide](DEVELOPMENT.md) and [Deployment Guide](DEPLOYMENT.md).

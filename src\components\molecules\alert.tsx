import * as React from 'react'
import { cn } from '@/lib/utils'
import { Icon } from '../atoms/icon'
import { Button } from '../atoms/button'
import { Heading } from '../atoms/heading'
import { Text } from '../atoms/text'
import { AlertTriangle, CheckCircle, Info, XCircle, X } from 'lucide-react'

export interface AlertProps {
  variant?: 'info' | 'success' | 'warning' | 'error'
  title?: string
  children: React.ReactNode
  dismissible?: boolean
  onDismiss?: () => void
  className?: string
}

const alertVariants = {
  info: {
    container: 'bg-blue-50 border-blue-200 text-blue-800',
    icon: Info,
    iconColor: 'text-blue-600'
  },
  success: {
    container: 'bg-green-50 border-green-200 text-green-800',
    icon: CheckCircle,
    iconColor: 'text-green-600'
  },
  warning: {
    container: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    icon: AlertTriangle,
    iconColor: 'text-yellow-600'
  },
  error: {
    container: 'bg-red-50 border-red-200 text-red-800',
    icon: XCircle,
    iconColor: 'text-red-600'
  }
}

export const Alert: React.FC<AlertProps> = ({
  variant = 'info',
  title,
  children,
  dismissible = false,
  onDismiss,
  className
}) => {
  const config = alertVariants[variant]

  return (
    <div
      className={cn(
        'relative rounded-lg border p-4',
        config.container,
        className
      )}
    >
      <div className="flex">
        <div className="flex-shrink-0">
          <Icon 
            icon={config.icon} 
            size="md" 
            className={config.iconColor}
          />
        </div>
        
        <div className="ml-3 flex-1">
          {title && (
            <Heading level={5} className="mb-1 text-current">
              {title}
            </Heading>
          )}
          
          <div className="text-current">
            {typeof children === 'string' ? (
              <Text variant="caption" className="text-current">
                {children}
              </Text>
            ) : (
              children
            )}
          </div>
        </div>
        
        {dismissible && onDismiss && (
          <div className="ml-auto pl-3">
            <Button
              variant="ghost"
              size="icon"
              onClick={onDismiss}
              className="h-6 w-6 p-0 text-current hover:bg-black/10"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}

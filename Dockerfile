# CTB Scanner Production Dockerfile
# Multi-stage build for optimized production image

# =============================================================================
# Stage 1: Dependencies and Build
# =============================================================================
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Install system dependencies for building
RUN apk add --no-cache \
    libc6-compat \
    python3 \
    make \
    g++ \
    git

# Copy package files
COPY package*.json ./
COPY prisma ./prisma/

# Install all dependencies (including dev dependencies for build)
RUN npm ci && npm cache clean --force

# Copy source code
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Build the application
RUN npm run build

# Install only production dependencies
RUN npm ci --only=production && npm cache clean --force

# =============================================================================
# Stage 2: Nuclei Scanner
# =============================================================================
FROM golang:1.21-alpine AS nuclei-builder

# Install Nuclei
RUN go install -v github.com/projectdiscovery/nuclei/v3/cmd/nuclei@latest

# =============================================================================
# Stage 3: Production Runtime
# =============================================================================
FROM node:18-alpine AS runner

# Set environment variables
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# Create app user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Install runtime dependencies
RUN apk add --no-cache \
    dumb-init \
    curl \
    ca-certificates \
    tzdata \
    tini

# Set working directory
WORKDIR /app

# Copy built application from builder stage
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
COPY --from=builder --chown=nextjs:nodejs /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/prisma ./prisma
COPY --from=builder --chown=nextjs:nodejs /app/package.json ./package.json

# Copy Nuclei binary from nuclei-builder stage
COPY --from=nuclei-builder /go/bin/nuclei /usr/local/bin/nuclei
RUN chmod +x /usr/local/bin/nuclei

# Create directories for Nuclei templates and logs
RUN mkdir -p /opt/nuclei-templates /var/log/ctb-scanner
RUN chown -R nextjs:nodejs /opt/nuclei-templates /var/log/ctb-scanner

# Create health check script
RUN echo '#!/bin/sh\ncurl -f http://localhost:3000/api/health || exit 1' > /usr/local/bin/healthcheck
RUN chmod +x /usr/local/bin/healthcheck

# Switch to non-root user
USER nextjs

# Update Nuclei templates
RUN nuclei -update-templates -templates-directory /opt/nuclei-templates

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD /usr/local/bin/healthcheck

# Start the application
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "server.js"]

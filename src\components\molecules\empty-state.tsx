import * as React from 'react'
import { cn } from '@/lib/utils'
import { LucideIcon } from 'lucide-react'
import { Button } from '../atoms/button'
import { Heading } from '../atoms/heading'
import { Text } from '../atoms/text'
import { Icon } from '../atoms/icon'

export interface EmptyStateProps {
  icon?: LucideIcon
  title: string
  description?: string
  action?: {
    label: string
    onClick: () => void
    variant?: 'default' | 'outline'
  }
  className?: string
  children?: React.ReactNode
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  icon,
  title,
  description,
  action,
  className,
  children,
}) => {
  return (
    <div className={cn('text-center py-12', className)}>
      {icon && (
        <div className="mx-auto mb-4">
          <Icon icon={icon} size="xl" color="muted" />
        </div>
      )}
      
      <Heading level={3} className="mb-2">
        {title}
      </Heading>
      
      {description && (
        <Text variant="body" color="muted" className="mb-6 max-w-md mx-auto">
          {description}
        </Text>
      )}
      
      {action && (
        <Button
          onClick={action.onClick}
          variant={action.variant || 'default'}
          className="mb-4"
        >
          {action.label}
        </Button>
      )}
      
      {children}
    </div>
  )
}

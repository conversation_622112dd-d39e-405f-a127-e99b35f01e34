/**
 * Caching System for Performance Optimization
 * 
 * This module provides a comprehensive caching system with multiple
 * storage backends and cache strategies for improved application performance.
 */

interface CacheEntry<T> {
  value: T
  expiresAt: number
  createdAt: number
  accessCount: number
  lastAccessed: number
}

interface CacheOptions {
  ttl?: number // Time to live in milliseconds
  maxSize?: number // Maximum number of entries
  strategy?: 'lru' | 'lfu' | 'fifo' // Eviction strategy
}

interface CacheStats {
  hits: number
  misses: number
  size: number
  maxSize: number
  hitRate: number
  evictions: number
}

/**
 * In-memory cache implementation with multiple eviction strategies
 */
export class MemoryCache<T> {
  private cache = new Map<string, CacheEntry<T>>()
  private stats = { hits: 0, misses: 0, evictions: 0 }
  private readonly maxSize: number
  private readonly defaultTtl: number
  private readonly strategy: 'lru' | 'lfu' | 'fifo'

  constructor(options: CacheOptions = {}) {
    this.maxSize = options.maxSize || 1000
    this.defaultTtl = options.ttl || 300000 // 5 minutes default
    this.strategy = options.strategy || 'lru'
  }

  /**
   * Get value from cache
   */
  get(key: string): T | null {
    const entry = this.cache.get(key)
    
    if (!entry) {
      this.stats.misses++
      return null
    }

    // Check if expired
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key)
      this.stats.misses++
      return null
    }

    // Update access statistics
    entry.accessCount++
    entry.lastAccessed = Date.now()
    this.stats.hits++

    return entry.value
  }

  /**
   * Set value in cache
   */
  set(key: string, value: T, ttl?: number): void {
    const now = Date.now()
    const expiresAt = now + (ttl || this.defaultTtl)

    // Check if we need to evict entries
    if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
      this.evict()
    }

    this.cache.set(key, {
      value,
      expiresAt,
      createdAt: now,
      accessCount: 0,
      lastAccessed: now
    })
  }

  /**
   * Check if key exists in cache
   */
  has(key: string): boolean {
    const entry = this.cache.get(key)
    if (!entry) return false

    // Check if expired
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key)
      return false
    }

    return true
  }

  /**
   * Delete value from cache
   */
  delete(key: string): boolean {
    return this.cache.delete(key)
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear()
    this.stats = { hits: 0, misses: 0, evictions: 0 }
  }

  /**
   * Get current cache size
   */
  size(): number {
    return this.cache.size
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    const size = this.cache.size
    const total = this.stats.hits + this.stats.misses
    const hitRate = total > 0 ? this.stats.hits / total : 0

    return {
      hits: this.stats.hits,
      misses: this.stats.misses,
      size,
      maxSize: this.maxSize,
      hitRate: Math.round(hitRate * 100) / 100,
      evictions: this.stats.evictions
    }
  }

  /**
   * Reset cache statistics
   */
  resetStats(): void {
    this.stats = { hits: 0, misses: 0, evictions: 0 }
  }

  /**
   * Evict entries based on strategy
   */
  private evict(): void {
    if (this.cache.size === 0) return

    let keyToEvict: string | null = null

    switch (this.strategy) {
      case 'lru': // Least Recently Used
        keyToEvict = this.findLRU()
        break
      case 'lfu': // Least Frequently Used
        keyToEvict = this.findLFU()
        break
      case 'fifo': // First In, First Out
        keyToEvict = this.findFIFO()
        break
    }

    if (keyToEvict) {
      this.cache.delete(keyToEvict)
      this.stats.evictions++
    }
  }

  private findLRU(): string | null {
    let oldestKey: string | null = null
    let oldestTime = Infinity

    for (const [key, entry] of this.cache) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed
        oldestKey = key
      }
    }

    return oldestKey
  }

  private findLFU(): string | null {
    let leastUsedKey: string | null = null
    let leastCount = Infinity

    for (const [key, entry] of this.cache) {
      if (entry.accessCount < leastCount) {
        leastCount = entry.accessCount
        leastUsedKey = key
      }
    }

    return leastUsedKey
  }

  private findFIFO(): string | null {
    let oldestKey: string | null = null
    let oldestTime = Infinity

    for (const [key, entry] of this.cache) {
      if (entry.createdAt < oldestTime) {
        oldestTime = entry.createdAt
        oldestKey = key
      }
    }

    return oldestKey
  }

  /**
   * Clean up expired entries
   */
  cleanup(): number {
    const now = Date.now()
    let cleaned = 0

    for (const [key, entry] of this.cache) {
      if (now > entry.expiresAt) {
        this.cache.delete(key)
        cleaned++
      }
    }

    return cleaned
  }
}

/**
 * Cache manager with multiple cache instances
 */
export class CacheManager {
  private caches = new Map<string, MemoryCache<any>>()

  /**
   * Get or create a cache instance
   */
  getCache<T>(name: string, options?: CacheOptions): MemoryCache<T> {
    if (!this.caches.has(name)) {
      this.caches.set(name, new MemoryCache<T>(options))
    }
    return this.caches.get(name)!
  }

  /**
   * Get statistics for all caches
   */
  getAllStats(): Record<string, CacheStats> {
    const stats: Record<string, CacheStats> = {}
    
    for (const [name, cache] of this.caches) {
      stats[name] = cache.getStats()
    }

    return stats
  }

  /**
   * Clean up expired entries in all caches
   */
  cleanupAll(): Record<string, number> {
    const results: Record<string, number> = {}
    
    for (const [name, cache] of this.caches) {
      results[name] = cache.cleanup()
    }

    return results
  }

  /**
   * Get all cache names
   */
  getCacheNames(): string[] {
    return Array.from(this.caches.keys())
  }

  /**
   * Remove a cache instance
   */
  removeCache(name: string): boolean {
    return this.caches.delete(name)
  }

  /**
   * Clear all caches
   */
  clearAll(): void {
    for (const cache of this.caches.values()) {
      cache.clear()
    }
  }
}

// Global cache manager instance
export const cacheManager = new CacheManager()

// Pre-configured cache instances for common use cases
export const caches = {
  // Scan results cache (longer TTL, larger size)
  scans: cacheManager.getCache('scans', {
    ttl: 3600000, // 1 hour
    maxSize: 500,
    strategy: 'lru'
  }),

  // Vulnerability data cache (medium TTL)
  vulnerabilities: cacheManager.getCache('vulnerabilities', {
    ttl: 1800000, // 30 minutes
    maxSize: 1000,
    strategy: 'lfu'
  }),

  // Asset information cache (shorter TTL)
  assets: cacheManager.getCache('assets', {
    ttl: 900000, // 15 minutes
    maxSize: 200,
    strategy: 'lru'
  }),

  // User session cache (short TTL)
  sessions: cacheManager.getCache('sessions', {
    ttl: 300000, // 5 minutes
    maxSize: 100,
    strategy: 'lru'
  }),

  // API response cache (very short TTL)
  api: cacheManager.getCache('api', {
    ttl: 60000, // 1 minute
    maxSize: 500,
    strategy: 'fifo'
  })
}

/**
 * Cache decorator for functions
 */
export function cached<T extends (...args: any[]) => any>(
  cacheName: string,
  keyGenerator: (...args: Parameters<T>) => string,
  ttl?: number
) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    const cache = cacheManager.getCache(cacheName, { ttl })

    descriptor.value = async function (...args: Parameters<T>) {
      const key = keyGenerator(...args)
      
      // Try to get from cache first
      const cached = cache.get(key)
      if (cached !== null) {
        return cached
      }

      // Execute original method
      const result = await originalMethod.apply(this, args)
      
      // Cache the result
      cache.set(key, result, ttl)
      
      return result
    }

    return descriptor
  }
}

/**
 * Utility function to create cache keys
 */
export function createCacheKey(...parts: (string | number | boolean)[]): string {
  return parts.map(part => String(part)).join(':')
}

/**
 * Start periodic cache cleanup
 */
export function startCacheCleanup(intervalMs: number = 300000): NodeJS.Timeout {
  return setInterval(() => {
    const results = cacheManager.cleanupAll()
    const totalCleaned = Object.values(results).reduce((sum, count) => sum + count, 0)
    
    if (totalCleaned > 0) {
      console.log(`🧹 Cache cleanup: removed ${totalCleaned} expired entries`)
    }
  }, intervalMs)
}

/**
 * Get comprehensive cache statistics
 */
export function getCacheStats(): {
  caches: Record<string, CacheStats>
  total: {
    hits: number
    misses: number
    size: number
    hitRate: number
  }
} {
  const cacheStats = cacheManager.getAllStats()
  
  const total = Object.values(cacheStats).reduce(
    (acc, stats) => ({
      hits: acc.hits + stats.hits,
      misses: acc.misses + stats.misses,
      size: acc.size + stats.size,
      hitRate: 0 // Will be calculated below
    }),
    { hits: 0, misses: 0, size: 0, hitRate: 0 }
  )

  const totalRequests = total.hits + total.misses
  total.hitRate = totalRequests > 0 ? Math.round((total.hits / totalRequests) * 100) / 100 : 0

  return {
    caches: cacheStats,
    total
  }
}

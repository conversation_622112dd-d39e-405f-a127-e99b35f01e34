'use client'

import { useState, useEffect, useMemo } from 'react'
import {
  Search,
  ExternalLink,
  Filter,
  Eye,
  Database,
  Shield,
  AlertTriangle,
  Download,
  FileText,
  RefreshCw,
  Copy,
  Calendar,
  Clock,
  Target,
  Activity,
  SortAsc,
  SortDesc,
  MoreHorizontal,
  Globe
} from 'lucide-react'
import { <PERSON><PERSON>ontainer, PageHeader } from '@/components/layout'
import { Button, Input, Badge, Card, CardContent, CardHeader, CardTitle, Alert, Pagination } from '@/components/atoms'
import { NoSSR } from '@/components/no-ssr'
import Link from 'next/link'

interface Vulnerability {
  id: string
  templateId: string
  name: string
  severity: string
  description?: string
  host: string
  matchedAt: string
  timestamp: string
  scan: {
    id: string
    targetUrl: string
    status: string
    startedAt?: string
    completedAt?: string
    asset?: {
      id: string
      title: string
      domain: string
    }
  }
}

interface FilterState {
  search: string
  severity: string
  assetId: string
  scanStatus: string
  dateRange: string
  sortBy: string
  sortOrder: 'asc' | 'desc'
}

interface PaginationState {
  page: number
  limit: number
  total: number
  pages: number
}

export default function VulnerabilitiesPage() {
  const [vulnerabilities, setVulnerabilities] = useState<Vulnerability[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [severityFilter, setSeverityFilter] = useState<string>('')

  // Enhanced filtering and pagination state
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    severity: '',
    assetId: '',
    scanStatus: '',
    dateRange: '',
    sortBy: 'timestamp',
    sortOrder: 'desc'
  })

  const [pagination, setPagination] = useState<PaginationState>({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  })

  useEffect(() => {
    fetchVulnerabilities()
  }, [pagination.page, filters.search, filters.severity])

  // Sync search term with filters for backward compatibility
  useEffect(() => {
    setSearchTerm(filters.search)
    setSeverityFilter(filters.severity)
  }, [filters.search, filters.severity])

  // Reset to page 1 when filters change
  useEffect(() => {
    if (pagination.page !== 1) {
      setPagination(prev => ({ ...prev, page: 1 }))
    }
  }, [filters.search, filters.severity])

  const fetchVulnerabilities = async () => {
    try {
      setLoading(true)
      setError(null)
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString()
      })

      if (filters.search) {
        params.append('search', filters.search)
      }

      if (filters.severity) {
        params.append('severity', filters.severity)
      }

      const response = await fetch(`/api/vulnerabilities?${params}`)
      if (response.ok) {
        const data = await response.json()
        setVulnerabilities(data.vulnerabilities)
        setPagination(prev => ({
          ...prev,
          total: data.pagination.total,
          pages: data.pagination.pages
        }))
      } else {
        setError('Failed to fetch vulnerabilities')
      }
    } catch (error) {
      setError('Failed to fetch vulnerabilities')
      console.error('Failed to fetch vulnerabilities:', error)
    } finally {
      setLoading(false)
    }
  }

  // Helper functions
  const handleFilterChange = (key: keyof FilterState, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }))
  }

  const handleExport = async (format: 'json' | 'csv') => {
    try {
      // Build export URL with current filters
      const params = new URLSearchParams({
        format
      })

      if (filters.severity) {
        params.append('severity', filters.severity)
      }

      if (filters.search) {
        params.append('search', filters.search)
      }

      const response = await fetch(`/api/export/vulnerabilities?${params}`)

      if (!response.ok) {
        throw new Error('Export failed')
      }

      if (format === 'csv') {
        // For CSV, the response is already formatted as CSV content
        const csvContent = await response.text()
        const blob = new Blob([csvContent], { type: 'text/csv' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `vulnerabilities-export-${new Date().toISOString().split('T')[0]}.csv`
        a.click()
        URL.revokeObjectURL(url)
      } else {
        // For JSON, get the data and create blob
        const data = await response.json()
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `vulnerabilities-export-${new Date().toISOString().split('T')[0]}.json`
        a.click()
        URL.revokeObjectURL(url)
      }
    } catch (error) {
      console.error('Export failed:', error)
      // You might want to show a toast notification here
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity.toUpperCase()) {
      case 'CRITICAL':
        return 'bg-red-50 text-red-700 border-red-200'
      case 'HIGH':
        return 'bg-orange-50 text-orange-700 border-orange-200'
      case 'MEDIUM':
        return 'bg-yellow-50 text-yellow-700 border-yellow-200'
      case 'LOW':
        return 'bg-blue-50 text-blue-700 border-blue-200'
      case 'INFO':
        return 'bg-green-50 text-green-700 border-green-200'
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200'
    }
  }

  // Since we're using server-side pagination, we use vulnerabilities directly
  const displayedVulnerabilities = vulnerabilities



  // Define table columns with optimized widths to prevent horizontal scrolling
  const columns = [
    {
      key: 'vulnerability',
      header: 'Vulnerability',
      className: 'w-[35%]', // Fixed percentage width
      render: (vuln: Vulnerability) => (
        <div className="min-w-0">
          <div className="text-sm font-medium text-gray-900 truncate" title={vuln.name}>
            {vuln.name}
          </div>
          <div className="text-xs text-gray-500 truncate" title={vuln.templateId}>
            {vuln.templateId}
          </div>
        </div>
      ),
    },
    {
      key: 'severity',
      header: 'Severity',
      className: 'w-[12%]', // Fixed percentage width
      render: (vuln: Vulnerability) => (
        <Badge variant="outline" className={`text-xs px-2 py-1 ${getSeverityColor(vuln.severity)}`}>
          {vuln.severity.toLowerCase()}
        </Badge>
      ),
    },
    {
      key: 'asset',
      header: 'Asset',
      className: 'w-[20%]', // Fixed percentage width
      render: (vuln: Vulnerability) => (
        <div className="min-w-0">
          <div className="text-sm font-medium text-gray-900 truncate" title={vuln.scan.asset?.title || 'Unknown'}>
            {vuln.scan.asset?.title || 'Unknown'}
          </div>
          <div className="text-xs text-gray-500 truncate" title={vuln.scan.asset?.domain}>
            {vuln.scan.asset?.domain}
          </div>
        </div>
      ),
    },
    {
      key: 'host',
      header: 'Host',
      className: 'w-[20%]', // Fixed percentage width
      render: (vuln: Vulnerability) => (
        <div className="flex items-center min-w-0">
          <span className="text-sm text-gray-900 truncate flex-1" title={vuln.host}>
            {vuln.host}
          </span>
          <button
            onClick={() => window.open(vuln.matchedAt, '_blank')}
            className="ml-1 text-gray-400 hover:text-gray-600 flex-shrink-0"
            title="Open"
          >
            <ExternalLink className="h-3 w-3" />
          </button>
        </div>
      ),
    },
    {
      key: 'actions',
      header: 'Actions',
      className: 'w-[13%]', // Fixed percentage width
      render: (vuln: Vulnerability) => (
        <div className="flex space-x-1">
          <Link href={`/dashboard/scans/${vuln.scan.id}`}>
            <Button size="sm" variant="outline" className="text-xs px-1.5 py-1">
              <Eye className="h-3 w-3" />
            </Button>
          </Link>
          {vuln.scan.asset && (
            <Link href={`/dashboard/assets/${vuln.scan.asset.id}`}>
              <Button size="sm" variant="outline" className="text-xs px-1.5 py-1">
                <Database className="h-3 w-3" />
              </Button>
            </Link>
          )}
        </div>
      ),
    },
  ]

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        <PageContainer maxWidth="full" className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </PageContainer>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <PageContainer maxWidth="full" className="space-y-6">
        <PageHeader
          title="Security Vulnerabilities"
          description="Comprehensive vulnerability management and analysis"
          actions={
            <div className="flex items-center space-x-3">
              <Button
                onClick={() => handleExport('json')}
                variant="outline"
                size="sm"
                className="bg-white/80 backdrop-blur-sm border-gray-200 hover:bg-white"
              >
                <Download className="h-4 w-4 mr-2" />
                Export JSON
              </Button>
              <Button
                onClick={() => handleExport('csv')}
                variant="outline"
                size="sm"
                className="bg-white/80 backdrop-blur-sm border-gray-200 hover:bg-white"
              >
                <FileText className="h-4 w-4 mr-2" />
                Export CSV
              </Button>
              <Link href="/dashboard/scan">
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Search className="h-4 w-4 mr-2" />
                  New Scan
                </Button>
              </Link>
            </div>
          }
        />

        {error && (
          <Alert variant="error" className="border-0 shadow-lg bg-red-50/80 backdrop-blur-sm">
            {error}
          </Alert>
        )}

        {/* Enhanced Filtering */}
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center text-lg font-semibold text-gray-900">
              <Filter className="h-5 w-5 mr-2 text-blue-500" />
              Filter & Search Vulnerabilities
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              {/* Search Input */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search vulnerabilities..."
                    value={filters.search}
                    onChange={(e) => handleFilterChange('search', e.target.value)}
                    className="pl-10 bg-white border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                  />
                </div>
              </div>

              {/* Severity Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Severity</label>
                <select
                  value={filters.severity}
                  onChange={(e) => handleFilterChange('severity', e.target.value)}
                  className="w-full px-3 py-2 bg-white border border-gray-200 rounded-md focus:border-blue-500 focus:ring-blue-500 text-sm"
                >
                  <option value="">All Severities</option>
                  <option value="CRITICAL">Critical</option>
                  <option value="HIGH">High</option>
                  <option value="MEDIUM">Medium</option>
                  <option value="LOW">Low</option>
                  <option value="INFO">Info</option>
                  <option value="UNKNOWN">Unknown</option>
                </select>
              </div>

              {/* Date Range Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Date Range</label>
                <select
                  value={filters.dateRange}
                  onChange={(e) => handleFilterChange('dateRange', e.target.value)}
                  className="w-full px-3 py-2 bg-white border border-gray-200 rounded-md focus:border-blue-500 focus:ring-blue-500 text-sm"
                >
                  <option value="">All Time</option>
                  <option value="today">Today</option>
                  <option value="week">This Week</option>
                  <option value="month">This Month</option>
                </select>
              </div>

              {/* Sort Options */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Sort By</label>
                <select
                  value={filters.sortBy}
                  onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                  className="w-full px-3 py-2 bg-white border border-gray-200 rounded-md focus:border-blue-500 focus:ring-blue-500 text-sm"
                >
                  <option value="timestamp">Discovery Time</option>
                  <option value="severity">Severity</option>
                  <option value="name">Name</option>
                  <option value="host">Host</option>
                </select>
              </div>

              {/* Actions */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Actions</label>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleFilterChange('sortOrder', filters.sortOrder === 'asc' ? 'desc' : 'asc')}
                    className="flex-1"
                  >
                    {filters.sortOrder === 'asc' ? <SortAsc className="h-4 w-4 mr-1" /> : <SortDesc className="h-4 w-4 mr-1" />}
                    {filters.sortOrder === 'asc' ? 'Asc' : 'Desc'}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={fetchVulnerabilities}
                    disabled={loading}
                    className="flex-1"
                  >
                    <RefreshCw className="h-4 w-4 mr-1" />
                    Refresh
                  </Button>
                </div>
              </div>
            </div>

            {/* Filter Summary */}
            <div className="flex items-center justify-between pt-4 border-t border-gray-200">
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <span>
                  Showing {displayedVulnerabilities.length} of {pagination.total} vulnerabilities
                </span>
                {filters.search && (
                  <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                    Search: "{filters.search}"
                  </Badge>
                )}
                {filters.severity && (
                  <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                    Severity: {filters.severity}
                  </Badge>
                )}
                {filters.dateRange && (
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    Date: {filters.dateRange}
                  </Badge>
                )}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setFilters({
                  search: '',
                  severity: '',
                  assetId: '',
                  scanStatus: '',
                  dateRange: '',
                  sortBy: 'timestamp',
                  sortOrder: 'desc'
                })}
                className="text-gray-600 hover:text-gray-800"
              >
                Clear Filters
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Enhanced Vulnerabilities Table */}
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center text-lg font-semibold text-gray-900">
              <Shield className="h-5 w-5 mr-2 text-red-500" />
              Vulnerabilities ({pagination.total})
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            {displayedVulnerabilities.length === 0 ? (
              <div className="text-center py-12">
                <Shield className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No vulnerabilities found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {filters.search || filters.severity || filters.dateRange
                    ? 'Try adjusting your filters to see more results.'
                    : 'Run security scans to discover vulnerabilities.'
                  }
                </p>
              </div>
            ) : (
              <>
                <div className="w-full overflow-hidden">
                  <table className="w-full table-fixed divide-y divide-gray-200">
                    <thead className="bg-gray-50/80">
                      <tr>
                        <th className="w-[30%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Vulnerability
                        </th>
                        <th className="w-[12%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Severity
                        </th>
                        <th className="w-[18%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Asset
                        </th>
                        <th className="w-[18%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Host
                        </th>
                        <th className="w-[12%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Discovered
                        </th>
                        <th className="w-[10%] px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {displayedVulnerabilities.map((vuln) => (
                        <tr key={vuln.id} className="hover:bg-gray-50/50 transition-colors">
                          <td className="px-4 py-4">
                            <div className="min-w-0">
                              <div className="text-sm font-medium text-gray-900 truncate" title={vuln.name}>
                                {vuln.name}
                              </div>
                              <div className="text-xs text-gray-500 truncate" title={vuln.templateId}>
                                Template: {vuln.templateId}
                              </div>
                              {vuln.description && (
                                <div
                                  className="text-xs text-gray-400 mt-1 overflow-hidden"
                                  style={{
                                    display: '-webkit-box',
                                    WebkitLineClamp: 2,
                                    WebkitBoxOrient: 'vertical',
                                    maxHeight: '2.5rem'
                                  }}
                                  title={vuln.description}
                                >
                                  {vuln.description}
                                </div>
                              )}
                            </div>
                          </td>
                          <td className="px-4 py-4">
                            <Badge variant="outline" className={`text-xs px-2 py-1 ${getSeverityColor(vuln.severity)}`}>
                              {vuln.severity.toLowerCase()}
                            </Badge>
                          </td>
                          <td className="px-4 py-4">
                            <div className="min-w-0">
                              <div className="text-sm font-medium text-gray-900 truncate" title={vuln.scan.asset?.title || 'Unknown'}>
                                {vuln.scan.asset?.title || 'Unknown Asset'}
                              </div>
                              <div className="text-xs text-gray-500 truncate" title={vuln.scan.asset?.domain}>
                                {vuln.scan.asset?.domain || 'No domain'}
                              </div>
                            </div>
                          </td>
                          <td className="px-4 py-4">
                            <div className="flex items-center min-w-0">
                              <span className="text-sm text-gray-900 truncate flex-1" title={vuln.host}>
                                {vuln.host}
                              </span>
                              <button
                                onClick={() => window.open(vuln.matchedAt, '_blank')}
                                className="ml-2 text-gray-400 hover:text-gray-600 flex-shrink-0"
                                title="Open URL"
                              >
                                <ExternalLink className="h-3 w-3" />
                              </button>
                            </div>
                          </td>
                          <td className="px-4 py-4 text-sm">
                            <NoSSR fallback="...">
                              <div>
                                <div className="text-gray-900">
                                  {new Date(vuln.timestamp).toLocaleDateString()}
                                </div>
                                <div className="text-xs text-gray-500">
                                  {new Date(vuln.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                                </div>
                              </div>
                            </NoSSR>
                          </td>
                          <td className="px-4 py-4">
                            <div className="flex items-center space-x-1">
                              <Link href={`/dashboard/scans/${vuln.scan.id}`}>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="text-xs px-1.5 py-1"
                                  title="View Scan"
                                >
                                  <Eye className="h-3 w-3" />
                                </Button>
                              </Link>
                              {vuln.scan.asset && (
                                <Link href={`/dashboard/assets/${vuln.scan.asset.id}`}>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    className="text-xs px-1.5 py-1"
                                    title="View Asset"
                                  >
                                    <Database className="h-3 w-3" />
                                  </Button>
                                </Link>
                              )}
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => navigator.clipboard.writeText(JSON.stringify(vuln, null, 2))}
                                className="text-xs px-1.5 py-1"
                                title="Copy vulnerability data"
                              >
                                <Copy className="h-3 w-3" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Pagination */}
                {pagination.pages > 1 && (
                  <div className="px-6 py-4 border-t border-gray-200 bg-gray-50/50">
                    <Pagination
                      currentPage={pagination.page}
                      totalPages={pagination.pages}
                      totalItems={pagination.total}
                      itemsPerPage={pagination.limit}
                      onPageChange={handlePageChange}
                      showInfo={true}
                      size="sm"
                    />
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>
      </PageContainer>
    </div>
  )
}

import { NextRequest, NextResponse } from 'next/server'
import { trackError } from './error-tracking'
import { logger } from './logger'
import { AppError, handleApiError } from './errors'

/**
 * Enhanced API error handler with comprehensive error tracking and recovery
 */
export interface ApiErrorContext {
  endpoint: string
  method: string
  userId?: string
  requestId?: string
  userAgent?: string
  ip?: string
  body?: any
  query?: any
}

/**
 * Wrapper for API route handlers with comprehensive error handling
 */
export function withErrorHandling<T extends any[]>(
  handler: (request: NextRequest, ...args: T) => Promise<NextResponse>
) {
  return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
    const startTime = Date.now()
    const requestId = generateRequestId()
    
    // Extract request context
    const context: ApiErrorContext = {
      endpoint: request.nextUrl.pathname,
      method: request.method,
      requestId,
      userAgent: request.headers.get('user-agent') || undefined,
      ip: request.headers.get('x-forwarded-for') || 
          request.headers.get('x-real-ip') || 
          'unknown'
    }

    try {
      // Add request ID to headers for tracking
      const response = await handler(request, ...args)
      
      // Log successful request
      const duration = Date.now() - startTime
      logger.info(`API request completed: ${context.method} ${context.endpoint}`, {
        component: 'API',
        requestId,
        duration,
        status: response.status
      })

      // Add request ID to response headers
      response.headers.set('x-request-id', requestId)
      
      return response

    } catch (error) {
      const duration = Date.now() - startTime
      
      // Determine error severity
      let severity: 'low' | 'medium' | 'high' | 'critical' = 'medium'
      
      if (error instanceof AppError) {
        severity = error.statusCode >= 500 ? 'high' : 'low'
      } else {
        severity = 'high' // Unknown errors are high severity
      }

      // Track the error
      const errorId = trackError(
        error instanceof Error ? error : new Error(String(error)),
        {
          component: 'API_HANDLER',
          ...context,
          duration,
          severity
        },
        severity
      )

      // Log the error
      logger.error(`API request failed: ${context.method} ${context.endpoint}`, 
        error instanceof Error ? error : new Error(String(error)), 
        {
          component: 'API',
          requestId,
          errorId,
          duration,
          ...context
        }
      )

      // Handle the error and return appropriate response
      const errorResponse = handleApiError(error)
      
      // Add request ID and error ID to response headers
      errorResponse.headers.set('x-request-id', requestId)
      errorResponse.headers.set('x-error-id', errorId)
      
      return errorResponse
    }
  }
}

/**
 * Async operation wrapper with retry logic and error handling
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  options: {
    maxRetries?: number
    delay?: number
    backoff?: boolean
    retryCondition?: (error: any) => boolean
    context?: Record<string, any>
  } = {}
): Promise<T> {
  const {
    maxRetries = 3,
    delay = 1000,
    backoff = true,
    retryCondition = () => true,
    context = {}
  } = options

  let lastError: any
  
  for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
    try {
      const result = await operation()
      
      // Log successful retry if this wasn't the first attempt
      if (attempt > 1) {
        logger.info(`Operation succeeded after ${attempt - 1} retries`, {
          component: 'RETRY_HANDLER',
          attempt,
          ...context
        })
      }
      
      return result
      
    } catch (error) {
      lastError = error
      
      // Don't retry on the last attempt
      if (attempt > maxRetries) {
        break
      }
      
      // Check if we should retry this error
      if (!retryCondition(error)) {
        logger.warn('Operation failed with non-retryable error', {
          component: 'RETRY_HANDLER',
          attempt,
          error: error instanceof Error ? error.message : String(error),
          ...context
        })
        break
      }
      
      // Calculate delay with optional exponential backoff
      const currentDelay = backoff ? delay * Math.pow(2, attempt - 1) : delay
      
      logger.warn(`Operation failed, retrying in ${currentDelay}ms (attempt ${attempt}/${maxRetries})`, {
        component: 'RETRY_HANDLER',
        attempt,
        delay: currentDelay,
        error: error instanceof Error ? error.message : String(error),
        ...context
      })
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, currentDelay))
    }
  }
  
  // Track the final failure
  trackError(
    lastError instanceof Error ? lastError : new Error(String(lastError)),
    {
      component: 'RETRY_HANDLER',
      maxRetries,
      finalFailure: true,
      ...context
    },
    'high'
  )
  
  throw lastError
}

/**
 * Database operation wrapper with connection recovery
 */
export async function withDatabaseRecovery<T>(
  operation: () => Promise<T>,
  context: Record<string, any> = {}
): Promise<T> {
  return withRetry(operation, {
    maxRetries: 3,
    delay: 2000,
    backoff: true,
    retryCondition: (error) => {
      // Retry on connection errors
      if (error && typeof error === 'object') {
        const errorCode = error.code || error.errno
        const errorMessage = error.message || ''
        
        // Common database connection error patterns
        const connectionErrors = [
          'ECONNREFUSED',
          'ENOTFOUND',
          'ETIMEDOUT',
          'ECONNRESET',
          'P1001', // Prisma connection error
          'P1008', // Prisma timeout
          'P1017'  // Prisma server closed connection
        ]
        
        return connectionErrors.some(pattern => 
          errorCode === pattern || errorMessage.includes(pattern)
        )
      }
      
      return false
    },
    context: {
      component: 'DATABASE_RECOVERY',
      ...context
    }
  })
}

/**
 * External service call wrapper with circuit breaker pattern
 */
class CircuitBreaker {
  private failures = 0
  private lastFailureTime = 0
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED'
  
  constructor(
    private threshold: number = 5,
    private timeout: number = 60000 // 1 minute
  ) {}
  
  async execute<T>(operation: () => Promise<T>, context: Record<string, any> = {}): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.timeout) {
        this.state = 'HALF_OPEN'
        logger.info('Circuit breaker transitioning to HALF_OPEN', {
          component: 'CIRCUIT_BREAKER',
          ...context
        })
      } else {
        throw new Error('Circuit breaker is OPEN - service unavailable')
      }
    }
    
    try {
      const result = await operation()
      
      // Reset on success
      if (this.state === 'HALF_OPEN') {
        this.state = 'CLOSED'
        this.failures = 0
        logger.info('Circuit breaker reset to CLOSED', {
          component: 'CIRCUIT_BREAKER',
          ...context
        })
      }
      
      return result
      
    } catch (error) {
      this.failures++
      this.lastFailureTime = Date.now()
      
      if (this.failures >= this.threshold) {
        this.state = 'OPEN'
        logger.warn(`Circuit breaker opened after ${this.failures} failures`, {
          component: 'CIRCUIT_BREAKER',
          failures: this.failures,
          threshold: this.threshold,
          ...context
        })
      }
      
      throw error
    }
  }
}

// Global circuit breakers for external services
const circuitBreakers = new Map<string, CircuitBreaker>()

export function withCircuitBreaker<T>(
  operation: () => Promise<T>,
  serviceName: string,
  context: Record<string, any> = {}
): Promise<T> {
  if (!circuitBreakers.has(serviceName)) {
    circuitBreakers.set(serviceName, new CircuitBreaker())
  }
  
  const circuitBreaker = circuitBreakers.get(serviceName)!
  return circuitBreaker.execute(operation, { serviceName, ...context })
}

/**
 * Generate unique request ID
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * Graceful shutdown handler
 */
export function setupGracefulShutdown() {
  const signals = ['SIGTERM', 'SIGINT', 'SIGUSR2']
  
  signals.forEach(signal => {
    process.on(signal, async () => {
      logger.info(`Received ${signal}, starting graceful shutdown`, {
        component: 'GRACEFUL_SHUTDOWN'
      })
      
      try {
        // Give ongoing requests time to complete
        await new Promise(resolve => setTimeout(resolve, 5000))
        
        logger.info('Graceful shutdown completed', {
          component: 'GRACEFUL_SHUTDOWN'
        })
        
        process.exit(0)
      } catch (error) {
        logger.error('Error during graceful shutdown', error as Error, {
          component: 'GRACEFUL_SHUTDOWN'
        })
        process.exit(1)
      }
    })
  })
}

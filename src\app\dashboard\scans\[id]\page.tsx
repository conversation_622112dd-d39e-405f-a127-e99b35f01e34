'use client'

import React, { useState, useEffect, useMemo } from 'react'
import { useParams } from 'next/navigation'
import {
  Shield,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  ExternalLink,
  ArrowLeft,
  Calendar,
  Globe,
  Database,
  Activity,
  Timer,
  Square,
  Loader2,
  Search,
  Filter,
  Download,
  BarChart3,
  TrendingUp,
  Target,
  Zap,
  RefreshCw,
  Copy,
  FileText,
  SortAsc,
  SortDesc,
  ChevronDown,
  Eye,
  EyeOff
} from 'lucide-react'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  TimeScale,
} from 'chart.js'
import { Line, Bar, Doughnut } from 'react-chartjs-2'
import { <PERSON>Container, PageHeader } from '@/components/layout'
import { PDFReportGenerator } from '@/components/reports/pdf-report-generator'
import { But<PERSON>, <PERSON><PERSON>, Card, CardContent, CardHeader, CardTitle, ScanStatusBadge, Input, Badge, Pagination } from '@/components/ui'
import { VulnerabilitySum<PERSON>y, ScanTerminal } from '@/components/features/scan'
import { NoSSR } from '@/components/no-ssr'
import { useScanEvents } from '@/hooks/use-scan-events'
import Link from 'next/link'

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  TimeScale
)

interface ScanDetails {
  id: string
  targetUrl: string
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED'
  startedAt?: string
  completedAt?: string
  duration?: number
  totalVulns: number
  criticalVulns: number
  highVulns: number
  mediumVulns: number
  lowVulns: number
  infoVulns: number
  unknownVulns?: number
  errorMessage?: string
  nucleiVersion?: string
  templateCount?: number
  createdAt: string
  asset?: {
    id: string
    url: string
    domain: string
    title: string
    description?: string
  }
  vulnerabilities: Array<{
    id: string
    templateId: string
    name: string
    severity: string
    description?: string
    reference?: any
    tags?: any
    matcher?: string
    extractedResults?: any
    request?: string
    response?: string
    curlCommand?: string
    host: string
    matchedAt: string
    timestamp: string
  }>
}

interface FilterState {
  search: string
  severity: string
  dateRange: string
  sortBy: string
  sortOrder: 'asc' | 'desc'
  showDetails: boolean
}

interface PaginationState {
  page: number
  limit: number
  total: number
}

export default function ScanDetailPage() {
  const params = useParams()
  const [scan, setScan] = useState<ScanDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedVuln, setSelectedVuln] = useState<string | null>(null)
  const [isCancelling, setIsCancelling] = useState(false)

  // Enhanced filtering and pagination state
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    severity: '',
    dateRange: '',
    sortBy: 'timestamp',
    sortOrder: 'desc',
    showDetails: false
  })

  const [pagination, setPagination] = useState<PaginationState>({
    page: 1,
    limit: 10,
    total: 0
  })

  // Real-time scan events
  const scanId = params.id as string
  const {
    vulnerabilities: realtimeVulns,
    totalCount: realtimeTotalCount,
    severityCount: realtimeSeverityCount,
    status: realtimeStatus,
    progress: realtimeProgress,
    error: realtimeError,
    complete: realtimeComplete,
    logs,
    isConnected,
    connectionError
  } = useScanEvents(scanId)

  useEffect(() => {
    if (params.id) {
      fetchScanDetails(params.id as string)
    }
  }, [params.id])

  // Update scan status from real-time events
  useEffect(() => {
    if (realtimeStatus) {
      setScan(prev => prev ? { ...prev, status: realtimeStatus.status } : null)
    }
  }, [realtimeStatus])

  // Update vulnerability counts from real-time events
  useEffect(() => {
    if (realtimeTotalCount > 0) {
      setScan(prev => prev ? {
        ...prev,
        totalVulns: realtimeTotalCount,
        criticalVulns: realtimeSeverityCount.CRITICAL || 0,
        highVulns: realtimeSeverityCount.HIGH || 0,
        mediumVulns: realtimeSeverityCount.MEDIUM || 0,
        lowVulns: realtimeSeverityCount.LOW || 0,
        infoVulns: realtimeSeverityCount.INFO || 0,
        vulnerabilities: realtimeVulns
      } : null)
    }
  }, [realtimeTotalCount, realtimeSeverityCount, realtimeVulns])

  // Handle real-time completion
  useEffect(() => {
    if (realtimeComplete) {
      setScan(prev => prev ? {
        ...prev,
        status: 'COMPLETED',
        duration: realtimeComplete.duration,
        totalVulns: realtimeComplete.totalVulnerabilities,
        criticalVulns: realtimeComplete.severityCount.CRITICAL || 0,
        highVulns: realtimeComplete.severityCount.HIGH || 0,
        mediumVulns: realtimeComplete.severityCount.MEDIUM || 0,
        lowVulns: realtimeComplete.severityCount.LOW || 0,
        infoVulns: realtimeComplete.severityCount.INFO || 0
      } : null)
    }
  }, [realtimeComplete])

  const fetchScanDetails = async (scanId: string) => {
    try {
      const response = await fetch(`/api/scans/${scanId}`)
      if (response.ok) {
        const data = await response.json()
        setScan(data.scan)
      } else {
        setError('Failed to fetch scan details')
      }
    } catch (error) {
      setError('Failed to fetch scan details')
      console.error('Error fetching scan details:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCancelScan = async () => {
    if (!scan || !confirm('Are you sure you want to cancel this scan? This action cannot be undone.')) {
      return
    }

    try {
      setIsCancelling(true)
      setError(null)

      const response = await fetch(`/api/scans/${scan.id}/cancel`, {
        method: 'POST',
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to cancel scan')
      }

      // Update the scan status
      setScan(prev => prev ? { ...prev, status: 'CANCELLED' } : null)

      // Refresh the data after a short delay
      setTimeout(() => {
        fetchScanDetails(scan.id)
      }, 1000)

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to cancel scan')
    } finally {
      setIsCancelling(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'RUNNING':
        return <Activity className="h-5 w-5 text-blue-500 animate-pulse" />
      case 'FAILED':
        return <XCircle className="h-5 w-5 text-red-500" />
      case 'PENDING':
        return <Clock className="h-5 w-5 text-yellow-500" />
      case 'CANCELLED':
        return <XCircle className="h-5 w-5 text-gray-500" />
      default:
        return <Clock className="h-5 w-5 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'bg-green-100 text-green-800'
      case 'RUNNING':
        return 'bg-blue-100 text-blue-800'
      case 'FAILED':
        return 'bg-red-100 text-red-800'
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800'
      case 'CANCELLED':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity.toUpperCase()) {
      case 'CRITICAL':
        return 'bg-red-100 text-red-800'
      case 'HIGH':
        return 'bg-orange-100 text-orange-800'
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800'
      case 'LOW':
        return 'bg-blue-100 text-blue-800'
      case 'INFO':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatDuration = (seconds?: number) => {
    if (!seconds) return 'N/A'
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}m ${remainingSeconds}s`
  }

  // Enhanced data processing and filtering
  const filteredVulnerabilities = useMemo(() => {
    if (!scan?.vulnerabilities) return []

    let filtered = [...scan.vulnerabilities]

    // Search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase()
      filtered = filtered.filter(vuln =>
        vuln.name.toLowerCase().includes(searchLower) ||
        vuln.templateId.toLowerCase().includes(searchLower) ||
        vuln.host.toLowerCase().includes(searchLower) ||
        (vuln.description && vuln.description.toLowerCase().includes(searchLower))
      )
    }

    // Severity filter
    if (filters.severity) {
      filtered = filtered.filter(vuln => vuln.severity.toUpperCase() === filters.severity.toUpperCase())
    }

    // Sort
    filtered.sort((a, b) => {
      let aValue: any = a[filters.sortBy as keyof typeof a]
      let bValue: any = b[filters.sortBy as keyof typeof b]

      if (filters.sortBy === 'timestamp') {
        aValue = new Date(aValue).getTime()
        bValue = new Date(bValue).getTime()
      } else if (filters.sortBy === 'severity') {
        const severityOrder = { CRITICAL: 5, HIGH: 4, MEDIUM: 3, LOW: 2, INFO: 1, UNKNOWN: 0 }
        aValue = severityOrder[aValue?.toUpperCase() as keyof typeof severityOrder] || 0
        bValue = severityOrder[bValue?.toUpperCase() as keyof typeof severityOrder] || 0
      }

      if (filters.sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

    return filtered
  }, [scan?.vulnerabilities, filters])

  // Pagination logic
  const paginatedVulnerabilities = useMemo(() => {
    const startIndex = (pagination.page - 1) * pagination.limit
    const endIndex = startIndex + pagination.limit
    return filteredVulnerabilities.slice(startIndex, endIndex)
  }, [filteredVulnerabilities, pagination.page, pagination.limit])

  // Update pagination total when filtered data changes
  useEffect(() => {
    setPagination(prev => ({
      ...prev,
      total: filteredVulnerabilities.length,
      page: 1 // Reset to first page when filters change
    }))
  }, [filteredVulnerabilities.length])

  // Chart data preparation
  const chartData = useMemo(() => {
    if (!scan) return null

    // Use real-time data if available and scan is running
    const isRunning = scan.status === 'RUNNING' || scan.status === 'PENDING'
    const useRealtimeData = isRunning && Object.keys(realtimeSeverityCount).length > 0

    const severityData = {
      labels: ['Critical', 'High', 'Medium', 'Low', 'Info', 'Unknown'],
      datasets: [{
        data: [
          useRealtimeData ? (realtimeSeverityCount.CRITICAL || 0) : (scan.criticalVulns || 0),
          useRealtimeData ? (realtimeSeverityCount.HIGH || 0) : (scan.highVulns || 0),
          useRealtimeData ? (realtimeSeverityCount.MEDIUM || 0) : (scan.mediumVulns || 0),
          useRealtimeData ? (realtimeSeverityCount.LOW || 0) : (scan.lowVulns || 0),
          useRealtimeData ? (realtimeSeverityCount.INFO || 0) : (scan.infoVulns || 0),
          useRealtimeData ? (realtimeSeverityCount.UNKNOWN || 0) : (scan.unknownVulns || 0)
        ],
        backgroundColor: [
          '#dc2626', // Critical - red
          '#ea580c', // High - orange
          '#d97706', // Medium - amber
          '#2563eb', // Low - blue
          '#059669', // Info - green
          '#6b7280'  // Unknown - gray
        ],
        borderWidth: 0,
      }]
    }

    // Timeline data (vulnerabilities discovered over time)
    const timelineData = scan.vulnerabilities.reduce((acc: any, vuln) => {
      const date = new Date(vuln.timestamp).toLocaleDateString()
      acc[date] = (acc[date] || 0) + 1
      return acc
    }, {})

    const timeline = {
      labels: Object.keys(timelineData).sort(),
      datasets: [{
        label: 'Vulnerabilities Discovered',
        data: Object.keys(timelineData).sort().map(date => timelineData[date]),
        borderColor: '#3b82f6',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
        fill: true,
      }]
    }

    // Vulnerability types (based on template IDs)
    const typeData = scan.vulnerabilities.reduce((acc: any, vuln) => {
      const type = vuln.templateId.split('-')[0] || 'other'
      acc[type] = (acc[type] || 0) + 1
      return acc
    }, {})

    const types = {
      labels: Object.keys(typeData).slice(0, 10), // Top 10 types
      datasets: [{
        label: 'Count',
        data: Object.values(typeData).slice(0, 10),
        backgroundColor: '#3b82f6',
        borderRadius: 4,
      }]
    }

    return { severity: severityData, timeline, types }
  }, [scan, realtimeSeverityCount, realtimeVulns])

  // Chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
        labels: {
          padding: 15,
          usePointStyle: true,
          font: { size: 12 }
        },
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: 'white',
        bodyColor: 'white',
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: { color: 'rgba(0, 0, 0, 0.1)' },
        ticks: { font: { size: 11 } }
      },
      x: {
        grid: { color: 'rgba(0, 0, 0, 0.1)' },
        ticks: { font: { size: 11 } }
      }
    }
  }

  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
        labels: {
          padding: 15,
          usePointStyle: true,
          font: { size: 12 }
        },
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: 'white',
        bodyColor: 'white',
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
      }
    },
    cutout: '60%',
  }

  // Helper functions
  const handleFilterChange = (key: keyof FilterState, value: string | boolean) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }))
  }

  const handleExport = async (format: 'json' | 'csv') => {
    if (!scan) return

    try {
      // Build export URL with current filters
      const params = new URLSearchParams({
        format
      })

      if (filters.severity) {
        params.append('severity', filters.severity)
      }

      if (filters.search) {
        params.append('search', filters.search)
      }

      const response = await fetch(`/api/export/scans/${scan.id}/vulnerabilities?${params}`)

      if (!response.ok) {
        throw new Error('Export failed')
      }

      if (format === 'csv') {
        // For CSV, the response is already formatted as CSV content
        const csvContent = await response.text()
        const blob = new Blob([csvContent], { type: 'text/csv' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `scan-${scan.id}-vulnerabilities-${new Date().toISOString().split('T')[0]}.csv`
        a.click()
        URL.revokeObjectURL(url)
      } else {
        // For JSON, get the data and create blob
        const data = await response.json()
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `scan-${scan.id}-results-${new Date().toISOString().split('T')[0]}.json`
        a.click()
        URL.revokeObjectURL(url)
      }
    } catch (error) {
      console.error('Export failed:', error)
      // You might want to show a toast notification here
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (error || !scan) {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="mx-auto h-12 w-12 text-red-500" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Error</h3>
        <p className="mt-1 text-sm text-gray-500">{error || 'Scan not found'}</p>
        <div className="mt-6">
          <Link href="/dashboard">
            <Button>
              <ArrowLeft className="h-4 w-4 mr-2" />
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <PageContainer maxWidth="full" className="space-y-6">
        <PageHeader
          title="Security Assessment Details"
          description={`Comprehensive vulnerability analysis for ${scan.targetUrl}`}
          backButton={{
            href: "/dashboard/scans",
            label: ""
          }}
          actions={
            <div className="flex items-center space-x-3">
              <ScanStatusBadge status={scan.status} />
              <Button
                onClick={() => handleExport('json')}
                variant="outline"
                size="sm"
                className="bg-white/80 backdrop-blur-sm border-gray-200 hover:bg-white"
              >
                <Download className="h-4 w-4 mr-2" />
                Export JSON
              </Button>
              <Button
                onClick={() => handleExport('csv')}
                variant="outline"
                size="sm"
                className="bg-white/80 backdrop-blur-sm border-gray-200 hover:bg-white"
              >
                <FileText className="h-4 w-4 mr-2" />
                Export CSV
              </Button>
              <PDFReportGenerator
                scanId={scan.id}
                reportType="scan"
                className="bg-white/80 backdrop-blur-sm"
              />
              {(scan.status === 'RUNNING' || scan.status === 'PENDING') && (
                <Button
                  onClick={handleCancelScan}
                  disabled={isCancelling}
                  variant="destructive"
                  size="sm"
                  className="bg-red-600 hover:bg-red-700"
                >
                  {isCancelling ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                      Cancelling...
                    </>
                  ) : (
                    <>
                      <Square className="h-4 w-4 mr-1" />
                      Cancel Scan
                    </>
                  )}
                </Button>
              )}
            </div>
          }
        />

        {/* Enhanced Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Total Vulnerabilities Card */}
          <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Vulnerabilities</p>
                  <p className="text-3xl font-bold text-gray-900">
                    {(scan?.status === 'RUNNING' || scan?.status === 'PENDING') && realtimeTotalCount > 0
                      ? realtimeTotalCount
                      : scan.totalVulns}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    {filteredVulnerabilities.length} filtered
                    {(scan?.status === 'RUNNING' || scan?.status === 'PENDING') && realtimeTotalCount > 0 && (
                      <span className="text-blue-600 ml-1">(live)</span>
                    )}
                  </p>
                </div>
                <div className="p-3 bg-blue-100 rounded-full">
                  <Shield className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Critical Vulnerabilities Card */}
          <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Critical Issues</p>
                  <p className="text-3xl font-bold text-red-600">
                    {(scan?.status === 'RUNNING' || scan?.status === 'PENDING') && Object.keys(realtimeSeverityCount).length > 0
                      ? (realtimeSeverityCount.CRITICAL || 0)
                      : scan.criticalVulns}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    Requires immediate attention
                    {(scan?.status === 'RUNNING' || scan?.status === 'PENDING') && Object.keys(realtimeSeverityCount).length > 0 && (
                      <span className="text-blue-600 ml-1">(live)</span>
                    )}
                  </p>
                </div>
                <div className="p-3 bg-red-100 rounded-full">
                  <AlertTriangle className="h-6 w-6 text-red-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Scan Duration Card */}
          <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Scan Duration</p>
                  <p className="text-3xl font-bold text-green-600">{formatDuration(scan.duration)}</p>
                  <p className="text-xs text-gray-500 mt-1">
                    {scan.templateCount} templates used
                  </p>
                </div>
                <div className="p-3 bg-green-100 rounded-full">
                  <Timer className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Scan Efficiency Card */}
          <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Scan Efficiency</p>
                  <p className="text-3xl font-bold text-purple-600">
                    {scan.duration ? Math.round(scan.totalVulns / (scan.duration / 60)) : 0}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    Vulnerabilities per minute
                  </p>
                </div>
                <div className="p-3 bg-purple-100 rounded-full">
                  <Zap className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Real-time connection status */}
        {connectionError && (
          <Alert variant="warning" className="border-0 shadow-lg bg-yellow-50/80 backdrop-blur-sm">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-4 w-4" />
              <span>Real-time updates: {connectionError}</span>
            </div>
          </Alert>
        )}

      {/* Real-time progress indicator */}
      {realtimeProgress && scan?.status === 'RUNNING' && (
        <div className="mb-6">
          <Alert className="border-0 shadow-lg bg-blue-50/80 backdrop-blur-sm">
            <div className="flex items-center space-x-2">
              <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
              <span className="text-blue-800">{realtimeProgress.message}</span>
            </div>
          </Alert>
        </div>
      )}

      {/* Real-time error indicator */}
      {realtimeError && (
        <div className="mb-6">
          <Alert variant="error" className="border-0 shadow-lg bg-red-50/80 backdrop-blur-sm">
            <div className="flex items-center space-x-2">
              <XCircle className="h-4 w-4" />
              <span>Scan Error: {realtimeError.message}</span>
            </div>
          </Alert>
        </div>
      )}

        {/* Real-time vulnerability counter */}
        {isConnected && scan?.status === 'RUNNING' && realtimeTotalCount > 0 && (
          <Alert className="border-0 shadow-lg bg-green-50/80 backdrop-blur-sm">
            <div className="flex items-center space-x-2">
              <Shield className="h-4 w-4 text-green-600" />
              <span className="text-green-800">
                Found {realtimeTotalCount} vulnerabilities so far...
              </span>
            </div>
          </Alert>
        )}

        {/* Enhanced Charts Section */}
        {chartData && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Vulnerability Severity Distribution */}
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center text-lg font-semibold text-gray-900">
                  <Shield className="h-5 w-5 mr-2 text-red-500" />
                  Severity Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                {((scan?.status === 'RUNNING' || scan?.status === 'PENDING') && realtimeTotalCount > 0) || scan.totalVulns > 0 ? (
                  <div className="h-64">
                    <Doughnut data={chartData.severity} options={doughnutOptions} />
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-64 text-gray-500">
                    <div className="text-center">
                      <Shield className="mx-auto h-12 w-12 text-gray-300 mb-2" />
                      <p>No vulnerabilities found</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Vulnerability Discovery Timeline */}
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center text-lg font-semibold text-gray-900">
                  <TrendingUp className="h-5 w-5 mr-2 text-blue-500" />
                  Discovery Timeline
                </CardTitle>
              </CardHeader>
              <CardContent>
                {chartData.timeline.labels.length > 0 ? (
                  <div className="h-64">
                    <Line data={chartData.timeline} options={chartOptions} />
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-64 text-gray-500">
                    <div className="text-center">
                      <Activity className="mx-auto h-12 w-12 text-gray-300 mb-2" />
                      <p>No timeline data</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Vulnerability Types */}
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center text-lg font-semibold text-gray-900">
                  <BarChart3 className="h-5 w-5 mr-2 text-green-500" />
                  Vulnerability Types
                </CardTitle>
              </CardHeader>
              <CardContent>
                {chartData.types.labels.length > 0 ? (
                  <div className="h-64">
                    <Bar data={chartData.types} options={chartOptions} />
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-64 text-gray-500">
                    <div className="text-center">
                      <Target className="mx-auto h-12 w-12 text-gray-300 mb-2" />
                      <p>No type data</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {/* Real-time Assessment Console */}
        {(scan?.status === 'RUNNING' || scan?.status === 'PENDING' || logs.length > 0) && (
          <div className="space-y-4">
            <div className="text-sm text-gray-600 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-200">
              <div className="flex items-center justify-between">
                <span className="font-semibold text-gray-800">
                  🔍 <strong>Live Security Assessment</strong>
                </span>
                <span className="text-xs bg-white px-2 py-1 rounded-full border">
                  Events: {logs.length} | {isConnected ? '🟢 Active' : '🔴 Inactive'}
                </span>
              </div>
              <p className="text-xs text-gray-600 mt-2">
                Real-time monitoring of your security assessment progress and findings
              </p>
            </div>
            <ScanTerminal
              logs={logs}
              isConnected={isConnected}
            />
          </div>
        )}

        {/* Advanced Filtering and Search */}
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center text-lg font-semibold text-gray-900">
              <Filter className="h-5 w-5 mr-2 text-blue-500" />
              Filter & Search Vulnerabilities
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Search Input */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search vulnerabilities..."
                    value={filters.search}
                    onChange={(e) => handleFilterChange('search', e.target.value)}
                    className="pl-10 bg-white border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                  />
                </div>
              </div>

              {/* Severity Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Severity</label>
                <select
                  value={filters.severity}
                  onChange={(e) => handleFilterChange('severity', e.target.value)}
                  className="w-full px-3 py-2 bg-white border border-gray-200 rounded-md focus:border-blue-500 focus:ring-blue-500 text-sm"
                >
                  <option value="">All Severities</option>
                  <option value="CRITICAL">Critical</option>
                  <option value="HIGH">High</option>
                  <option value="MEDIUM">Medium</option>
                  <option value="LOW">Low</option>
                  <option value="INFO">Info</option>
                  <option value="UNKNOWN">Unknown</option>
                </select>
              </div>

              {/* Sort By */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Sort By</label>
                <select
                  value={filters.sortBy}
                  onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                  className="w-full px-3 py-2 bg-white border border-gray-200 rounded-md focus:border-blue-500 focus:ring-blue-500 text-sm"
                >
                  <option value="timestamp">Discovery Time</option>
                  <option value="severity">Severity</option>
                  <option value="name">Name</option>
                  <option value="host">Host</option>
                  <option value="templateId">Template ID</option>
                </select>
              </div>

              {/* Sort Order */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Order</label>
                <div className="flex space-x-2">
                  <Button
                    variant={filters.sortOrder === 'desc' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handleFilterChange('sortOrder', 'desc')}
                    className="flex-1"
                  >
                    <SortDesc className="h-4 w-4 mr-1" />
                    Desc
                  </Button>
                  <Button
                    variant={filters.sortOrder === 'asc' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handleFilterChange('sortOrder', 'asc')}
                    className="flex-1"
                  >
                    <SortAsc className="h-4 w-4 mr-1" />
                    Asc
                  </Button>
                </div>
              </div>
            </div>

            {/* Filter Summary */}
            <div className="flex items-center justify-between pt-4 border-t border-gray-200">
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <span>
                  Showing {paginatedVulnerabilities.length} of {filteredVulnerabilities.length} vulnerabilities
                </span>
                {filters.search && (
                  <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                    Search: "{filters.search}"
                  </Badge>
                )}
                {filters.severity && (
                  <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                    Severity: {filters.severity}
                  </Badge>
                )}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setFilters({
                  search: '',
                  severity: '',
                  dateRange: '',
                  sortBy: 'timestamp',
                  sortOrder: 'desc',
                  showDetails: false
                })}
                className="text-gray-600 hover:text-gray-800"
              >
                Clear Filters
              </Button>
            </div>
          </CardContent>
        </Card>

      {/* Scan Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Basic Information */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Scan Information</h3>
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <Globe className="h-5 w-5 text-gray-400" />
              <div>
                <div className="text-sm font-medium text-gray-900">Target URL</div>
                <div className="text-sm text-gray-500">{scan.targetUrl}</div>
              </div>
            </div>
            
            {scan.asset && (
              <div className="flex items-center space-x-3">
                <Database className="h-5 w-5 text-gray-400" />
                <div>
                  <div className="text-sm font-medium text-gray-900">Asset</div>
                  <div className="text-sm text-gray-500">
                    <Link href={`/dashboard/assets/${scan.asset.id}`} className="text-blue-600 hover:text-blue-800">
                      {scan.asset.title || scan.asset.domain}
                    </Link>
                  </div>
                </div>
              </div>
            )}

            <div className="flex items-center space-x-3">
              <Calendar className="h-5 w-5 text-gray-400" />
              <div>
                <div className="text-sm font-medium text-gray-900">Created</div>
                <div className="text-sm text-gray-500">
                  <NoSSR fallback="Loading...">
                    {new Date(scan.createdAt).toLocaleString()}
                  </NoSSR>
                </div>
              </div>
            </div>

            {scan.startedAt && (
              <div className="flex items-center space-x-3">
                <Activity className="h-5 w-5 text-gray-400" />
                <div>
                  <div className="text-sm font-medium text-gray-900">Started</div>
                  <div className="text-sm text-gray-500">
                    <NoSSR fallback="Loading...">
                      {new Date(scan.startedAt).toLocaleString()}
                    </NoSSR>
                  </div>
                </div>
              </div>
            )}

            {scan.completedAt && (
              <div className="flex items-center space-x-3">
                <CheckCircle className="h-5 w-5 text-gray-400" />
                <div>
                  <div className="text-sm font-medium text-gray-900">Completed</div>
                  <div className="text-sm text-gray-500">
                    <NoSSR fallback="Loading...">
                      {new Date(scan.completedAt).toLocaleString()}
                    </NoSSR>
                  </div>
                </div>
              </div>
            )}

            {scan.duration && (
              <div className="flex items-center space-x-3">
                <Timer className="h-5 w-5 text-gray-400" />
                <div>
                  <div className="text-sm font-medium text-gray-900">Duration</div>
                  <div className="text-sm text-gray-500">{formatDuration(scan.duration)}</div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Technical Details */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Technical Details</h3>
          <div className="space-y-4">
            {scan.nucleiVersion && (
              <div>
                <div className="text-sm font-medium text-gray-900">Nuclei Version</div>
                <div className="text-sm text-gray-500">{scan.nucleiVersion}</div>
              </div>
            )}
            
            {scan.templateCount && (
              <div>
                <div className="text-sm font-medium text-gray-900">Templates Used</div>
                <div className="text-sm text-gray-500">{scan.templateCount} templates</div>
              </div>
            )}

            <div>
              <div className="text-sm font-medium text-gray-900">Total Vulnerabilities</div>
              <div className="text-sm text-gray-500">{scan.totalVulns} found</div>
            </div>

            {scan.errorMessage && (
              <div>
                <div className="text-sm font-medium text-red-900">Error Message</div>
                <div className="text-sm text-red-600 bg-red-50 p-2 rounded">{scan.errorMessage}</div>
              </div>
            )}
          </div>
        </div>
      </div>

      <VulnerabilitySummary
        critical={scan.criticalVulns}
        high={scan.highVulns}
        medium={scan.mediumVulns}
        low={scan.lowVulns}
        info={scan.infoVulns}
      />

        {/* Enhanced Vulnerabilities Table */}
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center text-lg font-semibold text-gray-900">
                <Shield className="h-5 w-5 mr-2 text-red-500" />
                Vulnerabilities ({filteredVulnerabilities.length})
              </CardTitle>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleFilterChange('showDetails', !filters.showDetails)}
                  className="bg-white/80 backdrop-blur-sm border-gray-200 hover:bg-white"
                >
                  {filters.showDetails ? (
                    <>
                      <EyeOff className="h-4 w-4 mr-2" />
                      Hide Details
                    </>
                  ) : (
                    <>
                      <Eye className="h-4 w-4 mr-2" />
                      Show Details
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            {filteredVulnerabilities.length > 0 ? (
              <>
                {/* Table with fixed column widths */}
                <div className="w-full">
                  <table className="w-full table-fixed divide-y divide-gray-200">
                    <thead className="bg-gray-50/80">
                      <tr>
                        <th className="w-1/3 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Vulnerability
                        </th>
                        <th className="w-20 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Severity
                        </th>
                        <th className="w-1/4 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Host
                        </th>
                        <th className="w-32 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Template ID
                        </th>
                        <th className="w-24 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Detected
                        </th>
                        <th className="w-24 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                        {paginatedVulnerabilities.map((vuln) => (
                          <React.Fragment key={vuln.id}>
                            <tr className="hover:bg-gray-50/50 transition-colors">
                              <td className="px-4 py-4">
                                <div>
                                  <div className="text-sm font-medium text-gray-900 truncate">
                                    {vuln.name}
                                  </div>
                                  {vuln.description && (
                                    <div className="text-xs text-gray-500 truncate mt-1">
                                      {vuln.description}
                                    </div>
                                  )}
                                </div>
                              </td>
                              <td className="px-4 py-4">
                                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(vuln.severity)}`}>
                                  {vuln.severity.toLowerCase()}
                                </span>
                              </td>
                              <td className="px-4 py-4 text-sm text-gray-900">
                                <div className="flex items-center">
                                  <span className="truncate">{vuln.host}</span>
                                  <button
                                    onClick={() => window.open(vuln.matchedAt, '_blank')}
                                    className="ml-2 text-gray-400 hover:text-gray-600 flex-shrink-0"
                                  >
                                    <ExternalLink className="h-3 w-3" />
                                  </button>
                                </div>
                              </td>
                              <td className="px-4 py-4 text-xs text-gray-500 truncate">
                                {vuln.templateId}
                              </td>
                              <td className="px-4 py-4 text-xs text-gray-500">
                                <NoSSR fallback="...">
                                  {new Date(vuln.timestamp).toLocaleDateString()}
                                </NoSSR>
                              </td>
                              <td className="px-4 py-4">
                                <div className="flex space-x-1">
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => setSelectedVuln(selectedVuln === vuln.id ? null : vuln.id)}
                                    className="text-xs px-2 py-1"
                                  >
                                    {selectedVuln === vuln.id ? (
                                      <ChevronDown className="h-3 w-3" />
                                    ) : (
                                      <Eye className="h-3 w-3" />
                                    )}
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => navigator.clipboard.writeText(JSON.stringify(vuln, null, 2))}
                                    className="text-xs px-2 py-1"
                                  >
                                    <Copy className="h-3 w-3" />
                                  </Button>
                                </div>
                              </td>
                            </tr>
                            {(selectedVuln === vuln.id || filters.showDetails) && (
                              <tr>
                                <td colSpan={6} className="px-4 py-4 bg-gradient-to-r from-gray-50 to-blue-50 border-l-4 border-blue-400">
                                  <div className="space-y-4">
                                    {vuln.description && (
                                      <div>
                                        <h4 className="text-sm font-semibold text-gray-900 mb-2">Description</h4>
                                        <p className="text-sm text-gray-700 bg-white p-3 rounded-lg border">{vuln.description}</p>
                                      </div>
                                    )}

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                      {vuln.reference && (
                                        <div>
                                          <h4 className="text-sm font-semibold text-gray-900 mb-2">References</h4>
                                          <div className="text-sm text-gray-700 bg-white p-3 rounded-lg border max-h-32 overflow-y-auto">
                                            {typeof vuln.reference === 'string' ? (
                                              <pre className="whitespace-pre-wrap text-xs">{vuln.reference}</pre>
                                            ) : (
                                              <pre className="whitespace-pre-wrap text-xs">{JSON.stringify(vuln.reference, null, 2)}</pre>
                                            )}
                                          </div>
                                        </div>
                                      )}

                                      {vuln.tags && (
                                        <div>
                                          <h4 className="text-sm font-semibold text-gray-900 mb-2">Tags</h4>
                                          <div className="flex flex-wrap gap-1">
                                            {(typeof vuln.tags === 'string' ? JSON.parse(vuln.tags) : vuln.tags).map((tag: string, index: number) => (
                                              <Badge key={index} variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 text-xs">
                                                {tag}
                                              </Badge>
                                            ))}
                                          </div>
                                        </div>
                                      )}
                                    </div>

                                    {vuln.matcher && (
                                      <div>
                                        <h4 className="text-sm font-semibold text-gray-900 mb-2">Matcher</h4>
                                        <pre className="text-xs text-gray-700 bg-white p-3 rounded-lg border overflow-x-auto">{vuln.matcher}</pre>
                                      </div>
                                    )}

                                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                                      {vuln.request && (
                                        <div>
                                          <h4 className="text-sm font-semibold text-gray-900 mb-2">Request</h4>
                                          <pre className="text-xs text-gray-700 bg-white p-3 rounded-lg border overflow-x-auto max-h-40">{vuln.request}</pre>
                                        </div>
                                      )}

                                      {vuln.response && (
                                        <div>
                                          <h4 className="text-sm font-semibold text-gray-900 mb-2">Response</h4>
                                          <pre className="text-xs text-gray-700 bg-white p-3 rounded-lg border overflow-x-auto max-h-40">{vuln.response}</pre>
                                        </div>
                                      )}
                                    </div>

                                    {vuln.curlCommand && (
                                      <div>
                                        <h4 className="text-sm font-semibold text-gray-900 mb-2">cURL Command</h4>
                                        <div className="relative">
                                          <pre className="text-xs text-gray-700 bg-white p-3 rounded-lg border overflow-x-auto">{vuln.curlCommand}</pre>
                                          <Button
                                            size="sm"
                                            variant="outline"
                                            onClick={() => navigator.clipboard.writeText(vuln.curlCommand || '')}
                                            className="absolute top-2 right-2 text-xs px-2 py-1"
                                          >
                                            <Copy className="h-3 w-3" />
                                          </Button>
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                </td>
                              </tr>
                            )}
                          </React.Fragment>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  {/* Pagination */}
                  {Math.ceil(filteredVulnerabilities.length / pagination.limit) > 1 && (
                    <div className="px-6 py-4 border-t border-gray-200 bg-gray-50/50">
                      <Pagination
                        currentPage={pagination.page}
                        totalPages={Math.ceil(filteredVulnerabilities.length / pagination.limit)}
                        totalItems={filteredVulnerabilities.length}
                        itemsPerPage={pagination.limit}
                        onPageChange={handlePageChange}
                        showInfo={true}
                        size="sm"
                      />
                    </div>
                  )}
                </>
              ) : (
                <div className="text-center py-12">
                  <Shield className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No vulnerabilities found</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    {filters.search || filters.severity
                      ? 'Try adjusting your filters to see more results.'
                      : 'This scan completed successfully with no security issues detected.'
                    }
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
      </PageContainer>
    </div>
  )
}

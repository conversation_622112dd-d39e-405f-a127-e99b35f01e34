# CTB Scanner Deployment Script for Windows
# Production deployment automation

param(
    [string]$Environment = "production",
    [switch]$SkipBackup = $false,
    [switch]$SkipBuild = $false,
    [switch]$Monitoring = $false
)

# =============================================================================
# Configuration
# =============================================================================
$ErrorActionPreference = "Stop"
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectDir = Split-Path -Parent $ScriptDir
$BackupDir = "$ProjectDir\backups"
$LogFile = "$ProjectDir\logs\deploy.log"

# =============================================================================
# Logging Functions
# =============================================================================
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    switch ($Level) {
        "ERROR" { Write-Host $logMessage -ForegroundColor Red }
        "WARN"  { Write-Host $logMessage -ForegroundColor Yellow }
        "INFO"  { Write-Host $logMessage -ForegroundColor Green }
        default { Write-Host $logMessage }
    }
    
    # Ensure log directory exists
    $logDir = Split-Path -Parent $LogFile
    if (!(Test-Path $logDir)) {
        New-Item -ItemType Directory -Path $logDir -Force | Out-Null
    }
    
    Add-Content -Path $LogFile -Value $logMessage
}

function Write-Error-Log {
    param([string]$Message)
    Write-Log $Message "ERROR"
    throw $Message
}

function Write-Warning-Log {
    param([string]$Message)
    Write-Log $Message "WARN"
}

# =============================================================================
# Utility Functions
# =============================================================================
function Test-Requirements {
    Write-Log "Checking deployment requirements..."
    
    # Check required commands
    $requiredCommands = @("docker", "docker-compose", "git")
    foreach ($cmd in $requiredCommands) {
        if (!(Get-Command $cmd -ErrorAction SilentlyContinue)) {
            Write-Error-Log "Required command '$cmd' not found. Please install it first."
        }
    }
    
    # Check Docker daemon
    try {
        docker info | Out-Null
    }
    catch {
        Write-Error-Log "Docker daemon is not running. Please start Docker Desktop first."
    }
    
    Write-Log "All requirements satisfied."
}

function New-RequiredDirectories {
    Write-Log "Creating required directories..."
    
    $directories = @(
        "$ProjectDir\backups",
        "$ProjectDir\logs",
        "$ProjectDir\ssl",
        "$ProjectDir\config"
    )
    
    foreach ($dir in $directories) {
        if (!(Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
        }
    }
    
    Write-Log "Directories created successfully."
}

function Backup-Database {
    if ($SkipBackup) {
        Write-Warning-Log "Skipping database backup as requested."
        return
    }
    
    Write-Log "Creating database backup..."
    
    $backupFile = "$BackupDir\ctb_scanner_$(Get-Date -Format 'yyyyMMdd_HHmmss').sql"
    
    try {
        # Check if MySQL container is running
        $mysqlStatus = docker-compose ps mysql 2>$null
        if ($mysqlStatus -match "Up") {
            # Load environment variables
            if (Test-Path "$ProjectDir\.env") {
                Get-Content "$ProjectDir\.env" | ForEach-Object {
                    if ($_ -match "^([^#][^=]+)=(.*)$") {
                        [Environment]::SetEnvironmentVariable($matches[1], $matches[2], "Process")
                    }
                }
            }
            
            $mysqlPassword = $env:MYSQL_ROOT_PASSWORD
            if (!$mysqlPassword) {
                Write-Warning-Log "MYSQL_ROOT_PASSWORD not found. Skipping database backup."
                return
            }
            
            docker-compose exec -T mysql mysqladump -u root -p"$mysqlPassword" --single-transaction --routines --triggers ctb_scanner > $backupFile
            
            # Compress backup
            Compress-Archive -Path $backupFile -DestinationPath "$backupFile.zip" -Force
            Remove-Item $backupFile
            
            Write-Log "Database backup created: $backupFile.zip"
        }
        else {
            Write-Warning-Log "MySQL container not running. Skipping database backup."
        }
    }
    catch {
        Write-Warning-Log "Failed to create database backup: $($_.Exception.Message)"
    }
}

function Import-Environment {
    Write-Log "Loading environment configuration..."
    
    # Check if .env file exists
    if (!(Test-Path "$ProjectDir\.env")) {
        if (Test-Path "$ProjectDir\.env.example") {
            Write-Warning-Log ".env file not found. Copying from .env.example"
            Copy-Item "$ProjectDir\.env.example" "$ProjectDir\.env"
            Write-Error-Log "Please configure .env file with your production settings before deploying."
        }
        else {
            Write-Error-Log ".env file not found and no .env.example available."
        }
    }
    
    # Load environment variables
    Get-Content "$ProjectDir\.env" | ForEach-Object {
        if ($_ -match "^([^#][^=]+)=(.*)$") {
            [Environment]::SetEnvironmentVariable($matches[1], $matches[2], "Process")
        }
    }
    
    # Validate required environment variables
    $requiredVars = @("MYSQL_ROOT_PASSWORD", "MYSQL_PASSWORD", "JWT_SECRET", "NEXTAUTH_SECRET")
    foreach ($var in $requiredVars) {
        if (!(Get-Item "env:$var" -ErrorAction SilentlyContinue)) {
            Write-Error-Log "Required environment variable '$var' is not set."
        }
    }
    
    Write-Log "Environment configuration loaded."
}

function Build-Application {
    if ($SkipBuild) {
        Write-Warning-Log "Skipping application build as requested."
        return
    }
    
    Write-Log "Building CTB Scanner application..."
    
    Set-Location $ProjectDir
    
    # Pull latest changes (if this is a git deployment)
    if (Test-Path ".git") {
        Write-Log "Pulling latest changes from git..."
        try {
            git pull origin main
        }
        catch {
            Write-Warning-Log "Failed to pull latest changes. Continuing with current code."
        }
    }
    
    # Build Docker images
    docker-compose build --no-cache app
    
    Write-Log "Application built successfully."
}

function Deploy-Application {
    Write-Log "Deploying CTB Scanner application..."
    
    Set-Location $ProjectDir
    
    # Stop existing containers
    Write-Log "Stopping existing containers..."
    docker-compose down --remove-orphans
    
    # Start database first
    Write-Log "Starting database..."
    docker-compose up -d mysql redis
    
    # Wait for database to be ready
    Write-Log "Waiting for database to be ready..."
    $maxAttempts = 30
    $attempt = 1
    
    while ($attempt -le $maxAttempts) {
        try {
            $result = docker-compose exec -T mysql mysqladmin ping -h localhost -u root -p"$env:MYSQL_ROOT_PASSWORD" 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Log "Database is ready."
                break
            }
        }
        catch {
            # Continue waiting
        }
        
        if ($attempt -eq $maxAttempts) {
            Write-Error-Log "Database failed to start after $maxAttempts attempts."
        }
        
        Write-Log "Waiting for database... (attempt $attempt/$maxAttempts)"
        Start-Sleep 10
        $attempt++
    }
    
    # Run database migrations
    Write-Log "Running database migrations..."
    docker-compose run --rm app npx prisma migrate deploy
    
    # Start application
    Write-Log "Starting application..."
    docker-compose up -d app
    
    # Start monitoring if requested
    if ($Monitoring) {
        Write-Log "Starting monitoring stack..."
        docker-compose --profile monitoring up -d prometheus grafana
    }
    
    Write-Log "Application deployed successfully."
}

function Test-Health {
    Write-Log "Performing health check..."
    
    $maxAttempts = 20
    $attempt = 1
    $healthUrl = "http://localhost:3000/api/health"
    
    while ($attempt -le $maxAttempts) {
        try {
            $response = Invoke-WebRequest -Uri $healthUrl -UseBasicParsing -TimeoutSec 5
            if ($response.StatusCode -eq 200) {
                Write-Log "Health check passed. Application is running."
                return
            }
        }
        catch {
            # Continue waiting
        }
        
        if ($attempt -eq $maxAttempts) {
            Write-Error-Log "Health check failed after $maxAttempts attempts."
        }
        
        Write-Log "Health check attempt $attempt/$maxAttempts..."
        Start-Sleep 15
        $attempt++
    }
}

function Remove-OldImages {
    Write-Log "Cleaning up old Docker images..."
    
    try {
        # Remove dangling images
        docker image prune -f
        
        Write-Log "Old images cleaned up."
    }
    catch {
        Write-Warning-Log "Failed to clean up old images: $($_.Exception.Message)"
    }
}

function Show-Status {
    Write-Log "Deployment Status:"
    Write-Host "===================="
    docker-compose ps
    Write-Host "===================="
    
    Write-Log "Application URL: http://localhost:3000"
    Write-Log "Health Check: http://localhost:3000/api/health"
    
    if ($Monitoring) {
        Write-Log "Prometheus: http://localhost:9090"
        Write-Log "Grafana: http://localhost:3001"
    }
}

# =============================================================================
# Main Deployment Function
# =============================================================================
function Main {
    Write-Log "Starting CTB Scanner deployment..."
    Write-Log "Environment: $Environment"
    
    try {
        Test-Requirements
        New-RequiredDirectories
        Import-Environment
        Backup-Database
        Build-Application
        Deploy-Application
        Test-Health
        Remove-OldImages
        Show-Status
        
        Write-Log "CTB Scanner deployment completed successfully!"
    }
    catch {
        Write-Error-Log "Deployment failed: $($_.Exception.Message)"
    }
}

# =============================================================================
# Script Execution
# =============================================================================
if ($MyInvocation.InvocationName -ne '.') {
    Main
}

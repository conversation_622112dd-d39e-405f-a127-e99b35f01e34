# 🗄️ Database Architecture

<div align="center">

[![MySQL](https://img.shields.io/badge/MySQL-8.0-4479A1?style=for-the-badge&logo=mysql&logoColor=white)](https://www.mysql.com/)
[![Prisma](https://img.shields.io/badge/Prisma-5.0-2D3748?style=for-the-badge&logo=prisma)](https://www.prisma.io/)

*Comprehensive database schema and architecture documentation for CTB Scanner*

</div>

---

## 📊 Schema Overview

CTB Scanner uses a **MySQL 8.0** database with **Prisma ORM** for type-safe database operations. The schema is designed for scalability, performance, and data integrity.

<div align="center">

```mermaid
erDiagram
    USERS ||--o{ ASSETS : owns
    USERS ||--o{ SCANS : creates
    ASSETS ||--o{ SCANS : scanned_by
    SCANS ||--o{ VULNERABILITIES : contains
    ASSETS ||--o{ VULNERABILITIES : found_in

    USERS {
        string id PK
        string first_name
        string last_name
        string company_name
        string country
        string email UK
        string password
        datetime created_at
        datetime updated_at
    }

    ASSETS {
        string id PK
        string url UK
        string domain
        string title
        text description
        json technology
        enum status
        datetime last_scanned
        string user_id FK
        datetime created_at
        datetime updated_at
    }

    SCANS {
        string id PK
        string target_url
        enum status
        datetime started_at
        datetime completed_at
        int duration
        int total_vulns
        int critical_vulns
        int high_vulns
        int medium_vulns
        int low_vulns
        int info_vulns
        int unknown_vulns
        text error_message
        string nuclei_version
        int template_count
        string user_id FK
        string asset_id FK
        datetime created_at
        datetime updated_at
    }

    VULNERABILITIES {
        string id PK
        string template_id
        string name
        enum severity
        longtext description
        json reference
        json tags
        longtext matcher
        json extracted_results
        longtext request
        longtext response
        longtext curl_command
        string host
        string matched_at
        datetime timestamp
        string scan_id FK
        string asset_id FK
        datetime created_at
        datetime updated_at
    }
```

</div>

## 🏗️ Table Structures

### 👤 Users Table

**Purpose**: Store user account information and authentication data.

```sql
CREATE TABLE users (
  id VARCHAR(191) PRIMARY KEY,
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  company_name VARCHAR(200) NOT NULL,
  country VARCHAR(100) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3),
  updated_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  
  INDEX idx_email (email)
);
```

**Key Features**:
- ✅ **Unique Email**: Prevents duplicate accounts
- 🔒 **Password Hashing**: Bcrypt with 12 salt rounds
- 📊 **Audit Trail**: Created/updated timestamps
- 🚀 **Performance**: Indexed email for fast lookups

### 🌐 Assets Table

**Purpose**: Manage target assets and their metadata.

```sql
CREATE TABLE assets (
  id VARCHAR(191) PRIMARY KEY,
  url VARCHAR(500) NOT NULL,
  domain VARCHAR(255) NOT NULL,
  title VARCHAR(500),
  description TEXT,
  technology JSON,
  status ENUM('ACTIVE', 'INACTIVE', 'ARCHIVED') DEFAULT 'ACTIVE',
  last_scanned DATETIME(3),
  created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3),
  updated_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  user_id VARCHAR(191) NOT NULL,
  
  INDEX idx_user_id (user_id),
  INDEX idx_domain (domain),
  INDEX idx_status (status),
  UNIQUE KEY unique_user_url (user_id, url),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

**Key Features**:
- 🔗 **User Isolation**: Each user has their own assets
- 🚫 **Duplicate Prevention**: Unique constraint on user+URL
- 📱 **Technology Stack**: JSON field for flexible metadata
- 🎯 **Status Management**: Active/Inactive/Archived states
- 🔍 **Optimized Queries**: Strategic indexing for performance

### 🔍 Scans Table

**Purpose**: Track vulnerability scans and their execution details.

```sql
CREATE TABLE scans (
  id VARCHAR(191) PRIMARY KEY,
  target_url VARCHAR(500) NOT NULL,
  status ENUM('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED') DEFAULT 'PENDING',
  started_at DATETIME(3),
  completed_at DATETIME(3),
  duration INT,
  total_vulns INT DEFAULT 0,
  critical_vulns INT DEFAULT 0,
  high_vulns INT DEFAULT 0,
  medium_vulns INT DEFAULT 0,
  low_vulns INT DEFAULT 0,
  info_vulns INT DEFAULT 0,
  unknown_vulns INT DEFAULT 0,
  error_message TEXT,
  nuclei_version VARCHAR(50),
  template_count INT,
  created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3),
  updated_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  user_id VARCHAR(191) NOT NULL,
  asset_id VARCHAR(191),
  
  INDEX idx_user_id (user_id),
  INDEX idx_status (status),
  INDEX idx_started_at (started_at),
  INDEX idx_asset_id (asset_id),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (asset_id) REFERENCES assets(id) ON DELETE SET NULL
);
```

**Key Features**:
- 📊 **Vulnerability Counters**: Pre-calculated counts by severity
- ⏱️ **Execution Tracking**: Start/end times and duration
- 🔄 **Status Management**: Complete scan lifecycle tracking
- 🛠️ **Tool Metadata**: Nuclei version and template count
- 🎯 **Asset Linking**: Optional asset association

### 🛡️ Vulnerabilities Table

**Purpose**: Store detailed vulnerability findings from scans.

```sql
CREATE TABLE vulnerabilities (
  id VARCHAR(191) PRIMARY KEY,
  template_id VARCHAR(255) NOT NULL,
  name VARCHAR(500) NOT NULL,
  severity ENUM('CRITICAL', 'HIGH', 'MEDIUM', 'LOW', 'INFO', 'UNKNOWN') NOT NULL,
  description LONGTEXT,
  reference JSON,
  tags JSON,
  matcher LONGTEXT,
  extracted_results JSON,
  request LONGTEXT,
  response LONGTEXT,
  curl_command LONGTEXT,
  host VARCHAR(500) NOT NULL,
  matched_at VARCHAR(500) NOT NULL,
  timestamp DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3),
  created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3),
  updated_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  scan_id VARCHAR(191) NOT NULL,
  asset_id VARCHAR(191),
  
  INDEX idx_scan_id (scan_id),
  INDEX idx_asset_id (asset_id),
  INDEX idx_severity (severity),
  INDEX idx_template_id (template_id),
  INDEX idx_host (host),
  FOREIGN KEY (scan_id) REFERENCES scans(id) ON DELETE CASCADE,
  FOREIGN KEY (asset_id) REFERENCES assets(id) ON DELETE SET NULL
);
```

**Key Features**:
- 🔍 **Rich Metadata**: Complete vulnerability details
- 📋 **Template Tracking**: Nuclei template identification
- 🌐 **HTTP Details**: Full request/response capture
- 🏷️ **Flexible Tagging**: JSON-based tag system
- 📊 **Severity Classification**: Six-level severity system

## 🚀 Performance Optimizations

### 📈 Indexing Strategy

| Table | Index | Purpose |
|-------|-------|---------|
| **users** | `idx_email` | Fast authentication lookups |
| **assets** | `idx_user_id` | User asset filtering |
| **assets** | `idx_domain` | Domain-based searches |
| **assets** | `idx_status` | Status filtering |
| **scans** | `idx_user_id` | User scan filtering |
| **scans** | `idx_status` | Status-based queries |
| **scans** | `idx_started_at` | Chronological sorting |
| **vulnerabilities** | `idx_severity` | Severity filtering |
| **vulnerabilities** | `idx_template_id` | Template-based analysis |
| **vulnerabilities** | `idx_host` | Host-based grouping |

### 🔗 Relationship Management

- **Cascade Deletes**: User deletion removes all associated data
- **Soft References**: Asset deletion preserves historical scan data
- **Referential Integrity**: Foreign key constraints ensure data consistency

## 🛠️ Database Operations

### 📝 Common Queries

#### Get User Dashboard Stats
```sql
SELECT 
  COUNT(DISTINCT a.id) as total_assets,
  COUNT(DISTINCT s.id) as total_scans,
  COUNT(DISTINCT v.id) as total_vulnerabilities,
  COUNT(DISTINCT CASE WHEN s.status = 'RUNNING' THEN s.id END) as active_scans
FROM users u
LEFT JOIN assets a ON u.id = a.user_id
LEFT JOIN scans s ON u.id = s.user_id
LEFT JOIN vulnerabilities v ON s.id = v.scan_id
WHERE u.id = ?;
```

#### Get Vulnerability Distribution
```sql
SELECT 
  severity,
  COUNT(*) as count
FROM vulnerabilities v
JOIN scans s ON v.scan_id = s.id
WHERE s.user_id = ?
GROUP BY severity
ORDER BY FIELD(severity, 'CRITICAL', 'HIGH', 'MEDIUM', 'LOW', 'INFO', 'UNKNOWN');
```

#### Get Recent Scan Activity
```sql
SELECT 
  s.*,
  a.domain,
  a.title
FROM scans s
LEFT JOIN assets a ON s.asset_id = a.id
WHERE s.user_id = ?
ORDER BY s.created_at DESC
LIMIT 10;
```

### 🔄 Migration Management

```bash
# Create new migration
npx prisma migrate dev --name migration_name

# Deploy to production
npx prisma migrate deploy

# Reset database (development only)
npx prisma migrate reset

# Generate Prisma client
npx prisma generate
```

## 🔒 Security Considerations

### 🛡️ Data Protection

- **Password Hashing**: Bcrypt with 12 salt rounds
- **SQL Injection Prevention**: Prisma ORM parameterized queries
- **User Data Isolation**: Row-level security through user_id filtering
- **Sensitive Data**: No plaintext passwords or tokens stored

### 🔐 Access Control

- **Authentication Required**: All operations require valid JWT
- **User Isolation**: Users can only access their own data
- **Cascade Deletes**: Proper cleanup on user deletion
- **Audit Trail**: Complete timestamp tracking

## 📊 Monitoring & Maintenance

### 🔍 Health Checks

```sql
-- Check database connectivity
SELECT 1;

-- Monitor table sizes
SELECT 
  table_name,
  table_rows,
  ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables
WHERE table_schema = 'ctb_scanner'
ORDER BY (data_length + index_length) DESC;

-- Check for long-running scans
SELECT 
  id,
  target_url,
  status,
  started_at,
  TIMESTAMPDIFF(MINUTE, started_at, NOW()) as duration_minutes
FROM scans
WHERE status = 'RUNNING' 
  AND started_at < DATE_SUB(NOW(), INTERVAL 30 MINUTE);
```

### 🧹 Maintenance Tasks

```sql
-- Clean up old completed scans (older than 90 days)
DELETE FROM scans 
WHERE status = 'COMPLETED' 
  AND completed_at < DATE_SUB(NOW(), INTERVAL 90 DAY);

-- Archive old assets
UPDATE assets 
SET status = 'ARCHIVED' 
WHERE last_scanned < DATE_SUB(NOW(), INTERVAL 180 DAY)
  AND status = 'ACTIVE';

-- Update asset scan statistics
UPDATE assets a
SET last_scanned = (
  SELECT MAX(completed_at)
  FROM scans s
  WHERE s.asset_id = a.id AND s.status = 'COMPLETED'
);
```

---

<div align="center">

**[⬅️ Back to Main Documentation](../README.md)**

</div>

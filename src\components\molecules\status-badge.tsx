import * as React from 'react'
import { cn } from '@/lib/utils'
import { Badge } from '../atoms/badge'
import { Icon } from '../atoms/icon'
import { CheckCircle, Clock, XCircle, AlertTriangle, Activity, Info } from 'lucide-react'

export type StatusType = 'success' | 'pending' | 'error' | 'warning' | 'running' | 'info'
export type SeverityType = 'critical' | 'high' | 'medium' | 'low' | 'info' | 'unknown'
export type ScanStatusType = 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED'

export interface StatusBadgeProps {
  status: StatusType
  children?: React.ReactNode
  showIcon?: boolean
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export interface SeverityBadgeProps {
  severity: SeverityType
  children?: React.ReactNode
  showIcon?: boolean
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export interface ScanStatusBadgeProps {
  status: ScanStatusType
  children?: React.ReactNode
  showIcon?: boolean
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

const statusConfig = {
  success: {
    variant: 'success' as const,
    icon: CheckCircle,
    label: 'Success'
  },
  pending: {
    variant: 'warning' as const,
    icon: Clock,
    label: 'Pending'
  },
  error: {
    variant: 'destructive' as const,
    icon: XCircle,
    label: 'Error'
  },
  warning: {
    variant: 'warning' as const,
    icon: AlertTriangle,
    label: 'Warning'
  },
  running: {
    variant: 'default' as const,
    icon: Activity,
    label: 'Running'
  },
  info: {
    variant: 'secondary' as const,
    icon: Info,
    label: 'Info'
  }
}

const severityConfig = {
  critical: {
    variant: 'destructive' as const,
    label: 'Critical',
    className: 'bg-red-100 text-red-800 border-red-200'
  },
  high: {
    variant: 'destructive' as const,
    label: 'High',
    className: 'bg-orange-100 text-orange-800 border-orange-200'
  },
  medium: {
    variant: 'warning' as const,
    label: 'Medium',
    className: 'bg-yellow-100 text-yellow-800 border-yellow-200'
  },
  low: {
    variant: 'success' as const,
    label: 'Low',
    className: 'bg-green-100 text-green-800 border-green-200'
  },
  info: {
    variant: 'secondary' as const,
    label: 'Info',
    className: 'bg-blue-100 text-blue-800 border-blue-200'
  },
  unknown: {
    variant: 'secondary' as const,
    label: 'Unknown',
    className: 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

const scanStatusConfig = {
  PENDING: {
    variant: 'warning' as const,
    icon: Clock,
    label: 'Pending'
  },
  RUNNING: {
    variant: 'default' as const,
    icon: Activity,
    label: 'Running'
  },
  COMPLETED: {
    variant: 'success' as const,
    icon: CheckCircle,
    label: 'Completed'
  },
  FAILED: {
    variant: 'destructive' as const,
    icon: XCircle,
    label: 'Failed'
  },
  CANCELLED: {
    variant: 'secondary' as const,
    icon: XCircle,
    label: 'Cancelled'
  }
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  children,
  showIcon = true,
  size = 'md',
  className
}) => {
  const config = statusConfig[status]

  return (
    <Badge variant={config.variant} size={size} className={className}>
      {showIcon && <Icon icon={config.icon} size="xs" className="mr-1" />}
      {children || config.label}
    </Badge>
  )
}

export const SeverityBadge: React.FC<SeverityBadgeProps> = ({
  severity,
  children,
  showIcon = false,
  size = 'md',
  className
}) => {
  const config = severityConfig[severity]

  return (
    <Badge 
      variant={config.variant} 
      size={size} 
      className={cn(config.className, className)}
    >
      {children || config.label}
    </Badge>
  )
}

export const ScanStatusBadge: React.FC<ScanStatusBadgeProps> = ({
  status,
  children,
  showIcon = true,
  size = 'md',
  className
}) => {
  const config = scanStatusConfig[status]

  return (
    <Badge variant={config.variant} size={size} className={className}>
      {showIcon && <Icon icon={config.icon} size="xs" className="mr-1" />}
      {children || config.label}
    </Badge>
  )
}

import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'

interface PDFGenerationOptions {
  filename?: string
  format?: 'a4' | 'letter'
  orientation?: 'portrait' | 'landscape'
  quality?: number
  scale?: number
}

export class PDFGenerator {
  private static defaultOptions: PDFGenerationOptions = {
    format: 'a4',
    orientation: 'portrait',
    quality: 1.0,
    scale: 2
  }

  /**
   * Generate PDF from HTML element
   */
  static async generateFromElement(
    element: HTMLElement,
    options: PDFGenerationOptions = {}
  ): Promise<jsPDF> {
    const config = { ...this.defaultOptions, ...options }
    
    // Create canvas from HTML element
    const canvas = await html2canvas(element, {
      scale: config.scale,
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      logging: false,
      width: element.scrollWidth,
      height: element.scrollHeight,
      ignoreElements: (element) => {
        // Skip elements that might have unsupported CSS
        return element.classList?.contains('skip-pdf') || false
      },
      onclone: (clonedDoc) => {
        // Remove any problematic CSS that html2canvas can't handle
        const style = clonedDoc.createElement('style')
        style.textContent = `
          * {
            color: rgb(0, 0, 0) !important;
            background-color: rgb(255, 255, 255) !important;
          }
          .text-gray-900 { color: rgb(17, 24, 39) !important; }
          .text-gray-600 { color: rgb(75, 85, 99) !important; }
          .text-gray-500 { color: rgb(107, 114, 128) !important; }
          .bg-gray-50 { background-color: rgb(249, 250, 251) !important; }
          .bg-blue-600 { background-color: rgb(37, 99, 235) !important; }
          .text-blue-600 { color: rgb(37, 99, 235) !important; }
          .text-red-600 { color: rgb(220, 38, 38) !important; }
          .text-orange-600 { color: rgb(234, 88, 12) !important; }
          .text-yellow-600 { color: rgb(217, 119, 6) !important; }
          .text-green-600 { color: rgb(22, 163, 74) !important; }
          .border-gray-200 { border-color: rgb(229, 231, 235) !important; }
        `
        clonedDoc.head.appendChild(style)
      }
    })

    // Calculate dimensions
    const imgWidth = config.format === 'a4' ? 210 : 216 // mm
    const pageHeight = config.format === 'a4' ? 297 : 279 // mm
    const imgHeight = (canvas.height * imgWidth) / canvas.width
    let heightLeft = imgHeight

    // Create PDF
    const pdf = new jsPDF({
      orientation: config.orientation,
      unit: 'mm',
      format: config.format
    })

    let position = 0

    // Add first page
    pdf.addImage(
      canvas.toDataURL('image/png', config.quality),
      'PNG',
      0,
      position,
      imgWidth,
      imgHeight
    )
    heightLeft -= pageHeight

    // Add additional pages if needed
    while (heightLeft >= 0) {
      position = heightLeft - imgHeight
      pdf.addPage()
      pdf.addImage(
        canvas.toDataURL('image/png', config.quality),
        'PNG',
        0,
        position,
        imgWidth,
        imgHeight
      )
      heightLeft -= pageHeight
    }

    return pdf
  }

  /**
   * Generate and download PDF from HTML element
   */
  static async downloadFromElement(
    element: HTMLElement,
    filename: string,
    options: PDFGenerationOptions = {}
  ): Promise<void> {
    const pdf = await this.generateFromElement(element, options)
    pdf.save(filename)
  }

  /**
   * Generate PDF from HTML string
   */
  static async generateFromHTML(
    htmlString: string,
    options: PDFGenerationOptions = {}
  ): Promise<jsPDF> {
    // Create temporary element
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = htmlString
    tempDiv.style.position = 'absolute'
    tempDiv.style.left = '-9999px'
    tempDiv.style.top = '-9999px'
    tempDiv.style.width = '210mm' // A4 width
    document.body.appendChild(tempDiv)

    try {
      const pdf = await this.generateFromElement(tempDiv, options)
      return pdf
    } finally {
      document.body.removeChild(tempDiv)
    }
  }

  /**
   * Generate scan report PDF
   */
  static async generateScanReport(
    scanData: any,
    options: PDFGenerationOptions = {}
  ): Promise<jsPDF> {
    const reportElement = document.getElementById('pdf-report')
    if (!reportElement) {
      throw new Error('PDF report element not found')
    }

    const filename = options.filename || `scan-report-${scanData.scan.id}-${new Date().toISOString().split('T')[0]}.pdf`
    
    return this.generateFromElement(reportElement, {
      ...options,
      filename
    })
  }

  /**
   * Download scan report PDF
   */
  static async downloadScanReport(
    scanData: any,
    options: PDFGenerationOptions = {}
  ): Promise<void> {
    try {
      const reportElement = document.getElementById('pdf-report')
      if (!reportElement) {
        throw new Error('PDF report element not found')
      }

      const filename = options.filename || `scan-report-${scanData.scan.id}-${new Date().toISOString().split('T')[0]}.pdf`

      await this.downloadFromElement(reportElement, filename, options)
    } catch (error) {
      console.error('Error with complex PDF generation, falling back to simple version:', error)

      // Fallback to simple PDF generation
      await this.generateSimplePDF(scanData, options)
    }
  }

  /**
   * Generate simple PDF without complex CSS
   */
  static async generateSimplePDF(
    scanData: any,
    options: PDFGenerationOptions = {}
  ): Promise<void> {
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    })

    const pageWidth = pdf.internal.pageSize.getWidth()
    const margin = 20
    const lineHeight = 7
    let yPosition = margin

    // Helper function to add text with word wrapping
    const addText = (text: string, fontSize = 12, isBold = false) => {
      pdf.setFontSize(fontSize)
      if (isBold) {
        pdf.setFont('helvetica', 'bold')
      } else {
        pdf.setFont('helvetica', 'normal')
      }

      const lines = pdf.splitTextToSize(text, pageWidth - 2 * margin)
      pdf.text(lines, margin, yPosition)
      yPosition += lines.length * lineHeight
    }

    // Header
    addText('VULNERABILITY SCAN REPORT', 20, true)
    yPosition += 5
    addText(`Target: ${scanData.scan?.targetUrl || 'N/A'}`, 14)
    addText(`Generated: ${new Date().toLocaleDateString()}`, 12)
    addText(`Company: CTB Scanner`, 12)
    yPosition += 10

    // Executive Summary
    addText('EXECUTIVE SUMMARY', 16, true)
    yPosition += 5
    addText(`Total Vulnerabilities: ${scanData.vulnerabilities?.length || 0}`, 12)
    addText(`Critical: ${scanData.vulnerabilities?.filter(v => v.severity === 'critical').length || 0}`, 12)
    addText(`High: ${scanData.vulnerabilities?.filter(v => v.severity === 'high').length || 0}`, 12)
    addText(`Medium: ${scanData.vulnerabilities?.filter(v => v.severity === 'medium').length || 0}`, 12)
    addText(`Low: ${scanData.vulnerabilities?.filter(v => v.severity === 'low').length || 0}`, 12)
    yPosition += 10

    // Vulnerability Details
    addText('VULNERABILITY DETAILS', 16, true)
    yPosition += 5

    if (scanData.vulnerabilities && scanData.vulnerabilities.length > 0) {
      scanData.vulnerabilities.slice(0, 10).forEach((vuln, index) => {
        if (yPosition > 250) { // Add new page if needed
          pdf.addPage()
          yPosition = margin
        }

        addText(`${index + 1}. ${vuln.title || 'Vulnerability'}`, 12, true)
        addText(`Severity: ${vuln.severity?.toUpperCase() || 'UNKNOWN'}`, 10)
        addText(`Template: ${vuln.templateId || 'N/A'}`, 10)
        addText(`URL: ${vuln.matchedAt || 'N/A'}`, 10)
        if (vuln.description) {
          addText(`Description: ${vuln.description}`, 10)
        }
        yPosition += 5
      })
    } else {
      addText('No vulnerabilities found.', 12)
    }

    // Recommendations
    if (yPosition > 200) {
      pdf.addPage()
      yPosition = margin
    }

    addText('SECURITY RECOMMENDATIONS', 16, true)
    yPosition += 5
    addText('• Prioritize fixing Critical and High severity vulnerabilities immediately', 11)
    addText('• Implement regular security scanning as part of your development workflow', 11)
    addText('• Keep all software components and dependencies up to date', 11)
    addText('• Follow secure coding practices and conduct code reviews', 11)
    addText('• Consider implementing a Web Application Firewall (WAF)', 11)
    addText('• Establish an incident response plan for security vulnerabilities', 11)

    const filename = options.filename || `scan-report-${scanData.scan?.id || 'unknown'}-${new Date().toISOString().split('T')[0]}.pdf`
    pdf.save(filename)
  }

  /**
   * Generate asset report PDF
   */
  static async generateAssetReport(
    assetData: any,
    options: PDFGenerationOptions = {}
  ): Promise<jsPDF> {
    const reportElement = document.getElementById('pdf-report')
    if (!reportElement) {
      throw new Error('PDF report element not found')
    }

    const filename = options.filename || `asset-report-${assetData.asset.id}-${new Date().toISOString().split('T')[0]}.pdf`

    return this.generateFromElement(reportElement, {
      ...options,
      filename
    })
  }

  /**
   * Download asset report PDF
   */
  static async downloadAssetReport(
    assetData: any,
    options: PDFGenerationOptions = {}
  ): Promise<void> {
    const reportElement = document.getElementById('pdf-report')
    if (!reportElement) {
      throw new Error('PDF report element not found')
    }

    const filename = options.filename || `asset-report-${assetData.asset.id}-${new Date().toISOString().split('T')[0]}.pdf`
    
    await this.downloadFromElement(reportElement, filename, options)
  }

  /**
   * Preview PDF in new window
   */
  static async previewPDF(pdf: jsPDF): Promise<void> {
    const pdfBlob = pdf.output('blob')
    const pdfUrl = URL.createObjectURL(pdfBlob)
    const newWindow = window.open(pdfUrl, '_blank')
    
    if (!newWindow) {
      throw new Error('Unable to open PDF preview. Please check your popup blocker settings.')
    }

    // Clean up URL after a delay
    setTimeout(() => {
      URL.revokeObjectURL(pdfUrl)
    }, 10000)
  }

  /**
   * Get PDF as base64 string
   */
  static async getPDFAsBase64(
    element: HTMLElement,
    options: PDFGenerationOptions = {}
  ): Promise<string> {
    const pdf = await this.generateFromElement(element, options)
    return pdf.output('datauristring')
  }

  /**
   * Get PDF as blob
   */
  static async getPDFAsBlob(
    element: HTMLElement,
    options: PDFGenerationOptions = {}
  ): Promise<Blob> {
    const pdf = await this.generateFromElement(element, options)
    return pdf.output('blob')
  }
}

import * as React from 'react'
import { cn } from '@/lib/utils'
import { Check } from 'lucide-react'

export interface CheckboxProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> {
  label?: string
  error?: string
  indeterminate?: boolean
}

export const Checkbox = React.forwardRef<HTMLInputElement, CheckboxProps>(
  ({ className, label, error, indeterminate = false, checked, ...props }, ref) => {
    const inputRef = React.useRef<HTMLInputElement>(null)

    React.useImperativeHandle(ref, () => inputRef.current!)

    React.useEffect(() => {
      if (inputRef.current) {
        inputRef.current.indeterminate = indeterminate
      }
    }, [indeterminate])

    return (
      <div className="flex items-start space-x-2">
        <div className="relative">
          <input
            type="checkbox"
            ref={inputRef}
            checked={checked}
            className={cn(
              'h-4 w-4 rounded border border-gray-300 text-blue-600',
              'focus:ring-2 focus:ring-blue-500 focus:ring-offset-0',
              'disabled:cursor-not-allowed disabled:opacity-50',
              error && 'border-red-500',
              'sr-only',
              className
            )}
            {...props}
          />
          
          {/* Custom checkbox appearance */}
          <div
            className={cn(
              'h-4 w-4 rounded border border-gray-300 bg-white flex items-center justify-center',
              'transition-colors duration-200',
              checked && 'bg-blue-600 border-blue-600',
              indeterminate && 'bg-blue-600 border-blue-600',
              error && 'border-red-500',
              props.disabled && 'opacity-50 cursor-not-allowed'
            )}
          >
            {checked && !indeterminate && (
              <Check className="w-3 h-3 text-white" />
            )}
            {indeterminate && (
              <div className="w-2 h-0.5 bg-white rounded" />
            )}
          </div>
        </div>
        
        {label && (
          <label
            htmlFor={props.id}
            className={cn(
              'text-sm font-medium leading-none cursor-pointer',
              error ? 'text-red-600' : 'text-gray-700',
              props.disabled && 'opacity-50 cursor-not-allowed'
            )}
          >
            {label}
          </label>
        )}
        
        {error && (
          <p className="mt-1 text-sm text-red-600">{error}</p>
        )}
      </div>
    )
  }
)

Checkbox.displayName = 'Checkbox'

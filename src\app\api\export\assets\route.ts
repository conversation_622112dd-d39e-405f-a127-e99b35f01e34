import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { requireAuth } from '@/lib/auth'
import { handleApiError } from '@/lib/errors'

export async function GET(request: NextRequest) {
  try {
    // Authentication
    const currentUser = await requireAuth()

    // Get query parameters for filtering (but no pagination)
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const search = searchParams.get('search')
    const format = searchParams.get('format') || 'json'

    // Build where clause
    const where: any = {
      userId: currentUser.userId
    }

    if (status) {
      where.status = status.toUpperCase()
    }

    if (search) {
      where.OR = [
        { url: { contains: search } },
        { domain: { contains: search } },
        { title: { contains: search } }
      ]
    }

    // Get ALL assets without pagination
    const assets = await db.asset.findMany({
      where,
      include: {
        _count: {
          select: {
            scans: true,
            vulnerabilities: true
          }
        },
        scans: {
          select: {
            id: true,
            status: true,
            createdAt: true,
            totalVulns: true,
            criticalVulns: true,
            highVulns: true,
            mediumVulns: true,
            lowVulns: true,
            infoVulns: true
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 1
        }
      },
      orderBy: {
        lastScanned: 'desc'
      }
    })

    const exportData = {
      assets,
      exportedAt: new Date().toISOString(),
      totalCount: assets.length,
      filters: {
        status,
        search
      }
    }

    if (format === 'csv') {
      const csvHeaders = ['Title', 'URL', 'Domain', 'Status', 'Total Vulnerabilities', 'Critical', 'High', 'Medium', 'Low', 'Info', 'Last Scanned', 'Created']
      const csvRows = assets.map(asset => [
        asset.title,
        asset.url,
        asset.domain,
        asset.status,
        asset._count.vulnerabilities,
        asset.scans[0]?.criticalVulns || 0,
        asset.scans[0]?.highVulns || 0,
        asset.scans[0]?.mediumVulns || 0,
        asset.scans[0]?.lowVulns || 0,
        asset.scans[0]?.infoVulns || 0,
        asset.lastScanned ? new Date(asset.lastScanned).toLocaleString() : 'Never',
        new Date(asset.createdAt).toLocaleString()
      ])

      const csvContent = [csvHeaders, ...csvRows]
        .map(row => row.map(field => `"${field}"`).join(','))
        .join('\n')

      return new NextResponse(csvContent, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="assets-export-${new Date().toISOString().split('T')[0]}.csv"`
        }
      })
    }

    // Default to JSON
    return NextResponse.json(exportData)

  } catch (error) {
    return handleApiError(error)
  }
}

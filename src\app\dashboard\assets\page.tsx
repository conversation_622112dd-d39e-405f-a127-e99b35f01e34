'use client'

import { useState, useEffect } from 'react'
import {
  Database,
  Search,
  Eye,
  Filter,
  Download,
  FileText,
  Plus,
  RefreshCw,
  ExternalLink,
  Copy,
  SortAsc,
  SortDesc
} from 'lucide-react'
import { PageContainer, PageHeader } from '@/components/layout'
import { Button, Input, Badge, Card, CardContent, CardHeader, CardTitle, Alert, Pagination } from '@/components/ui'
import { NoSSR } from '@/components/no-ssr'
import Link from 'next/link'

interface Asset {
  id: string
  url: string
  domain: string
  title: string
  description?: string
  status: string
  lastScanned?: string
  createdAt: string
  _count: {
    scans: number
    vulnerabilities: number
  }
  scans: Array<{
    id: string
    status: string
    totalVulns: number
    criticalVulns: number
    highVulns: number
    mediumVulns: number
    lowVulns: number
    infoVulns: number
  }>
}

interface FilterState {
  search: string
  status: string
  vulnerabilityLevel: string
  lastScanned: string
  sortBy: string
  sortOrder: 'asc' | 'desc'
}

interface PaginationState {
  page: number
  limit: number
  total: number
  pages: number
}

export default function AssetsPage() {
  const [assets, setAssets] = useState<Asset[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  // Enhanced filtering and pagination state
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    status: '',
    vulnerabilityLevel: '',
    lastScanned: '',
    sortBy: 'createdAt',
    sortOrder: 'desc'
  })

  const [pagination, setPagination] = useState<PaginationState>({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  })

  useEffect(() => {
    fetchAssets()
  }, [pagination.page, filters.search, filters.status])



  // Reset to page 1 when filters change
  useEffect(() => {
    if (pagination.page !== 1) {
      setPagination(prev => ({ ...prev, page: 1 }))
    }
  }, [filters.search, filters.status])

  const fetchAssets = async () => {
    try {
      setLoading(true)
      setError(null)
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString()
      })

      if (filters.search) {
        params.append('search', filters.search)
      }

      if (filters.status) {
        params.append('status', filters.status)
      }

      const response = await fetch(`/api/assets?${params}`)
      if (response.ok) {
        const data = await response.json()
        setAssets(data.assets)
        setPagination(prev => ({
          ...prev,
          total: data.pagination.total,
          pages: data.pagination.pages
        }))
      } else {
        setError('Failed to fetch assets')
      }
    } catch (error) {
      setError('Failed to fetch assets')
      console.error('Failed to fetch assets:', error)
    } finally {
      setLoading(false)
    }
  }

  // Helper functions
  const handleFilterChange = (key: keyof FilterState, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }))
  }

  const handleExport = async (format: 'json' | 'csv') => {
    try {
      // Build export URL with current filters
      const params = new URLSearchParams({
        format
      })

      if (filters.status) {
        params.append('status', filters.status)
      }

      if (filters.search) {
        params.append('search', filters.search)
      }

      const response = await fetch(`/api/export/assets?${params}`)

      if (!response.ok) {
        throw new Error('Export failed')
      }

      if (format === 'csv') {
        // For CSV, the response is already formatted as CSV content
        const csvContent = await response.text()
        const blob = new Blob([csvContent], { type: 'text/csv' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `assets-export-${new Date().toISOString().split('T')[0]}.csv`
        a.click()
        URL.revokeObjectURL(url)
      } else {
        // For JSON, get the data and create blob
        const data = await response.json()
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `assets-export-${new Date().toISOString().split('T')[0]}.json`
        a.click()
        URL.revokeObjectURL(url)
      }
    } catch (error) {
      console.error('Export failed:', error)
      // You might want to show a toast notification here
    }
  }

  // Since we're using server-side pagination, we use assets directly
  const displayedAssets = assets

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      ACTIVE: 'bg-green-50 text-green-700 border-green-200',
      INACTIVE: 'bg-gray-50 text-gray-700 border-gray-200',
      ARCHIVED: 'bg-red-50 text-red-700 border-red-200'
    }

    return (
      <Badge variant="outline" className={statusConfig[status as keyof typeof statusConfig] || statusConfig.INACTIVE}>
        {status.toLowerCase()}
      </Badge>
    )
  }

  const getSecurityScore = (asset: Asset) => {
    const scan = asset.scans[0]
    if (!scan || !scan.totalVulns) return 100

    const criticalWeight = (scan.criticalVulns || 0) * 20
    const highWeight = (scan.highVulns || 0) * 10
    const mediumWeight = (scan.mediumVulns || 0) * 5

    const score = Math.max(0, 100 - (criticalWeight + highWeight + mediumWeight))
    return isNaN(score) ? 100 : Math.round(score)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        <PageContainer maxWidth="full" className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </PageContainer>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <PageContainer maxWidth="full" className="space-y-6">
        <PageHeader
          title="Asset Inventory"
          description="Manage and monitor your security assets"
          actions={
            <div className="flex items-center space-x-3">
              <Button
                onClick={() => handleExport('json')}
                variant="outline"
                size="sm"
                className="bg-white/80 backdrop-blur-sm border-gray-200 hover:bg-white"
              >
                <Download className="h-4 w-4 mr-2" />
                Export JSON
              </Button>
              <Button
                onClick={() => handleExport('csv')}
                variant="outline"
                size="sm"
                className="bg-white/80 backdrop-blur-sm border-gray-200 hover:bg-white"
              >
                <FileText className="h-4 w-4 mr-2" />
                Export CSV
              </Button>
              <Link href="/dashboard/scan">
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Asset
                </Button>
              </Link>
            </div>
          }
        />

        {error && (
          <Alert variant="error" className="border-0 shadow-lg bg-red-50/80 backdrop-blur-sm">
            {error}
          </Alert>
        )}

        {/* Enhanced Filtering */}
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center text-lg font-semibold text-gray-900">
              <Filter className="h-5 w-5 mr-2 text-blue-500" />
              Filter & Search Assets
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              {/* Search Input */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search assets..."
                    value={filters.search}
                    onChange={(e) => handleFilterChange('search', e.target.value)}
                    className="pl-10 bg-white border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                  />
                </div>
              </div>

              {/* Status Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Status</label>
                <select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  className="w-full px-3 py-2 bg-white border border-gray-200 rounded-md focus:border-blue-500 focus:ring-blue-500 text-sm"
                >
                  <option value="">All Statuses</option>
                  <option value="ACTIVE">Active</option>
                  <option value="INACTIVE">Inactive</option>
                  <option value="ARCHIVED">Archived</option>
                </select>
              </div>

              {/* Vulnerability Level Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Vulnerability Level</label>
                <select
                  value={filters.vulnerabilityLevel}
                  onChange={(e) => handleFilterChange('vulnerabilityLevel', e.target.value)}
                  className="w-full px-3 py-2 bg-white border border-gray-200 rounded-md focus:border-blue-500 focus:ring-blue-500 text-sm"
                >
                  <option value="">All Levels</option>
                  <option value="critical">Has Critical</option>
                  <option value="high">Has High</option>
                  <option value="medium">Has Medium</option>
                  <option value="low">Has Low</option>
                  <option value="none">No Vulnerabilities</option>
                </select>
              </div>

              {/* Last Scanned Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Last Scanned</label>
                <select
                  value={filters.lastScanned}
                  onChange={(e) => handleFilterChange('lastScanned', e.target.value)}
                  className="w-full px-3 py-2 bg-white border border-gray-200 rounded-md focus:border-blue-500 focus:ring-blue-500 text-sm"
                >
                  <option value="">Any Time</option>
                  <option value="today">Today</option>
                  <option value="week">This Week</option>
                  <option value="month">This Month</option>
                  <option value="never">Never Scanned</option>
                </select>
              </div>

              {/* Sort Options */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Sort By</label>
                <div className="flex space-x-2">
                  <select
                    value={filters.sortBy}
                    onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                    className="flex-1 px-3 py-2 bg-white border border-gray-200 rounded-md focus:border-blue-500 focus:ring-blue-500 text-sm"
                  >
                    <option value="createdAt">Created</option>
                    <option value="lastScanned">Last Scan</option>
                    <option value="title">Name</option>
                    <option value="vulnerabilities">Vulnerabilities</option>
                  </select>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleFilterChange('sortOrder', filters.sortOrder === 'asc' ? 'desc' : 'asc')}
                    className="px-2"
                  >
                    {filters.sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
                  </Button>
                </div>
              </div>
            </div>

            {/* Filter Summary */}
            <div className="flex items-center justify-between pt-4 border-t border-gray-200">
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <span>
                  Showing {displayedAssets.length} of {pagination.total} assets
                </span>
                {filters.search && (
                  <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                    Search: &quot;{filters.search}&quot;
                  </Badge>
                )}
                {filters.status && (
                  <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                    Status: {filters.status}
                  </Badge>
                )}
                {filters.vulnerabilityLevel && (
                  <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                    Level: {filters.vulnerabilityLevel}
                  </Badge>
                )}
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={fetchAssets}
                  disabled={loading}
                >
                  <RefreshCw className="h-4 w-4 mr-1" />
                  Refresh
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setFilters({
                    search: '',
                    status: '',
                    vulnerabilityLevel: '',
                    lastScanned: '',
                    sortBy: 'createdAt',
                    sortOrder: 'desc'
                  })}
                  className="text-gray-600 hover:text-gray-800"
                >
                  Clear Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Enhanced Assets Table */}
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center text-lg font-semibold text-gray-900">
              <Database className="h-5 w-5 mr-2 text-blue-500" />
              Assets ({pagination.total})
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0 sm:p-0">
            {displayedAssets.length === 0 ? (
              <div className="text-center py-12">
                <Database className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No assets found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {filters.search || filters.status || filters.vulnerabilityLevel
                    ? 'Try adjusting your filters to see more results.'
                    : 'Start by scanning a website to add it to your asset inventory.'
                  }
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto -mx-4 sm:mx-0">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50/80">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[300px]">
                        Asset
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[100px]">
                        Status
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]">
                        Security Score
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[200px]">
                        Vulnerabilities
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[140px]">
                        Last Scan
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[180px]">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {displayedAssets.map((asset) => {
                      const securityScore = getSecurityScore(asset)
                      return (
                        <tr key={asset.id} className="hover:bg-gray-50/50 transition-colors">
                          <td className="px-4 py-4">
                            <div className="flex items-center space-x-3">
                              <div className="flex-shrink-0">
                                <div className={`w-3 h-3 rounded-full ${
                                  asset.status === 'ACTIVE' ? 'bg-green-400' :
                                  asset.status === 'INACTIVE' ? 'bg-gray-400' :
                                  'bg-red-400'
                                }`} />
                              </div>
                              <div className="min-w-0 flex-1">
                                <div className="text-sm font-medium text-gray-900 truncate">
                                  {asset.title}
                                </div>
                                <div className="text-xs text-gray-500 truncate">
                                  {asset.url}
                                </div>
                                <div className="text-xs text-gray-400 truncate">
                                  {asset.domain}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-4 py-4">
                            {getStatusBadge(asset.status)}
                          </td>
                          <td className="px-4 py-4">
                            <div className="flex items-center space-x-2">
                              <div className={`text-lg font-bold ${
                                securityScore >= 80 ? 'text-green-600' :
                                securityScore >= 60 ? 'text-yellow-600' :
                                securityScore >= 40 ? 'text-orange-600' :
                                'text-red-600'
                              }`}>
                                {isNaN(securityScore) ? '--' : securityScore}
                              </div>
                              <div className="text-xs text-gray-500">/100</div>
                            </div>
                          </td>
                          <td className="px-4 py-4">
                            <div className="space-y-1">
                              <div className="flex items-center space-x-1">
                                <span className="text-lg font-bold text-gray-900">{asset._count.vulnerabilities || 0}</span>
                                <span className="text-xs text-gray-500">total</span>
                              </div>
                              {asset.scans[0] ? (
                                <div className="flex flex-wrap gap-1">
                                  {(asset.scans[0].criticalVulns || 0) > 0 && (
                                    <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 text-xs px-1 py-0">
                                      {asset.scans[0].criticalVulns} Critical
                                    </Badge>
                                  )}
                                  {(asset.scans[0].highVulns || 0) > 0 && (
                                    <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200 text-xs px-1 py-0">
                                      {asset.scans[0].highVulns} High
                                    </Badge>
                                  )}
                                  {(asset.scans[0].mediumVulns || 0) > 0 && (
                                    <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200 text-xs px-1 py-0">
                                      {asset.scans[0].mediumVulns} Medium
                                    </Badge>
                                  )}
                                  {(asset.scans[0].lowVulns || 0) > 0 && (
                                    <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 text-xs px-1 py-0">
                                      {asset.scans[0].lowVulns} Low
                                    </Badge>
                                  )}
                                  {(asset.scans[0].infoVulns || 0) > 0 && (
                                    <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 text-xs px-1 py-0">
                                      {asset.scans[0].infoVulns} Info
                                    </Badge>
                                  )}
                                </div>
                              ) : (
                                <div className="text-xs text-gray-400">No scan data</div>
                              )}
                            </div>
                          </td>
                          <td className="px-4 py-4 text-sm">
                            <NoSSR fallback="...">
                              {asset.lastScanned ? (
                                <div>
                                  <div className="text-gray-900">
                                    {new Date(asset.lastScanned).toLocaleDateString()}
                                  </div>
                                  <div className="text-xs text-gray-500">
                                    {new Date(asset.lastScanned).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                                  </div>
                                </div>
                              ) : (
                                <span className="text-gray-400">Never scanned</span>
                              )}
                            </NoSSR>
                          </td>
                          <td className="px-4 py-4">
                            <div className="flex items-center space-x-1">
                              <Link href={`/dashboard/assets/${asset.id}`}>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="text-xs px-2 py-1"
                                >
                                  <Eye className="h-3 w-3 mr-1" />
                                  View
                                </Button>
                              </Link>
                              <Link href={`/dashboard/scan?url=${encodeURIComponent(asset.url)}`}>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="text-xs px-2 py-1"
                                >
                                  <Search className="h-3 w-3 mr-1" />
                                  Scan
                                </Button>
                              </Link>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => window.open(asset.url, '_blank')}
                                className="text-xs px-2 py-1"
                              >
                                <ExternalLink className="h-3 w-3" />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => navigator.clipboard.writeText(JSON.stringify(asset, null, 2))}
                                className="text-xs px-2 py-1"
                                title="Copy asset data"
                              >
                                <Copy className="h-3 w-3" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      )
                    })}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Enhanced Pagination */}
        {pagination.pages > 1 && (
          <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
            <CardContent className="p-4">
              <Pagination
                currentPage={pagination.page}
                totalPages={pagination.pages}
                totalItems={pagination.total}
                itemsPerPage={pagination.limit}
                onPageChange={handlePageChange}
                showInfo={true}
                size="sm"
              />
            </CardContent>
          </Card>
        )}
      </PageContainer>
    </div>
  )
}

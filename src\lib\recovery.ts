/**
 * Automatic Service Recovery System
 * 
 * This module provides automatic recovery capabilities for the CTB Scanner
 * application, including service health monitoring, automatic restarts,
 * and graceful degradation strategies.
 */

import { startupManager } from './startup'
import { jobQueue } from './job-queue'
import { processManager } from './process-manager'
import { nucleiScanner } from './nuclei'

interface RecoveryConfig {
  healthCheckInterval: number
  maxRecoveryAttempts: number
  recoveryDelayMs: number
  enableAutoRecovery: boolean
  enableGracefulDegradation: boolean
}

interface RecoveryState {
  isRecovering: boolean
  lastRecoveryAttempt: Date | null
  recoveryAttempts: number
  lastHealthCheck: Date | null
  degradedServices: string[]
}

class RecoveryService {
  private config: RecoveryConfig
  private state: RecoveryState
  private healthCheckInterval: NodeJS.Timeout | null = null
  private recoveryInProgress = false

  constructor(config: Partial<RecoveryConfig> = {}) {
    this.config = {
      healthCheckInterval: 60000, // 1 minute
      maxRecoveryAttempts: 3,
      recoveryDelayMs: 30000, // 30 seconds
      enableAutoRecovery: true,
      enableGracefulDegradation: true,
      ...config
    }

    this.state = {
      isRecovering: false,
      lastRecoveryAttempt: null,
      recoveryAttempts: 0,
      lastHealthCheck: null,
      degradedServices: []
    }
  }

  /**
   * Start the recovery service
   */
  start(): void {
    if (this.healthCheckInterval) {
      this.stop()
    }

    console.log('🔄 Starting automatic recovery service...')
    
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck()
    }, this.config.healthCheckInterval)

    console.log(`✅ Recovery service started (health checks every ${this.config.healthCheckInterval / 1000}s)`)
  }

  /**
   * Stop the recovery service
   */
  stop(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval)
      this.healthCheckInterval = null
    }

    console.log('🛑 Recovery service stopped')
  }

  /**
   * Perform comprehensive health check
   */
  private async performHealthCheck(): Promise<void> {
    if (this.recoveryInProgress) {
      console.log('⏳ Recovery in progress, skipping health check')
      return
    }

    try {
      console.log('🔍 Performing automatic health check...')
      this.state.lastHealthCheck = new Date()

      const healthResults = await this.checkAllServices()
      const unhealthyServices = Object.entries(healthResults)
        .filter(([_, healthy]) => !healthy)
        .map(([service, _]) => service)

      if (unhealthyServices.length > 0) {
        console.warn(`⚠️ Unhealthy services detected: ${unhealthyServices.join(', ')}`)
        this.state.degradedServices = unhealthyServices

        if (this.config.enableAutoRecovery) {
          await this.attemptRecovery(unhealthyServices)
        } else {
          console.warn('🚫 Auto-recovery is disabled, manual intervention required')
        }
      } else {
        console.log('✅ All services healthy')
        this.state.degradedServices = []
        this.state.recoveryAttempts = 0 // Reset recovery attempts on success
      }

    } catch (error) {
      console.error('❌ Health check failed:', error)
    }
  }

  /**
   * Check health of all services
   */
  private async checkAllServices(): Promise<Record<string, boolean>> {
    const results: Record<string, boolean> = {}

    // Check database
    try {
      const { db } = await import('./db')
      await db.$queryRaw`SELECT 1 as health_check`
      results.database = true
    } catch (error) {
      console.error('❌ Database health check failed:', error)
      results.database = false
    }

    // Check Nuclei
    try {
      const nucleiHealthy = await nucleiScanner.checkNucleiInstallation()
      results.nuclei = nucleiHealthy
    } catch (error) {
      console.error('❌ Nuclei health check failed:', error)
      results.nuclei = false
    }

    // Check job queue
    try {
      results.jobQueue = jobQueue.isInitialized()
    } catch (error) {
      console.error('❌ Job queue health check failed:', error)
      results.jobQueue = false
    }

    // Check for stuck processes
    try {
      const processStats = processManager.getProcessStats()
      results.processManager = processStats.stuckProcesses === 0
      
      if (processStats.stuckProcesses > 0) {
        console.warn(`⚠️ ${processStats.stuckProcesses} stuck processes detected`)
      }
    } catch (error) {
      console.error('❌ Process manager health check failed:', error)
      results.processManager = false
    }

    return results
  }

  /**
   * Attempt automatic recovery
   */
  private async attemptRecovery(unhealthyServices: string[]): Promise<void> {
    if (this.state.recoveryAttempts >= this.config.maxRecoveryAttempts) {
      console.error(`💥 Maximum recovery attempts (${this.config.maxRecoveryAttempts}) reached, giving up`)
      
      if (this.config.enableGracefulDegradation) {
        await this.enableGracefulDegradation(unhealthyServices)
      }
      return
    }

    this.recoveryInProgress = true
    this.state.isRecovering = true
    this.state.recoveryAttempts++
    this.state.lastRecoveryAttempt = new Date()

    console.log(`🔧 Starting recovery attempt ${this.state.recoveryAttempts}/${this.config.maxRecoveryAttempts}`)

    try {
      for (const service of unhealthyServices) {
        await this.recoverService(service)
      }

      // Wait before checking if recovery was successful
      console.log(`⏳ Waiting ${this.config.recoveryDelayMs / 1000}s before verifying recovery...`)
      await new Promise(resolve => setTimeout(resolve, this.config.recoveryDelayMs))

      // Verify recovery
      const healthResults = await this.checkAllServices()
      const stillUnhealthy = Object.entries(healthResults)
        .filter(([_, healthy]) => !healthy)
        .map(([service, _]) => service)

      if (stillUnhealthy.length === 0) {
        console.log('✅ Recovery successful, all services restored')
        this.state.recoveryAttempts = 0
        this.state.degradedServices = []
      } else {
        console.warn(`⚠️ Recovery partially successful, still unhealthy: ${stillUnhealthy.join(', ')}`)
      }

    } catch (error) {
      console.error('❌ Recovery attempt failed:', error)
    } finally {
      this.recoveryInProgress = false
      this.state.isRecovering = false
    }
  }

  /**
   * Recover a specific service
   */
  private async recoverService(service: string): Promise<void> {
    console.log(`🔧 Attempting to recover service: ${service}`)

    try {
      switch (service) {
        case 'database':
          // Database recovery is typically handled at the connection level
          console.log('🔄 Database recovery: reconnection will be attempted automatically')
          break

        case 'nuclei':
          console.log('🔄 Nuclei recovery: performing comprehensive health check')
          const nucleiHealth = await nucleiScanner.performHealthCheck(2, 5000)
          if (!nucleiHealth.healthy) {
            console.warn('⚠️ Nuclei recovery failed:', nucleiHealth.details)
          } else {
            console.log('✅ Nuclei recovery successful')
          }
          break

        case 'jobQueue':
          console.log('🔄 Job queue recovery: reinitializing')
          jobQueue.initialize()
          await jobQueue.recoverStuckScans()
          console.log('✅ Job queue recovery completed')
          break

        case 'processManager':
          console.log('🔄 Process manager recovery: cleaning up stuck processes')
          const stuckProcesses = processManager.getDetailedProcessInfo()
            .filter(p => p.isStuck)
          
          for (const process of stuckProcesses) {
            console.log(`🧹 Cleaning up stuck process: ${process.scanId}`)
            processManager.killProcess(process.scanId)
          }
          console.log('✅ Process manager recovery completed')
          break

        default:
          console.warn(`⚠️ Unknown service for recovery: ${service}`)
      }
    } catch (error) {
      console.error(`❌ Failed to recover service ${service}:`, error)
    }
  }

  /**
   * Enable graceful degradation
   */
  private async enableGracefulDegradation(unhealthyServices: string[]): Promise<void> {
    console.log('🔄 Enabling graceful degradation mode...')

    try {
      // Implement graceful degradation strategies
      if (unhealthyServices.includes('nuclei')) {
        console.log('⚠️ Nuclei unavailable: new scans will be queued but not processed')
        // Could implement mock scan results or queue-only mode
      }

      if (unhealthyServices.includes('database')) {
        console.log('⚠️ Database unavailable: application will operate in read-only mode')
        // Could implement in-memory caching or read-only mode
      }

      if (unhealthyServices.includes('jobQueue')) {
        console.log('⚠️ Job queue unavailable: scans will be processed synchronously')
        // Could implement direct processing mode
      }

      console.log('✅ Graceful degradation enabled')
    } catch (error) {
      console.error('❌ Failed to enable graceful degradation:', error)
    }
  }

  /**
   * Get recovery status
   */
  getStatus(): {
    isRunning: boolean
    isRecovering: boolean
    recoveryAttempts: number
    maxRecoveryAttempts: number
    lastHealthCheck: Date | null
    lastRecoveryAttempt: Date | null
    degradedServices: string[]
    config: RecoveryConfig
  } {
    return {
      isRunning: this.healthCheckInterval !== null,
      isRecovering: this.state.isRecovering,
      recoveryAttempts: this.state.recoveryAttempts,
      maxRecoveryAttempts: this.config.maxRecoveryAttempts,
      lastHealthCheck: this.state.lastHealthCheck,
      lastRecoveryAttempt: this.state.lastRecoveryAttempt,
      degradedServices: [...this.state.degradedServices],
      config: { ...this.config }
    }
  }

  /**
   * Force recovery attempt
   */
  async forceRecovery(): Promise<void> {
    console.log('🔧 Manual recovery requested...')
    
    const healthResults = await this.checkAllServices()
    const unhealthyServices = Object.entries(healthResults)
      .filter(([_, healthy]) => !healthy)
      .map(([service, _]) => service)

    if (unhealthyServices.length === 0) {
      console.log('✅ All services are healthy, no recovery needed')
      return
    }

    await this.attemptRecovery(unhealthyServices)
  }
}

// Export singleton instance
export const recoveryService = new RecoveryService()

// Export convenience functions
export const startRecoveryService = () => recoveryService.start()
export const stopRecoveryService = () => recoveryService.stop()
export const getRecoveryStatus = () => recoveryService.getStatus()
export const forceRecovery = () => recoveryService.forceRecovery()

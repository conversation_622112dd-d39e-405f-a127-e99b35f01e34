import { test, expect } from '@playwright/test'

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
  })

  test('should display login page by default', async ({ page }) => {
    await expect(page).toHaveTitle(/CTB Scanner/)
    await expect(page.getByRole('heading', { name: /sign in/i })).toBeVisible()
    await expect(page.getByPlaceholder(/email/i)).toBeVisible()
    await expect(page.getByPlaceholder(/password/i)).toBeVisible()
    await expect(page.getByRole('button', { name: /sign in/i })).toBeVisible()
  })

  test('should navigate to signup page', async ({ page }) => {
    await page.getByRole('link', { name: /sign up/i }).click()
    await expect(page.getByRole('heading', { name: /create account/i })).toBeVisible()
    await expect(page.getByPlaceholder(/first name/i)).toBeVisible()
    await expect(page.getByPlaceholder(/last name/i)).toBeVisible()
    await expect(page.getByPlaceholder(/company name/i)).toBeVisible()
  })

  test('should show validation errors for empty login form', async ({ page }) => {
    await page.getByRole('button', { name: /sign in/i }).click()
    
    await expect(page.getByText(/email is required/i)).toBeVisible()
    await expect(page.getByText(/password is required/i)).toBeVisible()
  })

  test('should show validation errors for invalid email', async ({ page }) => {
    await page.getByPlaceholder(/email/i).fill('invalid-email')
    await page.getByPlaceholder(/password/i).fill('password123')
    await page.getByRole('button', { name: /sign in/i }).click()
    
    await expect(page.getByText(/invalid email/i)).toBeVisible()
  })

  test('should complete signup flow', async ({ page }) => {
    // Navigate to signup
    await page.getByRole('link', { name: /sign up/i }).click()
    
    // Fill signup form
    await page.getByPlaceholder(/first name/i).fill('John')
    await page.getByPlaceholder(/last name/i).fill('Doe')
    await page.getByPlaceholder(/company name/i).fill('Test Company')
    await page.getByPlaceholder(/country/i).fill('United States')
    await page.getByPlaceholder(/email/i).fill(`test-${Date.now()}@example.com`)
    await page.getByPlaceholder(/^password$/i).fill('TestPassword123!')
    await page.getByPlaceholder(/confirm password/i).fill('TestPassword123!')
    
    // Submit form
    await page.getByRole('button', { name: /create account/i }).click()
    
    // Should redirect to dashboard
    await expect(page).toHaveURL('/dashboard')
    await expect(page.getByRole('heading', { name: /dashboard/i })).toBeVisible()
  })

  test('should show error for duplicate email signup', async ({ page }) => {
    const email = `duplicate-${Date.now()}@example.com`
    
    // First signup
    await page.getByRole('link', { name: /sign up/i }).click()
    await page.getByPlaceholder(/first name/i).fill('John')
    await page.getByPlaceholder(/last name/i).fill('Doe')
    await page.getByPlaceholder(/company name/i).fill('Test Company')
    await page.getByPlaceholder(/country/i).fill('United States')
    await page.getByPlaceholder(/email/i).fill(email)
    await page.getByPlaceholder(/^password$/i).fill('TestPassword123!')
    await page.getByPlaceholder(/confirm password/i).fill('TestPassword123!')
    await page.getByRole('button', { name: /create account/i }).click()
    
    // Wait for redirect and logout
    await expect(page).toHaveURL('/dashboard')
    await page.getByRole('button', { name: /logout/i }).click()
    
    // Try to signup again with same email
    await page.getByRole('link', { name: /sign up/i }).click()
    await page.getByPlaceholder(/first name/i).fill('Jane')
    await page.getByPlaceholder(/last name/i).fill('Smith')
    await page.getByPlaceholder(/company name/i).fill('Another Company')
    await page.getByPlaceholder(/country/i).fill('Canada')
    await page.getByPlaceholder(/email/i).fill(email)
    await page.getByPlaceholder(/^password$/i).fill('AnotherPassword123!')
    await page.getByPlaceholder(/confirm password/i).fill('AnotherPassword123!')
    await page.getByRole('button', { name: /create account/i }).click()
    
    // Should show error
    await expect(page.getByText(/email already exists/i)).toBeVisible()
  })

  test('should complete login flow', async ({ page }) => {
    const email = `login-test-${Date.now()}@example.com`
    const password = 'TestPassword123!'
    
    // First create an account
    await page.getByRole('link', { name: /sign up/i }).click()
    await page.getByPlaceholder(/first name/i).fill('John')
    await page.getByPlaceholder(/last name/i).fill('Doe')
    await page.getByPlaceholder(/company name/i).fill('Test Company')
    await page.getByPlaceholder(/country/i).fill('United States')
    await page.getByPlaceholder(/email/i).fill(email)
    await page.getByPlaceholder(/^password$/i).fill(password)
    await page.getByPlaceholder(/confirm password/i).fill(password)
    await page.getByRole('button', { name: /create account/i }).click()
    
    // Logout
    await expect(page).toHaveURL('/dashboard')
    await page.getByRole('button', { name: /logout/i }).click()
    
    // Login
    await page.getByPlaceholder(/email/i).fill(email)
    await page.getByPlaceholder(/password/i).fill(password)
    await page.getByRole('button', { name: /sign in/i }).click()
    
    // Should redirect to dashboard
    await expect(page).toHaveURL('/dashboard')
    await expect(page.getByRole('heading', { name: /dashboard/i })).toBeVisible()
  })

  test('should show error for invalid login credentials', async ({ page }) => {
    await page.getByPlaceholder(/email/i).fill('<EMAIL>')
    await page.getByPlaceholder(/password/i).fill('wrongpassword')
    await page.getByRole('button', { name: /sign in/i }).click()
    
    await expect(page.getByText(/invalid credentials/i)).toBeVisible()
  })

  test('should logout successfully', async ({ page }) => {
    const email = `logout-test-${Date.now()}@example.com`
    const password = 'TestPassword123!'
    
    // Create account and login
    await page.getByRole('link', { name: /sign up/i }).click()
    await page.getByPlaceholder(/first name/i).fill('John')
    await page.getByPlaceholder(/last name/i).fill('Doe')
    await page.getByPlaceholder(/company name/i).fill('Test Company')
    await page.getByPlaceholder(/country/i).fill('United States')
    await page.getByPlaceholder(/email/i).fill(email)
    await page.getByPlaceholder(/^password$/i).fill(password)
    await page.getByPlaceholder(/confirm password/i).fill(password)
    await page.getByRole('button', { name: /create account/i }).click()
    
    // Should be on dashboard
    await expect(page).toHaveURL('/dashboard')
    
    // Logout
    await page.getByRole('button', { name: /logout/i }).click()
    
    // Should redirect to login page
    await expect(page).toHaveURL('/')
    await expect(page.getByRole('heading', { name: /sign in/i })).toBeVisible()
  })

  test('should protect dashboard route when not authenticated', async ({ page }) => {
    await page.goto('/dashboard')
    
    // Should redirect to login
    await expect(page).toHaveURL('/')
    await expect(page.getByRole('heading', { name: /sign in/i })).toBeVisible()
  })

  test('should redirect authenticated users from auth pages', async ({ page }) => {
    const email = `redirect-test-${Date.now()}@example.com`
    const password = 'TestPassword123!'
    
    // Create account
    await page.getByRole('link', { name: /sign up/i }).click()
    await page.getByPlaceholder(/first name/i).fill('John')
    await page.getByPlaceholder(/last name/i).fill('Doe')
    await page.getByPlaceholder(/company name/i).fill('Test Company')
    await page.getByPlaceholder(/country/i).fill('United States')
    await page.getByPlaceholder(/email/i).fill(email)
    await page.getByPlaceholder(/^password$/i).fill(password)
    await page.getByPlaceholder(/confirm password/i).fill(password)
    await page.getByRole('button', { name: /create account/i }).click()
    
    // Should be on dashboard
    await expect(page).toHaveURL('/dashboard')
    
    // Try to visit login page
    await page.goto('/')
    
    // Should redirect back to dashboard
    await expect(page).toHaveURL('/dashboard')
  })
})

# 🛡️ Security Architecture

<div align="center">

[![Security](https://img.shields.io/badge/Security-Enterprise_Grade-success?style=for-the-badge&logo=shield)](https://owasp.org/)
[![JWT](https://img.shields.io/badge/JWT-Authentication-000000?style=for-the-badge&logo=jsonwebtokens)](https://jwt.io/)
[![OWASP](https://img.shields.io/badge/OWASP-Compliant-blue?style=for-the-badge&logo=owasp)](https://owasp.org/Top10/)

*Comprehensive security architecture and implementation guide for CTB Scanner*

</div>

---

## 🔒 Security Overview

CTB Scanner implements **enterprise-grade security** with multiple layers of protection, following OWASP best practices and industry standards.

<div align="center">

```mermaid
graph TB
    A[Client Request] --> B[Rate Limiting]
    B --> C[CORS & Security Headers]
    C --> D[Input Validation]
    D --> E[Authentication]
    E --> F[Authorization]
    F --> G[Business Logic]
    G --> H[Database Layer]
    
    I[Security Middleware] --> B
    I --> C
    I --> D
    
    J[JWT Tokens] --> E
    K[Bcrypt Hashing] --> H
    L[Prisma ORM] --> H
    
    style A fill:#e1f5fe
    style I fill:#f3e5f5
    style J fill:#e8f5e8
    style K fill:#fff3e0
    style L fill:#fce4ec
```

</div>

## 🔐 Authentication System

### JWT Token Management

**Implementation**: Secure JWT tokens with HTTP-only cookies

```typescript
// Token Configuration
const JWT_CONFIG = {
  algorithm: 'HS256',
  expiresIn: '7d',
  issuer: 'ctb-scanner',
  audience: 'ctb-users'
}

// Secure Cookie Settings
const COOKIE_CONFIG = {
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'strict',
  maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
}
```

**Security Features**:
- ✅ **HTTP-Only Cookies**: Prevents XSS token theft
- ✅ **Secure Flag**: HTTPS-only in production
- ✅ **SameSite Protection**: CSRF prevention
- ✅ **Token Expiration**: 7-day automatic expiry
- ✅ **Signature Verification**: HMAC-SHA256 signing

### Password Security

**Hashing Strategy**: Bcrypt with adaptive salt rounds

```typescript
// Password Hashing Configuration
const BCRYPT_CONFIG = {
  saltRounds: 12,
  maxLength: 128,
  minLength: 8
}

// Password Requirements
const PASSWORD_POLICY = {
  minLength: 8,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true
}
```

**Security Features**:
- 🔒 **Bcrypt Hashing**: Industry-standard password hashing
- 🧂 **Salt Rounds**: 12 rounds for optimal security/performance
- 📏 **Length Validation**: 8-128 character limits
- 🔤 **Complexity Requirements**: Mixed case, numbers, symbols
- 🚫 **Common Password Prevention**: Dictionary attack protection

## 🛡️ Input Validation & Sanitization

### Zod Schema Validation

**Comprehensive validation** for all user inputs using Zod schemas:

```typescript
// User Registration Schema
const signupSchema = z.object({
  firstName: z.string().min(1).max(100).regex(/^[a-zA-Z\s]+$/),
  lastName: z.string().min(1).max(100).regex(/^[a-zA-Z\s]+$/),
  email: z.string().email().max(255),
  password: z.string().min(8).max(128).regex(PASSWORD_REGEX),
  companyName: z.string().min(1).max(200),
  country: z.string().min(1).max(100)
})

// Scan Request Schema
const scanSchema = z.object({
  url: z.string().url().refine(validateUrl),
  severity: z.array(z.enum(['critical', 'high', 'medium', 'low', 'info'])),
  tags: z.array(z.string()).optional(),
  templates: z.array(z.string()).optional()
})
```

**Validation Features**:
- ✅ **Type Safety**: TypeScript integration
- ✅ **Format Validation**: Email, URL, regex patterns
- ✅ **Length Limits**: Prevent buffer overflow attacks
- ✅ **Character Filtering**: Alphanumeric and safe characters only
- ✅ **Business Logic**: Custom validation rules

### URL Security Validation

**Advanced URL sanitization** to prevent SSRF and other attacks:

```typescript
const URL_SECURITY = {
  allowedProtocols: ['http:', 'https:'],
  blockedHosts: [
    'localhost', '127.0.0.1', '0.0.0.0',
    '10.0.0.0/8', '**********/12', '***********/16'
  ],
  maxLength: 500,
  requirePublicIP: true
}
```

**Protection Against**:
- 🚫 **SSRF Attacks**: Private IP blocking
- 🚫 **Protocol Abuse**: HTTP/HTTPS only
- 🚫 **Local Access**: Localhost prevention
- 🚫 **Internal Networks**: RFC 1918 blocking

## 🚦 Rate Limiting & DDoS Protection

### Multi-Layer Rate Limiting

**Granular rate limiting** for different endpoint types:

| Endpoint Type | Limit | Window | Purpose |
|---------------|-------|--------|---------|
| **Authentication** | 50 requests | 15 minutes | Brute force prevention |
| **Scan Creation** | 200 requests | 15 minutes | Resource protection |
| **General API** | 500 requests | 15 minutes | Service availability |
| **Asset Management** | 300 requests | 15 minutes | Data integrity |

**Implementation Features**:
- 🔄 **Sliding Window**: Precise rate calculation
- 🌐 **IP-Based Tracking**: Per-client limits
- 👤 **User-Based Limits**: Authenticated user tracking
- 📊 **Redis Backend**: Distributed rate limiting
- ⚡ **Fast Lookup**: O(1) rate check performance

### Concurrency Control

**Scan concurrency limits** to prevent resource exhaustion:

```typescript
const CONCURRENCY_LIMITS = {
  maxConcurrentScans: 3,           // Global limit
  maxConcurrentScansPerUser: 1,    // Per-user limit
  scanTimeout: 30 * 60 * 1000,     // 30 minutes
  queueTimeout: 60 * 60 * 1000     // 1 hour
}
```

## 🔒 Security Headers & CORS

### HTTP Security Headers

**Comprehensive security headers** for browser protection:

```typescript
const SECURITY_HEADERS = {
  'X-Frame-Options': 'DENY',
  'X-Content-Type-Options': 'nosniff',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'X-XSS-Protection': '1; mode=block',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
  'Content-Security-Policy': CSP_POLICY
}
```

### Content Security Policy

**Strict CSP** to prevent XSS and injection attacks:

```typescript
const CSP_POLICY = [
  "default-src 'self'",
  "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
  "style-src 'self' 'unsafe-inline'",
  "img-src 'self' data: https:",
  "font-src 'self'",
  "connect-src 'self'",
  "frame-ancestors 'none'",
  "base-uri 'self'",
  "form-action 'self'"
].join('; ')
```

### CORS Configuration

**Restrictive CORS** for API security:

```typescript
const CORS_CONFIG = {
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  maxAge: 86400 // 24 hours
}
```

## 🗄️ Database Security

### SQL Injection Prevention

**Prisma ORM** provides automatic protection:

```typescript
// Safe Parameterized Queries
const user = await db.user.findUnique({
  where: { email: userEmail },
  select: { id: true, email: true, password: true }
})

// Safe Dynamic Queries
const scans = await db.scan.findMany({
  where: {
    userId: authenticatedUserId,
    status: { in: statusFilters }
  },
  orderBy: { createdAt: 'desc' }
})
```

**Protection Features**:
- ✅ **Parameterized Queries**: Automatic SQL injection prevention
- ✅ **Type Safety**: Compile-time query validation
- ✅ **User Isolation**: Row-level security through user filtering
- ✅ **Connection Pooling**: Secure connection management

### Data Encryption

**Sensitive data protection**:

```typescript
const ENCRYPTION_CONFIG = {
  passwords: 'bcrypt',           // Bcrypt hashing
  tokens: 'HMAC-SHA256',        // JWT signing
  sessions: 'AES-256-GCM',      // Session encryption
  database: 'TLS 1.3'           // Connection encryption
}
```

## 🔍 Security Monitoring & Logging

### Error Tracking

**Comprehensive error monitoring** without exposing sensitive data:

```typescript
const ERROR_HANDLING = {
  logLevel: 'warn',
  sanitizeErrors: true,
  trackFailedLogins: true,
  alertOnSuspiciousActivity: true,
  maxErrorsPerMinute: 10
}
```

### Audit Trail

**Complete activity logging** for security analysis:

```typescript
const AUDIT_EVENTS = [
  'user_login',
  'user_logout', 
  'scan_created',
  'scan_completed',
  'asset_created',
  'password_changed',
  'failed_authentication'
]
```

## 🚨 Incident Response

### Security Event Detection

**Automated threat detection**:

- 🔍 **Brute Force Detection**: Multiple failed login attempts
- 🌐 **SSRF Attempts**: Private IP access attempts
- 📊 **Rate Limit Violations**: Excessive API usage
- 🔒 **Authentication Anomalies**: Unusual login patterns
- 💾 **Database Errors**: SQL injection attempts

### Response Procedures

**Automated security responses**:

1. **Rate Limiting**: Temporary IP blocking
2. **Account Lockout**: Suspicious activity prevention
3. **Alert Generation**: Security team notification
4. **Audit Logging**: Complete event documentation
5. **Error Sanitization**: Information disclosure prevention

## 🔧 Security Configuration

### Environment Variables

**Secure configuration management**:

```bash
# Authentication
JWT_SECRET="your-256-bit-secret-key"
BCRYPT_ROUNDS=12

# Database
DATABASE_URL="mysql://user:pass@host:port/db?sslmode=require"

# Security
ALLOWED_ORIGINS="https://yourdomain.com"
RATE_LIMIT_REDIS_URL="redis://localhost:6379"

# Monitoring
ERROR_TRACKING_DSN="your-error-tracking-url"
```

### Production Checklist

**Security deployment checklist**:

- [ ] **HTTPS Enabled**: SSL/TLS certificates configured
- [ ] **Environment Variables**: All secrets in environment
- [ ] **Database Encryption**: TLS connections enabled
- [ ] **Rate Limiting**: Redis backend configured
- [ ] **Error Monitoring**: Tracking service integrated
- [ ] **Security Headers**: All headers implemented
- [ ] **CORS Configuration**: Restrictive origin policy
- [ ] **Firewall Rules**: Network access controls
- [ ] **Regular Updates**: Dependencies and security patches

## 🔒 Compliance & Standards

### Security Standards

**Compliance with industry standards**:

- ✅ **OWASP Top 10**: Complete coverage
- ✅ **NIST Cybersecurity Framework**: Implementation aligned
- ✅ **ISO 27001**: Security management practices
- ✅ **SOC 2 Type II**: Security controls ready

### Data Protection

**Privacy and data protection**:

- 🔒 **Data Minimization**: Only necessary data collected
- 🗑️ **Right to Deletion**: User data removal capability
- 📊 **Data Portability**: Export functionality
- 🔐 **Encryption at Rest**: Database encryption
- 🌐 **Encryption in Transit**: TLS 1.3 connections

---

<div align="center">

**[⬅️ Back to Main Documentation](../README.md)**

</div>

'use client'

import { useState, useEffect, useMemo } from 'react'
import {
  Search,
  Filter,
  ExternalLink,
  Eye,
  Square,
  AlertTriangle,
  Download,
  FileText,
  Database,
  RefreshCw,
  Plus,
  MoreHorizontal
} from 'lucide-react'
import { <PERSON><PERSON><PERSON>r, PageHeader, DataTable } from '@/components/layout'
import { Button, Input, ScanStatusBadge, Alert, Card, CardContent, CardHeader, CardTitle, Badge } from '@/components/ui'
import { NoSSR } from '@/components/no-ssr'

import Link from 'next/link'

interface Scan {
  id: string
  targetUrl: string
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED'
  startedAt?: string
  completedAt?: string
  duration?: number
  totalVulns: number
  criticalVulns: number
  highVulns: number
  mediumVulns: number
  lowVulns: number
  infoVulns: number
  unknownVulns?: number
  createdAt: string
  asset?: {
    id: string
    domain: string
    title: string
  }
  _count: {
    vulnerabilities: number
  }
}

interface PaginationInfo {
  page: number
  limit: number
  total: number
  pages: number
}



export default function ScansPage() {
  const [scans, setScans] = useState<Scan[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [cancellingScans, setCancellingScans] = useState<Set<string>>(new Set())
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  })

  useEffect(() => {
    fetchScans()
  }, [pagination.page, statusFilter])

  // Auto-refresh every 3 seconds for running scans, 10 seconds otherwise
  useEffect(() => {
    const hasRunningScans = scans.some(scan => scan.status === 'RUNNING' || scan.status === 'PENDING')
    const refreshInterval = hasRunningScans ? 300000 : 1000000 // 3s if running scans, 10s otherwise

    const interval = setInterval(() => {
      fetchScans()
    }, refreshInterval)

    return () => clearInterval(interval)
  }, [scans])

  const fetchScans = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        // Add cache buster for real-time updates
        _t: Date.now().toString()
      })

      if (statusFilter) {
        params.append('status', statusFilter)
      }

      const response = await fetch(`/api/scans?${params}`, {
        // Disable caching for real-time updates
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache'
        }
      })
      if (response.ok) {
        const data = await response.json()
        setScans(data.scans)
        setPagination(data.pagination)

      } else {
        setError('Failed to fetch scans')
      }
    } catch (error) {
      setError('Failed to fetch scans')
      console.error('Error fetching scans:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleExport = async (format: 'json' | 'csv') => {
    try {
      // Build export URL with current filters
      const params = new URLSearchParams({
        format
      })

      if (statusFilter) {
        params.append('status', statusFilter)
      }

      const response = await fetch(`/api/export/scans?${params}`)

      if (!response.ok) {
        throw new Error('Export failed')
      }

      if (format === 'csv') {
        // For CSV, the response is already formatted as CSV content
        const csvContent = await response.text()
        const blob = new Blob([csvContent], { type: 'text/csv' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `scans-export-${new Date().toISOString().split('T')[0]}.csv`
        a.click()
        URL.revokeObjectURL(url)
      } else {
        // For JSON, get the data and create blob
        const data = await response.json()
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `scans-export-${new Date().toISOString().split('T')[0]}.json`
        a.click()
        URL.revokeObjectURL(url)
      }
    } catch (error) {
      console.error('Export failed:', error)
      // You might want to show a toast notification here
    }
  }

  const formatDuration = (seconds?: number) => {
    if (!seconds) return 'N/A'
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}m ${remainingSeconds}s`
  }

  const filteredScans = scans.filter(scan =>
    scan.targetUrl.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (scan.asset?.title && scan.asset.title.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (scan.asset?.domain && scan.asset.domain.toLowerCase().includes(searchTerm.toLowerCase()))
  )

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }))
  }

  const handleCancelScan = async (scanId: string) => {
    if (!confirm('Are you sure you want to cancel this scan? This action cannot be undone.')) {
      return
    }

    try {
      setCancellingScans(prev => new Set(prev).add(scanId))
      setError(null)

      const response = await fetch(`/api/scans/${scanId}/cancel`, {
        method: 'POST',
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to cancel scan')
      }

      // Update the scan in the local state
      setScans(prevScans =>
        prevScans.map(scan =>
          scan.id === scanId
            ? { ...scan, status: 'CANCELLED' as const }
            : scan
        )
      )

      // Show success message briefly
      setTimeout(() => {
        fetchScans() // Refresh the data
      }, 1000)

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to cancel scan')
    } finally {
      setCancellingScans(prev => {
        const newSet = new Set(prev)
        newSet.delete(scanId)
        return newSet
      })
    }
  }

  // Enhanced table columns with better visual indicators
  const columns = [
    {
      key: 'target',
      header: 'Target',
      className: 'w-1/3',
      render: (scan: Scan) => (
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <div className={`w-3 h-3 rounded-full ${
              scan.status === 'COMPLETED' ? 'bg-green-400' :
              scan.status === 'RUNNING' ? 'bg-blue-400 animate-pulse' :
              scan.status === 'FAILED' ? 'bg-red-400' :
              scan.status === 'PENDING' ? 'bg-yellow-400' :
              'bg-gray-400'
            }`} />
          </div>
          <div className="min-w-0 flex-1">
            <div className="text-sm font-medium text-gray-900 truncate">
              {scan.asset?.title || scan.targetUrl}
            </div>
            <div className="text-xs text-gray-500 truncate">
              {scan.asset?.domain || new URL(scan.targetUrl).hostname}
            </div>
            {scan.duration && (
              <div className="text-xs text-gray-400 mt-1">
                Duration: {formatDuration(scan.duration)}
              </div>
            )}
          </div>
        </div>
      ),
    },
    {
      key: 'status',
      header: 'Status',
      className: 'w-24',
      render: (scan: Scan) => (
        <div className="space-y-1">
          <ScanStatusBadge status={scan.status} size="sm" />
          {scan.startedAt && (
            <div className="text-xs text-gray-500">
              <NoSSR fallback="...">
                {new Date(scan.startedAt).toLocaleDateString()}
              </NoSSR>
            </div>
          )}
        </div>
      ),
    },
    {
      key: 'vulnerabilities',
      header: 'Vulnerabilities',
      className: 'w-1/4',
      render: (scan: Scan) => (
        <div className="space-y-2">
          <div className="flex items-center space-x-1">
            <span className="text-lg font-bold text-gray-900">{scan.totalVulns}</span>
            <span className="text-xs text-gray-500">total</span>
          </div>
          <div className="flex flex-wrap gap-1">
            {scan.criticalVulns > 0 && (
              <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 text-xs px-1 py-0">
                {scan.criticalVulns} Critical
              </Badge>
            )}
            {scan.highVulns > 0 && (
              <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200 text-xs px-1 py-0">
                {scan.highVulns} High
              </Badge>
            )}
            {scan.mediumVulns > 0 && (
              <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200 text-xs px-1 py-0">
                {scan.mediumVulns} Medium
              </Badge>
            )}
            {scan.lowVulns > 0 && (
              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 text-xs px-1 py-0">
                {scan.lowVulns} Low
              </Badge>
            )}
            {scan.infoVulns > 0 && (
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 text-xs px-1 py-0">
                {scan.infoVulns} Info
              </Badge>
            )}
          </div>
        </div>
      ),
    },
    {
      key: 'created',
      header: 'Created',
      className: 'w-32',
      render: (scan: Scan) => (
        <div className="text-sm">
          <div className="text-gray-900">
            <NoSSR fallback="Loading...">
              {new Date(scan.createdAt).toLocaleDateString()}
            </NoSSR>
          </div>
          <div className="text-xs text-gray-500">
            <NoSSR fallback="...">
              {new Date(scan.createdAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </NoSSR>
          </div>
        </div>
      ),
    },
    {
      key: 'actions',
      header: 'Actions',
      className: 'w-40',
      render: (scan: Scan) => (
        <div className="flex items-center space-x-1">
          <Link href={`/dashboard/scans/${scan.id}`}>
            <Button size="sm" variant="outline" className="text-xs px-2 py-1">
              <Eye className="h-3 w-3 mr-1" />
              View
            </Button>
          </Link>
          {scan.asset && (
            <Link href={`/dashboard/assets/${scan.asset.id}`}>
              <Button size="sm" variant="outline" className="text-xs px-2 py-1">
                <ExternalLink className="h-3 w-3 mr-1" />
                Asset
              </Button>
            </Link>
          )}
          {(scan.status === 'RUNNING' || scan.status === 'PENDING') && (
            <Button
              size="sm"
              variant="destructive"
              onClick={() => handleCancelScan(scan.id)}
              disabled={cancellingScans.has(scan.id)}
              className="bg-red-600 hover:bg-red-700 text-xs px-2 py-1"
            >
              {cancellingScans.has(scan.id) ? (
                <AlertTriangle className="h-3 w-3 animate-spin" />
              ) : (
                <Square className="h-3 w-3" />
              )}
            </Button>
          )}
        </div>
      ),
    },
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <PageContainer maxWidth="full" className="space-y-6">
        <PageHeader
          title="Vulnerability Scans"
          description="View and manage your security scans"
          actions={
            <div className="flex items-center space-x-3">
              <Button
                onClick={() => handleExport('json')}
                variant="outline"
                size="sm"
                className="bg-white/80 backdrop-blur-sm border-gray-200 hover:bg-white"
              >
                <Download className="h-4 w-4 mr-2" />
                Export JSON
              </Button>
              <Button
                onClick={() => handleExport('csv')}
                variant="outline"
                size="sm"
                className="bg-white/80 backdrop-blur-sm border-gray-200 hover:bg-white"
              >
                <FileText className="h-4 w-4 mr-2" />
                Export CSV
              </Button>
              <Link href="/dashboard/scan">
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="h-4 w-4 mr-2" />
                  New Scan
                </Button>
              </Link>
            </div>
          }
        />

        {error && (
          <Alert variant="error" className="border-0 shadow-lg bg-red-50/80 backdrop-blur-sm">
            {error}
          </Alert>
        )}

        {/* Enhanced Filters */}
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center text-lg font-semibold text-gray-900">
              <Filter className="h-5 w-5 mr-2 text-blue-500" />
              Filter & Search Scans
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Search Input */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search scans..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 bg-white border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                  />
                </div>
              </div>

              {/* Status Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Status</label>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full px-3 py-2 bg-white border border-gray-200 rounded-md focus:border-blue-500 focus:ring-blue-500 text-sm"
                >
                  <option value="">All Statuses</option>
                  <option value="PENDING">Pending</option>
                  <option value="RUNNING">Running</option>
                  <option value="COMPLETED">Completed</option>
                  <option value="FAILED">Failed</option>
                  <option value="CANCELLED">Cancelled</option>
                </select>
              </div>

              {/* Quick Filters */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Quick Filter</label>
                <div className="flex flex-wrap gap-2">
                  <Badge
                    variant="outline"
                    className="cursor-pointer hover:bg-blue-50 text-xs"
                    onClick={() => setStatusFilter('RUNNING')}
                  >
                    Active Scans
                  </Badge>
                  <Badge
                    variant="outline"
                    className="cursor-pointer hover:bg-green-50 text-xs"
                    onClick={() => setStatusFilter('COMPLETED')}
                  >
                    Completed
                  </Badge>
                  <Badge
                    variant="outline"
                    className="cursor-pointer hover:bg-red-50 text-xs"
                    onClick={() => setStatusFilter('FAILED')}
                  >
                    Failed
                  </Badge>
                </div>
              </div>

              {/* Actions */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Actions</label>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={fetchScans}
                    disabled={loading}
                    className="flex-1"
                  >
                    <RefreshCw className="h-4 w-4 mr-1" />
                    Refresh
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSearchTerm('')
                      setStatusFilter('')
                    }}
                    className="flex-1"
                  >
                    Clear
                  </Button>
                </div>
              </div>
            </div>

            {/* Filter Summary */}
            <div className="flex items-center justify-between pt-4 border-t border-gray-200">
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <span>
                  Showing {filteredScans.length} of {scans.length} scans
                </span>
                {searchTerm && (
                  <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                    Search: "{searchTerm}"
                  </Badge>
                )}
                {statusFilter && (
                  <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                    Status: {statusFilter}
                  </Badge>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Enhanced Scans Table */}
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center text-lg font-semibold text-gray-900">
              <Database className="h-5 w-5 mr-2 text-blue-500" />
              Scans (Total: {pagination.total})
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <DataTable
              data={filteredScans}
              columns={columns}
              loading={loading}
              emptyState={{
                title: 'No scans found',
                description: searchTerm || statusFilter
                  ? 'Try adjusting your filters to see more results.'
                  : 'Start by running your first vulnerability scan to see results here.',
                action: {
                  label: 'Start New Scan',
                  onClick: () => window.location.href = '/dashboard/scan'
                }
              }}
              pagination={{
                page: pagination.page,
                pages: pagination.pages,
                total: pagination.total,
                limit: pagination.limit,
                onPageChange: handlePageChange,
              }}

            />
          </CardContent>
        </Card>
      </PageContainer>
    </div>
  )
}

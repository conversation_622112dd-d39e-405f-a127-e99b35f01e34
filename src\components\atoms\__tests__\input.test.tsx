import { render, screen, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { Input } from '../input'
import { Search, Eye } from 'lucide-react'

describe('Input Component', () => {
  it('renders with default props', () => {
    render(<Input placeholder="Enter text..." />)
    const input = screen.getByPlaceholderText('Enter text...')
    expect(input).toBeInTheDocument()
    expect(input).toHaveClass('border-gray-300')
  })

  it('renders with left icon', () => {
    render(<Input leftIcon={<Search data-testid="search-icon" />} />)
    expect(screen.getByTestId('search-icon')).toBeInTheDocument()
    expect(screen.getByRole('textbox')).toHaveClass('pl-10')
  })

  it('renders with right icon', () => {
    render(<Input rightIcon={<Eye data-testid="eye-icon" />} />)
    expect(screen.getByTestId('eye-icon')).toBeInTheDocument()
    expect(screen.getByRole('textbox')).toHaveClass('pr-10')
  })

  it('renders with both left and right icons', () => {
    render(
      <Input 
        leftIcon={<Search data-testid="search-icon" />}
        rightIcon={<Eye data-testid="eye-icon" />}
      />
    )
    expect(screen.getByTestId('search-icon')).toBeInTheDocument()
    expect(screen.getByTestId('eye-icon')).toBeInTheDocument()
    expect(screen.getByRole('textbox')).toHaveClass('pl-10', 'pr-10')
  })

  it('shows error state correctly', () => {
    render(<Input error="This field is required" />)
    const input = screen.getByRole('textbox')
    expect(input).toHaveClass('border-red-500')
    expect(input).toHaveAttribute('aria-invalid', 'true')
  })

  it('handles user input', async () => {
    const user = userEvent.setup()
    const handleChange = jest.fn()
    
    render(<Input onChange={handleChange} />)
    const input = screen.getByRole('textbox')
    
    await user.type(input, 'Hello World')
    expect(input).toHaveValue('Hello World')
    expect(handleChange).toHaveBeenCalled()
  })

  it('supports different input types', () => {
    const { rerender } = render(<Input type="password" data-testid="test-input" />)
    expect(screen.getByTestId('test-input')).toHaveAttribute('type', 'password')

    rerender(<Input type="email" data-testid="test-input" />)
    expect(screen.getByRole('textbox')).toHaveAttribute('type', 'email')

    rerender(<Input type="number" data-testid="test-input" />)
    expect(screen.getByRole('spinbutton')).toHaveAttribute('type', 'number')
  })

  it('handles disabled state', () => {
    render(<Input disabled />)
    const input = screen.getByRole('textbox')
    expect(input).toBeDisabled()
    expect(input).toHaveClass('disabled:opacity-50', 'disabled:cursor-not-allowed')
  })

  it('forwards ref correctly', () => {
    const ref = jest.fn()
    render(<Input ref={ref} />)
    expect(ref).toHaveBeenCalled()
  })

  it('applies custom className', () => {
    render(<Input className="custom-input" />)
    expect(screen.getByRole('textbox')).toHaveClass('custom-input')
  })

  it('supports all HTML input attributes', () => {
    render(
      <Input 
        name="test-input"
        id="test-id"
        required
        maxLength={100}
        data-testid="test-input"
        aria-label="Test input"
      />
    )
    
    const input = screen.getByRole('textbox')
    expect(input).toHaveAttribute('name', 'test-input')
    expect(input).toHaveAttribute('id', 'test-id')
    expect(input).toHaveAttribute('required')
    expect(input).toHaveAttribute('maxlength', '100')
    expect(input).toHaveAttribute('data-testid', 'test-input')
    expect(input).toHaveAttribute('aria-label', 'Test input')
  })

  it('handles focus and blur events', async () => {
    const user = userEvent.setup()
    const handleFocus = jest.fn()
    const handleBlur = jest.fn()
    
    render(<Input onFocus={handleFocus} onBlur={handleBlur} />)
    const input = screen.getByRole('textbox')
    
    await user.click(input)
    expect(handleFocus).toHaveBeenCalled()
    
    await user.tab()
    expect(handleBlur).toHaveBeenCalled()
  })

  it('shows proper styling when focused', async () => {
    const user = userEvent.setup()
    render(<Input />)
    const input = screen.getByRole('textbox')
    
    await user.click(input)
    expect(input).toHaveFocus()
  })
})

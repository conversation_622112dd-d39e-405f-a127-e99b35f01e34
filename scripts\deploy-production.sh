#!/bin/bash

# CTB Scanner Production Deployment Script for Azure Ubuntu VM
# This script automates the deployment process for production environments

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="ctb-scanner"
APP_DIR="/opt/ctb-scanner"
SERVICE_USER="ctb-scanner"
NODE_VERSION="18"
BACKUP_DIR="/opt/backups/ctb-scanner"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root (use sudo)"
        exit 1
    fi
}

install_dependencies() {
    log_info "Installing system dependencies..."
    
    # Update package list
    apt update
    
    # Install required packages
    apt install -y \
        curl \
        wget \
        git \
        build-essential \
        nginx \
        mysql-client \
        supervisor \
        ufw \
        fail2ban \
        logrotate \
        htop \
        unzip
    
    log_success "System dependencies installed"
}

install_nodejs() {
    log_info "Installing Node.js ${NODE_VERSION}..."
    
    # Install Node.js using NodeSource repository
    curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION}.x | bash -
    apt install -y nodejs
    
    # Verify installation
    node_version=$(node --version)
    npm_version=$(npm --version)
    
    log_success "Node.js ${node_version} and npm ${npm_version} installed"
}

install_go() {
    log_info "Installing Go for Nuclei..."
    
    # Download and install Go
    GO_VERSION="1.21.5"
    wget -q https://go.dev/dl/go${GO_VERSION}.linux-amd64.tar.gz
    rm -rf /usr/local/go
    tar -C /usr/local -xzf go${GO_VERSION}.linux-amd64.tar.gz
    rm go${GO_VERSION}.linux-amd64.tar.gz
    
    # Add Go to PATH
    echo 'export PATH=$PATH:/usr/local/go/bin' >> /etc/profile
    export PATH=$PATH:/usr/local/go/bin
    
    log_success "Go ${GO_VERSION} installed"
}

install_nuclei() {
    log_info "Installing Nuclei scanner..."
    
    # Install Nuclei
    export PATH=$PATH:/usr/local/go/bin
    go install -v github.com/projectdiscovery/nuclei/v3/cmd/nuclei@latest
    
    # Move nuclei to system path
    cp /root/go/bin/nuclei /usr/local/bin/
    chmod +x /usr/local/bin/nuclei
    
    # Create nuclei user and directories
    useradd -r -s /bin/false nuclei || true
    mkdir -p /opt/nuclei-templates
    chown nuclei:nuclei /opt/nuclei-templates
    
    # Update templates
    sudo -u nuclei nuclei -update-templates -templates-directory /opt/nuclei-templates
    
    log_success "Nuclei scanner installed and templates updated"
}

create_app_user() {
    log_info "Creating application user..."
    
    # Create user if it doesn't exist
    if ! id "$SERVICE_USER" &>/dev/null; then
        useradd -r -s /bin/bash -d "$APP_DIR" -m "$SERVICE_USER"
        log_success "User $SERVICE_USER created"
    else
        log_warning "User $SERVICE_USER already exists"
    fi
}

setup_application() {
    log_info "Setting up application..."
    
    # Create application directory
    mkdir -p "$APP_DIR"
    mkdir -p "$BACKUP_DIR"
    
    # Set ownership
    chown -R "$SERVICE_USER:$SERVICE_USER" "$APP_DIR"
    chown -R "$SERVICE_USER:$SERVICE_USER" "$BACKUP_DIR"
    
    log_success "Application directories created"
}

configure_nginx() {
    log_info "Configuring Nginx..."
    
    # Create Nginx configuration
    cat > /etc/nginx/sites-available/$APP_NAME << 'EOF'
server {
    listen 80;
    server_name _;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy strict-origin-when-cross-origin;
    
    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
    
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
    
    location /api/auth/ {
        limit_req zone=login burst=5 nodelay;
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF
    
    # Enable site
    ln -sf /etc/nginx/sites-available/$APP_NAME /etc/nginx/sites-enabled/
    rm -f /etc/nginx/sites-enabled/default
    
    # Test configuration
    nginx -t
    
    log_success "Nginx configured"
}

configure_supervisor() {
    log_info "Configuring Supervisor..."
    
    # Create supervisor configuration
    cat > /etc/supervisor/conf.d/$APP_NAME.conf << EOF
[program:$APP_NAME]
command=npm start
directory=$APP_DIR
user=$SERVICE_USER
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/$APP_NAME.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
environment=NODE_ENV=production
EOF
    
    log_success "Supervisor configured"
}

configure_firewall() {
    log_info "Configuring firewall..."
    
    # Reset UFW
    ufw --force reset
    
    # Default policies
    ufw default deny incoming
    ufw default allow outgoing
    
    # Allow SSH
    ufw allow ssh
    
    # Allow HTTP and HTTPS
    ufw allow 80/tcp
    ufw allow 443/tcp
    
    # Allow MySQL (if needed)
    # ufw allow 3306/tcp
    
    # Enable firewall
    ufw --force enable
    
    log_success "Firewall configured"
}

configure_fail2ban() {
    log_info "Configuring Fail2Ban..."
    
    # Create jail configuration
    cat > /etc/fail2ban/jail.local << 'EOF'
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[sshd]
enabled = true

[nginx-http-auth]
enabled = true

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
action = iptables-multiport[name=ReqLimit, port="http,https", protocol=tcp]
logpath = /var/log/nginx/error.log
maxretry = 10
findtime = 600
bantime = 7200
EOF
    
    systemctl enable fail2ban
    systemctl restart fail2ban
    
    log_success "Fail2Ban configured"
}

setup_logging() {
    log_info "Setting up logging..."
    
    # Create log directory
    mkdir -p /var/log/$APP_NAME
    chown $SERVICE_USER:$SERVICE_USER /var/log/$APP_NAME
    
    # Create logrotate configuration
    cat > /etc/logrotate.d/$APP_NAME << EOF
/var/log/$APP_NAME/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $SERVICE_USER $SERVICE_USER
    postrotate
        supervisorctl restart $APP_NAME
    endscript
}
EOF
    
    log_success "Logging configured"
}

create_backup_script() {
    log_info "Creating backup script..."
    
    cat > /usr/local/bin/backup-$APP_NAME << 'EOF'
#!/bin/bash
# CTB Scanner Backup Script

BACKUP_DIR="/opt/backups/ctb-scanner"
DATE=$(date +%Y%m%d_%H%M%S)
APP_DIR="/opt/ctb-scanner"

# Create backup directory
mkdir -p "$BACKUP_DIR"

# Backup application files
tar -czf "$BACKUP_DIR/app_$DATE.tar.gz" -C "$APP_DIR" .

# Backup database (update with your credentials)
# mysqldump -u username -p password database_name > "$BACKUP_DIR/db_$DATE.sql"

# Keep only last 7 days of backups
find "$BACKUP_DIR" -name "*.tar.gz" -mtime +7 -delete
find "$BACKUP_DIR" -name "*.sql" -mtime +7 -delete

echo "Backup completed: $DATE"
EOF
    
    chmod +x /usr/local/bin/backup-$APP_NAME
    
    # Add to crontab
    (crontab -l 2>/dev/null; echo "0 2 * * * /usr/local/bin/backup-$APP_NAME") | crontab -
    
    log_success "Backup script created"
}

main() {
    log_info "Starting CTB Scanner production deployment..."
    
    check_root
    install_dependencies
    install_nodejs
    install_go
    install_nuclei
    create_app_user
    setup_application
    configure_nginx
    configure_supervisor
    configure_firewall
    configure_fail2ban
    setup_logging
    create_backup_script
    
    log_success "Production deployment setup completed!"
    log_info "Next steps:"
    echo "1. Copy your application code to $APP_DIR"
    echo "2. Create and configure .env file in $APP_DIR"
    echo "3. Run 'npm install' in $APP_DIR as $SERVICE_USER"
    echo "4. Run 'npm run build' in $APP_DIR as $SERVICE_USER"
    echo "5. Start services: systemctl restart nginx supervisor"
    echo "6. Check logs: tail -f /var/log/$APP_NAME.log"
}

# Run main function
main "$@"

# ⚙️ Environment Configuration

<div align="center">

[![Environment](https://img.shields.io/badge/Environment-Multi_Platform-success?style=for-the-badge&logo=docker)](https://docker.com/)
[![Node.js](https://img.shields.io/badge/Node.js-18+-339933?style=for-the-badge&logo=node.js)](https://nodejs.org/)
[![MySQL](https://img.shields.io/badge/MySQL-8.0-4479A1?style=for-the-badge&logo=mysql&logoColor=white)](https://www.mysql.com/)

*Complete environment setup and configuration guide for all deployment scenarios*

</div>

---

## 🌍 Environment Overview

CTB Scanner supports **multiple deployment environments** with flexible configuration management for development, staging, and production scenarios.

## 📋 Environment Variables

### 🔧 Core Configuration

Create a `.env` file in your project root:

```bash
# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DATABASE_URL="mysql://username:password@localhost:3306/database_name"

# Example configurations for different environments:
# Development: "mysql://root:rootroot@localhost:3306/ctb_dev"
# Production:  "mysql://ctb_user:<EMAIL>:3306/ctb_prod"
# Docker:      "mysql://root:rootroot@mysql:3306/ctb_scanner"

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production-256-bits-minimum"
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret-change-this-in-production"

# Production examples:
# JWT_SECRET="a-very-long-random-string-with-at-least-256-bits-of-entropy"
# NEXTAUTH_URL="https://yourdomain.com"
# NEXTAUTH_SECRET="another-very-long-random-string-for-nextauth"

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
NODE_ENV="development"  # development | production | test
PORT=3000
HOST="0.0.0.0"

# =============================================================================
# NUCLEI SCANNER CONFIGURATION
# =============================================================================
NUCLEI_PATH="/usr/local/bin/nuclei"  # Auto-detected if not specified
NUCLEI_TEMPLATES_PATH="/opt/nuclei-templates"  # Auto-detected if not specified
NUCLEI_CONFIG_PATH="/opt/nuclei-config"  # Optional custom config

# Platform-specific examples:
# Windows: NUCLEI_PATH="C:\\tools\\nuclei\\nuclei.exe"
# macOS:   NUCLEI_PATH="/opt/homebrew/bin/nuclei"
# Linux:   NUCLEI_PATH="/usr/local/bin/nuclei"

# =============================================================================
# RATE LIMITING & PERFORMANCE
# =============================================================================
REDIS_URL="redis://localhost:6379"  # Optional: for distributed rate limiting
MAX_CONCURRENT_SCANS=3
MAX_CONCURRENT_SCANS_PER_USER=1
SCAN_TIMEOUT_MINUTES=30

# =============================================================================
# LOGGING & MONITORING
# =============================================================================
LOG_LEVEL="info"  # error | warn | info | debug
LOG_FILE_PATH="./logs/app.log"
ERROR_TRACKING_DSN=""  # Optional: Sentry or similar service

# =============================================================================
# SECURITY HEADERS & CORS
# =============================================================================
ALLOWED_ORIGINS="http://localhost:3000,http://127.0.0.1:3000"
# Production: ALLOWED_ORIGINS="https://yourdomain.com,https://www.yourdomain.com"

TRUSTED_PROXIES=""  # Comma-separated list of trusted proxy IPs
# Example: TRUSTED_PROXIES="********,**********"

# =============================================================================
# FILE STORAGE & UPLOADS
# =============================================================================
UPLOAD_DIR="./uploads"
MAX_FILE_SIZE_MB=10
TEMP_DIR="./temp"

# =============================================================================
# EMAIL CONFIGURATION (Optional)
# =============================================================================
SMTP_HOST=""
SMTP_PORT=587
SMTP_USER=""
SMTP_PASS=""
SMTP_FROM="<EMAIL>"

# =============================================================================
# BACKUP & MAINTENANCE
# =============================================================================
BACKUP_ENABLED=false
BACKUP_SCHEDULE="0 2 * * *"  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
```

## 🏗️ Environment-Specific Configurations

### 🔧 Development Environment

**File**: `.env.development` or `.env.local`

```bash
# Development-optimized settings
NODE_ENV=development
DATABASE_URL="mysql://root:rootroot@localhost:3306/ctb_dev"
JWT_SECRET="dev-jwt-secret-not-for-production"
NEXTAUTH_URL="http://localhost:3000"
LOG_LEVEL=debug
REDIS_URL=""  # Use in-memory rate limiting for development
```

**Features**:
- 🐛 **Debug Logging**: Verbose logging for development
- 🔄 **Hot Reload**: Automatic code reloading
- 🚫 **No Redis Required**: In-memory rate limiting
- 📊 **Database Logging**: Query logging enabled
- 🔓 **Relaxed Security**: Development-friendly settings

### 🚀 Production Environment

**File**: `.env.production`

```bash
# Production-optimized settings
NODE_ENV=production
DATABASE_URL="mysql://ctb_user:${DB_PASSWORD}@db.example.com:3306/ctb_prod?sslmode=require"
JWT_SECRET="${JWT_SECRET_256_BIT}"
NEXTAUTH_URL="https://yourdomain.com"
NEXTAUTH_SECRET="${NEXTAUTH_SECRET_256_BIT}"
LOG_LEVEL=warn
REDIS_URL="redis://redis.example.com:6379"
ALLOWED_ORIGINS="https://yourdomain.com,https://www.yourdomain.com"
ERROR_TRACKING_DSN="${SENTRY_DSN}"
```

**Features**:
- 🔒 **Enhanced Security**: Strict security headers
- 📊 **Performance Monitoring**: Error tracking enabled
- 🚀 **Optimized Logging**: Warn level and above only
- 🔄 **Redis Backend**: Distributed rate limiting
- 🛡️ **SSL/TLS**: Encrypted database connections

### 🧪 Testing Environment

**File**: `.env.test`

```bash
# Test-optimized settings
NODE_ENV=test
DATABASE_URL="mysql://root:rootroot@localhost:3306/ctb_test"
JWT_SECRET="test-jwt-secret"
NEXTAUTH_URL="http://localhost:3000"
LOG_LEVEL=error
REDIS_URL=""  # Use in-memory for tests
MAX_CONCURRENT_SCANS=1
SCAN_TIMEOUT_MINUTES=5
```

**Features**:
- ⚡ **Fast Execution**: Minimal logging and timeouts
- 🔄 **Isolated Database**: Separate test database
- 🚫 **No External Dependencies**: Self-contained testing
- 📊 **Predictable Behavior**: Consistent test environment

## 🐳 Docker Configuration

### 📦 Docker Compose Setup

**File**: `docker-compose.yml`

```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=mysql://root:rootroot@mysql:3306/ctb_scanner
      - JWT_SECRET=${JWT_SECRET}
      - NEXTAUTH_URL=http://localhost:3000
      - REDIS_URL=redis://redis:6379
    depends_on:
      - mysql
      - redis
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs

  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=rootroot
      - MYSQL_DATABASE=ctb_scanner
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  mysql_data:
  redis_data:
```

### 🔧 Docker Environment Variables

**File**: `.env.docker`

```bash
# Docker-specific configuration
COMPOSE_PROJECT_NAME=ctb-scanner
JWT_SECRET=your-docker-jwt-secret-256-bits
MYSQL_ROOT_PASSWORD=rootroot
MYSQL_DATABASE=ctb_scanner
REDIS_PASSWORD=""
```

## ☁️ Cloud Platform Configurations

### 🌐 Azure Configuration

**Azure App Service Environment Variables**:

```bash
# Azure-specific settings
WEBSITES_PORT=3000
WEBSITE_NODE_DEFAULT_VERSION=18-lts
SCM_DO_BUILD_DURING_DEPLOYMENT=true

# Database (Azure Database for MySQL)
DATABASE_URL="mysql://ctbuser@ctbserver:${MYSQL_PASSWORD}@ctbserver.mysql.database.azure.com:3306/ctbscanner?ssl=true"

# Redis (Azure Cache for Redis)
REDIS_URL="rediss://:${REDIS_PASSWORD}@ctbcache.redis.cache.windows.net:6380"

# Application Insights
APPLICATIONINSIGHTS_CONNECTION_STRING="${APPINSIGHTS_CONNECTION_STRING}"
```

### 🚀 AWS Configuration

**AWS Elastic Beanstalk Environment Variables**:

```bash
# AWS-specific settings
PORT=8080
NODE_ENV=production

# RDS Database
DATABASE_URL="mysql://${RDS_USERNAME}:${RDS_PASSWORD}@${RDS_HOSTNAME}:${RDS_PORT}/${RDS_DB_NAME}"

# ElastiCache Redis
REDIS_URL="redis://${ELASTICACHE_ENDPOINT}:6379"

# CloudWatch Logging
AWS_REGION=us-east-1
LOG_GROUP_NAME=/aws/elasticbeanstalk/ctb-scanner
```

### 🌊 DigitalOcean Configuration

**DigitalOcean App Platform Environment Variables**:

```bash
# DigitalOcean-specific settings
DATABASE_URL="${DATABASE_URL}"  # Managed Database
REDIS_URL="${REDIS_URL}"        # Managed Redis

# App Platform specific
_SELF_URL="${APP_URL}"
```

## 🔧 Platform-Specific Setup

### 🪟 Windows Setup

**PowerShell Configuration**:

```powershell
# Set environment variables (PowerShell)
$env:NODE_ENV="development"
$env:DATABASE_URL="mysql://root:rootroot@localhost:3306/ctb_dev"
$env:NUCLEI_PATH="C:\tools\nuclei\nuclei.exe"

# Or use .env file with cross-env
npm install -g cross-env
cross-env NODE_ENV=development npm run dev
```

**Windows-specific paths**:
```bash
NUCLEI_PATH="C:\\tools\\nuclei\\nuclei.exe"
NUCLEI_TEMPLATES_PATH="C:\\nuclei-templates"
LOG_FILE_PATH="C:\\logs\\ctb-scanner.log"
UPLOAD_DIR="C:\\uploads"
TEMP_DIR="C:\\temp"
```

### 🍎 macOS Setup

**Bash/Zsh Configuration**:

```bash
# Add to ~/.bashrc or ~/.zshrc
export NODE_ENV=development
export DATABASE_URL="mysql://root:rootroot@localhost:3306/ctb_dev"
export NUCLEI_PATH="/opt/homebrew/bin/nuclei"
```

**macOS-specific paths**:
```bash
NUCLEI_PATH="/opt/homebrew/bin/nuclei"  # Homebrew
NUCLEI_TEMPLATES_PATH="/opt/homebrew/share/nuclei-templates"
LOG_FILE_PATH="/var/log/ctb-scanner.log"
```

### 🐧 Linux Setup

**Systemd Service Configuration**:

```bash
# /etc/systemd/system/ctb-scanner.service
[Unit]
Description=CTB Scanner Application
After=network.target mysql.service

[Service]
Type=simple
User=ctb
WorkingDirectory=/opt/ctb-scanner
Environment=NODE_ENV=production
Environment=DATABASE_URL=mysql://ctb_user:password@localhost:3306/ctb_prod
EnvironmentFile=/opt/ctb-scanner/.env
ExecStart=/usr/bin/node server.js
Restart=always

[Install]
WantedBy=multi-user.target
```

**Linux-specific paths**:
```bash
NUCLEI_PATH="/usr/local/bin/nuclei"
NUCLEI_TEMPLATES_PATH="/opt/nuclei-templates"
LOG_FILE_PATH="/var/log/ctb-scanner/app.log"
UPLOAD_DIR="/var/lib/ctb-scanner/uploads"
```

## 🔍 Environment Validation

### ✅ Configuration Checker

**Built-in environment validation**:

```typescript
// Environment validation on startup
const requiredEnvVars = [
  'DATABASE_URL',
  'JWT_SECRET',
  'NEXTAUTH_URL',
  'NEXTAUTH_SECRET'
]

const optionalEnvVars = [
  'NUCLEI_PATH',
  'REDIS_URL',
  'LOG_LEVEL',
  'ALLOWED_ORIGINS'
]

// Validation script
npm run validate-env
```

### 🧪 Environment Testing

**Test environment connectivity**:

```bash
# Test database connection
npm run test:db

# Test Nuclei installation
npm run test:nuclei

# Test Redis connection (if configured)
npm run test:redis

# Comprehensive environment test
npm run test:env
```

## 🔒 Security Best Practices

### 🔐 Secret Management

**Production secret management**:

1. **Never commit secrets** to version control
2. **Use environment variables** for all sensitive data
3. **Rotate secrets regularly** (JWT, database passwords)
4. **Use secret management services** (Azure Key Vault, AWS Secrets Manager)
5. **Implement least privilege** access controls

### 🛡️ Environment Isolation

**Secure environment separation**:

```bash
# Different databases per environment
DEV_DATABASE_URL="mysql://dev_user:dev_pass@dev-db:3306/ctb_dev"
STAGING_DATABASE_URL="mysql://staging_user:staging_pass@staging-db:3306/ctb_staging"
PROD_DATABASE_URL="mysql://prod_user:prod_pass@prod-db:3306/ctb_prod"

# Different JWT secrets per environment
DEV_JWT_SECRET="dev-secret-not-for-production"
STAGING_JWT_SECRET="staging-secret-256-bits"
PROD_JWT_SECRET="production-secret-256-bits"
```

## 📊 Monitoring & Logging

### 📈 Environment Monitoring

**Monitor environment health**:

```bash
# Environment-specific monitoring
LOG_LEVEL=info          # Development: debug, Production: warn
ERROR_TRACKING_DSN=""   # Sentry, Rollbar, etc.
METRICS_ENDPOINT=""     # Prometheus, DataDog, etc.
HEALTH_CHECK_INTERVAL=30000  # 30 seconds
```

### 📝 Logging Configuration

**Environment-specific logging**:

```bash
# Development
LOG_LEVEL=debug
LOG_FORMAT=pretty
LOG_FILE_ENABLED=false

# Production
LOG_LEVEL=warn
LOG_FORMAT=json
LOG_FILE_ENABLED=true
LOG_ROTATION_SIZE=100MB
LOG_RETENTION_DAYS=30
```

---

<div align="center">

**[⬅️ Back to Main Documentation](../README.md)**

</div>

#!/bin/bash

# CTB Scanner - Maintenance Script
# This script performs routine maintenance tasks for the CTB Scanner application

set -e  # Exit on any error

# Configuration
APP_DIR="/home/<USER>/ctb-scanner"
BACKUP_DIR="/home/<USER>/backups"
LOG_DIR="/var/log/ctb-scanner"
DB_NAME="ctb_scanner_prod"
DB_USER="ctb_user"
DB_PASS="secure_password"  # Change this!

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Check if running as correct user
check_user() {
    if [ "$USER" != "ctbscanner" ] && [ "$USER" != "root" ]; then
        error "This script should be run as 'ctbscanner' user or root"
        exit 1
    fi
}

# Create backup directory if it doesn't exist
setup_directories() {
    log "Setting up directories..."
    mkdir -p "$BACKUP_DIR"
    mkdir -p "$LOG_DIR"
    
    # Set proper permissions
    if [ "$USER" = "root" ]; then
        chown -R ctbscanner:ctbscanner "$BACKUP_DIR"
        chown -R ctbscanner:ctbscanner "$LOG_DIR"
    fi
}

# Database backup
backup_database() {
    log "Creating database backup..."
    
    local backup_file="$BACKUP_DIR/ctb_scanner_$(date +%Y%m%d_%H%M%S).sql"
    
    if mysqldump -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" > "$backup_file"; then
        gzip "$backup_file"
        log "Database backup created: ${backup_file}.gz"
        
        # Keep only last 7 days of backups
        find "$BACKUP_DIR" -name "*.sql.gz" -mtime +7 -delete
        log "Old backups cleaned up (kept last 7 days)"
    else
        error "Database backup failed"
        return 1
    fi
}

# Update Nuclei templates
update_nuclei_templates() {
    log "Updating Nuclei templates..."
    
    if command -v nuclei >/dev/null 2>&1; then
        if nuclei -update-templates -silent; then
            log "Nuclei templates updated successfully"
        else
            warning "Failed to update Nuclei templates"
        fi
    else
        warning "Nuclei not found in PATH"
    fi
}

# Clean up log files
cleanup_logs() {
    log "Cleaning up log files..."
    
    # Rotate logs if they're too large (>100MB)
    find "$LOG_DIR" -name "*.log" -size +100M -exec logrotate -f {} \;
    
    # Remove old compressed logs (older than 30 days)
    find "$LOG_DIR" -name "*.gz" -mtime +30 -delete
    
    # Clean PM2 logs
    if command -v pm2 >/dev/null 2>&1; then
        pm2 flush
        log "PM2 logs flushed"
    fi
    
    log "Log cleanup completed"
}

# Check disk space
check_disk_space() {
    log "Checking disk space..."
    
    local usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [ "$usage" -gt 80 ]; then
        warning "Disk usage is ${usage}% - consider cleaning up"
    else
        info "Disk usage: ${usage}% - OK"
    fi
}

# Check application health
check_app_health() {
    log "Checking application health..."
    
    local health_url="http://localhost:3000/api/health"
    local response=$(curl -s -o /dev/null -w "%{http_code}" "$health_url" 2>/dev/null || echo "000")
    
    if [ "$response" = "200" ]; then
        log "Application health check: OK"
    else
        error "Application health check failed (HTTP $response)"
        
        # Try to restart the application
        if command -v pm2 >/dev/null 2>&1; then
            warning "Attempting to restart application..."
            pm2 restart ctb-scanner
            sleep 10
            
            # Check again
            response=$(curl -s -o /dev/null -w "%{http_code}" "$health_url" 2>/dev/null || echo "000")
            if [ "$response" = "200" ]; then
                log "Application restarted successfully"
            else
                error "Application restart failed"
            fi
        fi
    fi
}

# Update system packages (if running as root)
update_system() {
    if [ "$USER" = "root" ]; then
        log "Updating system packages..."
        
        if command -v apt >/dev/null 2>&1; then
            apt update && apt upgrade -y
        elif command -v yum >/dev/null 2>&1; then
            yum update -y
        else
            warning "Unknown package manager - skipping system update"
        fi
        
        log "System packages updated"
    else
        info "Skipping system update (not running as root)"
    fi
}

# Check SSL certificate expiration
check_ssl_certificate() {
    log "Checking SSL certificate..."
    
    local domain="your-domain.com"  # Change this to your domain
    
    if command -v openssl >/dev/null 2>&1; then
        local expiry_date=$(echo | openssl s_client -servername "$domain" -connect "$domain:443" 2>/dev/null | openssl x509 -noout -dates | grep notAfter | cut -d= -f2)
        
        if [ -n "$expiry_date" ]; then
            local expiry_epoch=$(date -d "$expiry_date" +%s)
            local current_epoch=$(date +%s)
            local days_until_expiry=$(( (expiry_epoch - current_epoch) / 86400 ))
            
            if [ "$days_until_expiry" -lt 30 ]; then
                warning "SSL certificate expires in $days_until_expiry days"
            else
                info "SSL certificate expires in $days_until_expiry days - OK"
            fi
        else
            warning "Could not check SSL certificate expiration"
        fi
    else
        warning "OpenSSL not found - skipping SSL check"
    fi
}

# Generate maintenance report
generate_report() {
    log "Generating maintenance report..."
    
    local report_file="$LOG_DIR/maintenance_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "CTB Scanner Maintenance Report"
        echo "Generated: $(date)"
        echo "================================"
        echo
        echo "System Information:"
        echo "- Hostname: $(hostname)"
        echo "- OS: $(uname -a)"
        echo "- Uptime: $(uptime)"
        echo
        echo "Disk Usage:"
        df -h
        echo
        echo "Memory Usage:"
        free -h
        echo
        echo "Application Status:"
        if command -v pm2 >/dev/null 2>&1; then
            pm2 status
        else
            echo "PM2 not available"
        fi
        echo
        echo "Database Status:"
        if systemctl is-active --quiet mysql; then
            echo "MySQL: Active"
        else
            echo "MySQL: Inactive"
        fi
        echo
        echo "Recent Backups:"
        ls -la "$BACKUP_DIR"/*.gz 2>/dev/null | tail -5 || echo "No backups found"
        echo
        echo "Log Files:"
        ls -la "$LOG_DIR"/*.log 2>/dev/null | head -10 || echo "No log files found"
    } > "$report_file"
    
    log "Maintenance report saved: $report_file"
}

# Main maintenance function
run_maintenance() {
    log "Starting CTB Scanner maintenance..."
    
    setup_directories
    backup_database
    update_nuclei_templates
    cleanup_logs
    check_disk_space
    check_app_health
    check_ssl_certificate
    generate_report
    
    log "Maintenance completed successfully!"
}

# Help function
show_help() {
    echo "CTB Scanner Maintenance Script"
    echo
    echo "Usage: $0 [OPTION]"
    echo
    echo "Options:"
    echo "  --full          Run full maintenance (default)"
    echo "  --backup        Database backup only"
    echo "  --update        Update Nuclei templates only"
    echo "  --cleanup       Log cleanup only"
    echo "  --health        Health check only"
    echo "  --ssl           SSL certificate check only"
    echo "  --report        Generate report only"
    echo "  --help          Show this help message"
    echo
    echo "Examples:"
    echo "  $0                    # Run full maintenance"
    echo "  $0 --backup          # Backup database only"
    echo "  $0 --health          # Check application health"
}

# Parse command line arguments
case "${1:-}" in
    --backup)
        check_user
        setup_directories
        backup_database
        ;;
    --update)
        update_nuclei_templates
        ;;
    --cleanup)
        cleanup_logs
        ;;
    --health)
        check_app_health
        ;;
    --ssl)
        check_ssl_certificate
        ;;
    --report)
        setup_directories
        generate_report
        ;;
    --help)
        show_help
        ;;
    --full|"")
        check_user
        run_maintenance
        ;;
    *)
        error "Unknown option: $1"
        show_help
        exit 1
        ;;
esac

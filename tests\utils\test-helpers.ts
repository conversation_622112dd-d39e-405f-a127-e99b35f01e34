import { render, RenderOptions } from '@testing-library/react'
import { ReactElement } from 'react'

// Mock data generators
export const createMockUser = (overrides = {}) => ({
  id: 'user-123',
  firstName: 'John',
  lastName: 'Doe',
  companyName: 'Test Company',
  country: 'United States',
  email: '<EMAIL>',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides,
})

export const createMockAsset = (overrides = {}) => ({
  id: 'asset-123',
  url: 'https://example.com',
  domain: 'example.com',
  title: 'Example Website',
  description: 'A test website',
  technology: ['React', 'Node.js'],
  status: 'ACTIVE',
  lastScanned: new Date().toISOString(),
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  userId: 'user-123',
  ...overrides,
})

export const createMockScan = (overrides = {}) => ({
  id: 'scan-123',
  targetUrl: 'https://example.com',
  status: 'COMPLETED',
  startedAt: new Date().toISOString(),
  completedAt: new Date().toISOString(),
  duration: 120,
  totalVulns: 5,
  criticalVulns: 1,
  highVulns: 2,
  mediumVulns: 1,
  lowVulns: 1,
  infoVulns: 0,
  nucleiVersion: '2.9.4',
  templateCount: 100,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  userId: 'user-123',
  assetId: 'asset-123',
  ...overrides,
})

export const createMockVulnerability = (overrides = {}) => ({
  id: 'vuln-123',
  templateId: 'CVE-2023-1234',
  name: 'SQL Injection Vulnerability',
  severity: 'HIGH',
  description: 'A SQL injection vulnerability was found',
  reference: {
    cve: ['CVE-2023-1234'],
    cwe: ['CWE-89'],
  },
  tags: ['sqli', 'injection'],
  matcher: 'word',
  extractedResults: ['admin', 'password'],
  request: 'GET /admin HTTP/1.1\nHost: example.com',
  response: 'HTTP/1.1 200 OK\nContent-Type: text/html',
  curlCommand: 'curl -X GET https://example.com/admin',
  host: 'https://example.com',
  matchedAt: 'https://example.com/admin',
  timestamp: new Date().toISOString(),
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  scanId: 'scan-123',
  assetId: 'asset-123',
  ...overrides,
})

// API response helpers
export const createMockApiResponse = (data: any, pagination?: any) => ({
  data,
  pagination: pagination || {
    page: 1,
    pages: 1,
    total: Array.isArray(data) ? data.length : 1,
    limit: 10,
  },
})

export const createMockErrorResponse = (message: string, status = 400) => ({
  error: message,
  status,
})

// Test utilities
export const waitFor = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

export const mockFetch = (response: any, status = 200) => {
  global.fetch = jest.fn().mockResolvedValue({
    ok: status >= 200 && status < 300,
    status,
    json: jest.fn().mockResolvedValue(response),
    text: jest.fn().mockResolvedValue(JSON.stringify(response)),
  })
}

export const mockFetchError = (error: string) => {
  global.fetch = jest.fn().mockRejectedValue(new Error(error))
}

// Custom render function with providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return <>{children}</>
}

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options })

export * from '@testing-library/react'
export { customRender as render }

// Form testing helpers
export const fillForm = async (form: HTMLFormElement, data: Record<string, string>) => {
  const { fireEvent } = await import('@testing-library/react')
  
  Object.entries(data).forEach(([name, value]) => {
    const input = form.querySelector(`[name="${name}"]`) as HTMLInputElement
    if (input) {
      fireEvent.change(input, { target: { value } })
    }
  })
}

export const submitForm = async (form: HTMLFormElement) => {
  const { fireEvent } = await import('@testing-library/react')
  fireEvent.submit(form)
}

// Assertion helpers
export const expectToBeInDocument = (element: HTMLElement | null) => {
  expect(element).toBeInTheDocument()
}

export const expectToHaveClass = (element: HTMLElement, className: string) => {
  expect(element).toHaveClass(className)
}

export const expectToHaveAttribute = (element: HTMLElement, attribute: string, value?: string) => {
  if (value !== undefined) {
    expect(element).toHaveAttribute(attribute, value)
  } else {
    expect(element).toHaveAttribute(attribute)
  }
}

// Date helpers for testing
export const mockDate = (date: string | Date) => {
  const mockDate = new Date(date)
  jest.spyOn(global, 'Date').mockImplementation(() => mockDate as any)
  return mockDate
}

export const restoreDate = () => {
  jest.restoreAllMocks()
}

// Local storage helpers
export const mockLocalStorage = () => {
  const store: Record<string, string> = {}
  
  Object.defineProperty(window, 'localStorage', {
    value: {
      getItem: jest.fn((key: string) => store[key] || null),
      setItem: jest.fn((key: string, value: string) => {
        store[key] = value
      }),
      removeItem: jest.fn((key: string) => {
        delete store[key]
      }),
      clear: jest.fn(() => {
        Object.keys(store).forEach(key => delete store[key])
      }),
    },
    writable: true,
  })
}

// Console helpers
export const mockConsole = () => {
  const originalConsole = { ...console }
  
  console.log = jest.fn()
  console.warn = jest.fn()
  console.error = jest.fn()
  console.info = jest.fn()
  
  return {
    restore: () => {
      Object.assign(console, originalConsole)
    },
  }
}

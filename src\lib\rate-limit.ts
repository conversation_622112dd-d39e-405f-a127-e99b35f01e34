import { NextRequest } from 'next/server'

interface RateLimitConfig {
  interval: number // Time window in milliseconds
  uniqueTokenPerInterval: number // Max requests per interval
}

interface RateLimitStore {
  [key: string]: {
    count: number
    resetTime: number
  }
}

const store: RateLimitStore = {}

export function rateLimit(config: RateLimitConfig) {
  return {
    check: (request: NextRequest, limit: number = config.uniqueTokenPerInterval) => {
      const token = getIdentifier(request)
      const now = Date.now()
      const windowStart = now - config.interval

      // Clean up old entries
      Object.keys(store).forEach(key => {
        if (store[key].resetTime < windowStart) {
          delete store[key]
        }
      })

      const record = store[token]
      
      if (!record) {
        store[token] = {
          count: 1,
          resetTime: now + config.interval
        }
        return { success: true, remaining: limit - 1 }
      }

      if (record.resetTime < now) {
        // Reset the window
        store[token] = {
          count: 1,
          resetTime: now + config.interval
        }
        return { success: true, remaining: limit - 1 }
      }

      if (record.count >= limit) {
        return { 
          success: false, 
          remaining: 0,
          resetTime: record.resetTime
        }
      }

      record.count++
      return { 
        success: true, 
        remaining: limit - record.count 
      }
    }
  }
}

function getIdentifier(request: NextRequest): string {
  // Use IP address as identifier
  const forwarded = request.headers.get('x-forwarded-for')
  const ip = forwarded ? forwarded.split(',')[0] : request.ip || 'unknown'
  return ip
}

// Pre-configured rate limiters
export const authRateLimit = rateLimit({
  interval: 15 * 60 * 1000, // 15 minutes
  uniqueTokenPerInterval: 5, // 5 attempts per 15 minutes
})

export const generalRateLimit = rateLimit({
  interval: 60 * 1000, // 1 minute
  uniqueTokenPerInterval: 60, // 60 requests per minute
})

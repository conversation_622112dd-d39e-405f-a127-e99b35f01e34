#!/bin/bash

# CTB Scanner Deployment Script
# Production deployment automation for Azure Ubuntu VM

set -euo pipefail

# =============================================================================
# Configuration
# =============================================================================
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
BACKUP_DIR="/opt/ctb-scanner/backups"
LOG_FILE="/var/log/ctb-scanner/deploy.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# =============================================================================
# Logging Functions
# =============================================================================
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a "$LOG_FILE"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}" | tee -a "$LOG_FILE"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a "$LOG_FILE"
}

# =============================================================================
# Utility Functions
# =============================================================================
check_requirements() {
    log "Checking deployment requirements..."
    
    # Check if running as root or with sudo
    if [[ $EUID -eq 0 ]]; then
        warn "Running as root. Consider using a dedicated user for deployment."
    fi
    
    # Check required commands
    local required_commands=("docker" "docker-compose" "git" "curl" "mysql")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            error "Required command '$cmd' not found. Please install it first."
        fi
    done
    
    # Check Docker daemon
    if ! docker info &> /dev/null; then
        error "Docker daemon is not running. Please start Docker first."
    fi
    
    log "All requirements satisfied."
}

create_directories() {
    log "Creating required directories..."
    
    sudo mkdir -p /opt/ctb-scanner/{backups,logs,ssl,config}
    sudo mkdir -p /var/log/ctb-scanner
    sudo chown -R $USER:$USER /opt/ctb-scanner
    sudo chown -R $USER:$USER /var/log/ctb-scanner
    
    log "Directories created successfully."
}

backup_database() {
    log "Creating database backup..."
    
    local backup_file="$BACKUP_DIR/ctb_scanner_$(date +%Y%m%d_%H%M%S).sql"
    
    if docker-compose ps mysql | grep -q "Up"; then
        docker-compose exec -T mysql mysqldump \
            -u root -p"${MYSQL_ROOT_PASSWORD}" \
            --single-transaction \
            --routines \
            --triggers \
            ctb_scanner > "$backup_file"
        
        # Compress backup
        gzip "$backup_file"
        log "Database backup created: ${backup_file}.gz"
    else
        warn "MySQL container not running. Skipping database backup."
    fi
}

load_environment() {
    log "Loading environment configuration..."
    
    # Check if .env file exists
    if [[ ! -f "$PROJECT_DIR/.env" ]]; then
        if [[ -f "$PROJECT_DIR/.env.example" ]]; then
            warn ".env file not found. Copying from .env.example"
            cp "$PROJECT_DIR/.env.example" "$PROJECT_DIR/.env"
            error "Please configure .env file with your production settings before deploying."
        else
            error ".env file not found and no .env.example available."
        fi
    fi
    
    # Source environment variables
    set -a
    source "$PROJECT_DIR/.env"
    set +a
    
    # Validate required environment variables
    local required_vars=("MYSQL_ROOT_PASSWORD" "MYSQL_PASSWORD" "JWT_SECRET" "NEXTAUTH_SECRET")
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            error "Required environment variable '$var' is not set."
        fi
    done
    
    log "Environment configuration loaded."
}

build_application() {
    log "Building CTB Scanner application..."
    
    cd "$PROJECT_DIR"
    
    # Pull latest changes (if this is a git deployment)
    if [[ -d ".git" ]]; then
        info "Pulling latest changes from git..."
        git pull origin main || warn "Failed to pull latest changes. Continuing with current code."
    fi
    
    # Build Docker images
    docker-compose build --no-cache app
    
    log "Application built successfully."
}

deploy_application() {
    log "Deploying CTB Scanner application..."
    
    cd "$PROJECT_DIR"
    
    # Stop existing containers
    info "Stopping existing containers..."
    docker-compose down --remove-orphans
    
    # Start database first
    info "Starting database..."
    docker-compose up -d mysql redis
    
    # Wait for database to be ready
    info "Waiting for database to be ready..."
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if docker-compose exec -T mysql mysqladmin ping -h localhost -u root -p"${MYSQL_ROOT_PASSWORD}" &> /dev/null; then
            log "Database is ready."
            break
        fi
        
        if [[ $attempt -eq $max_attempts ]]; then
            error "Database failed to start after $max_attempts attempts."
        fi
        
        info "Waiting for database... (attempt $attempt/$max_attempts)"
        sleep 10
        ((attempt++))
    done
    
    # Run database migrations
    info "Running database migrations..."
    docker-compose run --rm app npx prisma migrate deploy
    
    # Start application
    info "Starting application..."
    docker-compose up -d app
    
    # Start nginx (if in production profile)
    if [[ "${DEPLOY_PROFILE:-}" == "production" ]]; then
        info "Starting nginx reverse proxy..."
        docker-compose --profile production up -d nginx
    fi
    
    log "Application deployed successfully."
}

health_check() {
    log "Performing health check..."
    
    local max_attempts=20
    local attempt=1
    local health_url="http://localhost:3000/api/health"
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f "$health_url" &> /dev/null; then
            log "Health check passed. Application is running."
            return 0
        fi
        
        if [[ $attempt -eq $max_attempts ]]; then
            error "Health check failed after $max_attempts attempts."
        fi
        
        info "Health check attempt $attempt/$max_attempts..."
        sleep 15
        ((attempt++))
    done
}

cleanup_old_images() {
    log "Cleaning up old Docker images..."
    
    # Remove dangling images
    docker image prune -f
    
    # Remove old CTB Scanner images (keep last 3)
    local old_images=$(docker images --format "table {{.Repository}}:{{.Tag}}\t{{.CreatedAt}}" | \
                      grep "ctb-scanner" | \
                      tail -n +4 | \
                      awk '{print $1}')
    
    if [[ -n "$old_images" ]]; then
        echo "$old_images" | xargs -r docker rmi
        log "Old images cleaned up."
    else
        log "No old images to clean up."
    fi
}

show_status() {
    log "Deployment Status:"
    echo "===================="
    docker-compose ps
    echo "===================="
    
    info "Application URL: http://localhost:3000"
    info "Health Check: http://localhost:3000/api/health"
    
    if docker-compose ps prometheus | grep -q "Up"; then
        info "Prometheus: http://localhost:9090"
    fi
    
    if docker-compose ps grafana | grep -q "Up"; then
        info "Grafana: http://localhost:3001"
    fi
}

# =============================================================================
# Main Deployment Function
# =============================================================================
main() {
    log "Starting CTB Scanner deployment..."
    
    # Create log directory if it doesn't exist
    sudo mkdir -p "$(dirname "$LOG_FILE")"
    sudo chown -R $USER:$USER "$(dirname "$LOG_FILE")"
    
    check_requirements
    create_directories
    load_environment
    
    # Create backup if database is running
    if docker-compose ps mysql | grep -q "Up"; then
        backup_database
    fi
    
    build_application
    deploy_application
    health_check
    cleanup_old_images
    show_status
    
    log "CTB Scanner deployment completed successfully!"
}

# =============================================================================
# Script Execution
# =============================================================================
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

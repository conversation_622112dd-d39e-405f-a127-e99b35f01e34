/**
 * Error Tracking API Endpoint
 * 
 * Provides access to error tracking data and statistics
 * for monitoring and debugging purposes.
 */

import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth'
import { handleApiError } from '@/lib/errors'
import { logger } from '@/lib/logger'
import { errorTracker, getErrorStats } from '@/lib/error-tracking'

/**
 * GET /api/errors
 * 
 * Returns error tracking statistics and recent errors
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Require authentication
    await requireAuth()

    const { searchParams } = new URL(request.url)
    const component = searchParams.get('component')
    const severity = searchParams.get('severity') as 'low' | 'medium' | 'high' | 'critical' | null
    const fingerprint = searchParams.get('fingerprint')

    logger.info('Error tracking data requested', {
      component: 'ERROR_TRACKING',
      filters: { component, severity, fingerprint }
    })

    // If specific error requested
    if (fingerprint) {
      const error = errorTracker.getError(fingerprint)
      if (!error) {
        return NextResponse.json(
          { error: 'Error not found' },
          { status: 404 }
        )
      }
      return NextResponse.json({ error })
    }

    // Get filtered errors
    let errors
    if (component) {
      errors = errorTracker.getErrorsByComponent(component)
    } else if (severity) {
      errors = errorTracker.getErrorsBySeverity(severity)
    } else {
      // Get general statistics
      const stats = getErrorStats()
      return NextResponse.json({
        stats,
        timestamp: new Date().toISOString()
      })
    }

    return NextResponse.json({
      errors,
      count: errors.length,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    logger.error('Failed to get error tracking data', error as Error, {
      component: 'ERROR_TRACKING'
    })
    return handleApiError(error)
  }
}

/**
 * POST /api/errors
 *
 * Manually track an error (for client-side error reporting)
 * Supports both single error and batch error reporting
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json()

    // Support both single error and batch errors
    const errors = body.errors || [{ error: body.error, context: body.context || {}, severity: body.severity || 'medium' }]
    const results: Array<{ fingerprint: string; success: boolean }> = []

    for (const errorData of errors) {
      const { error, context = {}, severity = 'medium' } = errorData

      // Validate required fields
      if (!error || !error.message) {
        results.push({ fingerprint: '', success: false })
        continue
      }

      // Create Error object
      const errorObj = new Error(error.message)
      errorObj.name = error.name || 'ClientError'
      errorObj.stack = error.stack

      // Add request context
      const requestContext = {
        ...context,
        userAgent: request.headers.get('user-agent') || undefined,
        ip: request.headers.get('x-forwarded-for') ||
            request.headers.get('x-real-ip') ||
            'unknown',
        url: request.url,
        method: 'POST'
      }

      // Track the error
      const fingerprint = errorTracker.trackError(errorObj, requestContext, severity)
      results.push({ fingerprint, success: true })

      logger.info('Client error tracked', {
        component: 'ERROR_TRACKING',
        fingerprint,
        severity,
        message: error.message
      })
    }

    return NextResponse.json({
      success: true,
      results,
      message: `${results.filter(r => r.success).length}/${results.length} errors tracked successfully`
    })

  } catch (error) {
    logger.error('Failed to track client error', error as Error, {
      component: 'ERROR_TRACKING'
    })
    return handleApiError(error)
  }
}

/**
 * DELETE /api/errors
 * 
 * Clear error tracking data (admin only)
 */
export async function DELETE(request: NextRequest): Promise<NextResponse> {
  try {
    // Require authentication
    const user = await requireAuth()

    // Only allow admins to clear error data
    // Note: You might want to add role-based access control here
    
    const { searchParams } = new URL(request.url)
    const confirm = searchParams.get('confirm')

    if (confirm !== 'true') {
      return NextResponse.json(
        { error: 'Confirmation required. Add ?confirm=true to clear all error data.' },
        { status: 400 }
      )
    }

    errorTracker.clear()

    logger.security('Error tracking data cleared', 'medium', {
      component: 'ERROR_TRACKING',
      userId: user.userId,
      action: 'CLEAR_ERRORS'
    })

    return NextResponse.json({
      success: true,
      message: 'Error tracking data cleared successfully'
    })

  } catch (error) {
    logger.error('Failed to clear error tracking data', error as Error, {
      component: 'ERROR_TRACKING'
    })
    return handleApiError(error)
  }
}

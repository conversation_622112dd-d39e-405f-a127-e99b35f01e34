import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { requireAuth } from '@/lib/auth'
import { handleApiError } from '@/lib/errors'

export async function GET(request: NextRequest) {
  try {
    // Authentication
    const currentUser = await requireAuth()

    // Get query parameters for filtering (but no pagination)
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const format = searchParams.get('format') || 'json'

    // Build where clause
    const where: any = {
      userId: currentUser.userId
    }

    if (status) {
      where.status = status.toUpperCase()
    }

    // Get ALL scans without pagination
    const scans = await db.scan.findMany({
      where,
      include: {
        asset: {
          select: {
            id: true,
            domain: true,
            title: true
          }
        },
        _count: {
          select: {
            vulnerabilities: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    const exportData = {
      scans,
      exportedAt: new Date().toISOString(),
      totalCount: scans.length,
      filters: {
        status
      }
    }

    if (format === 'csv') {
      const csvHeaders = ['ID', 'Target URL', 'Status', 'Total Vulnerabilities', 'Critical', 'High', 'Medium', 'Low', 'Info', 'Unknown', 'Created', 'Duration']
      const csvRows = scans.map(scan => [
        scan.id,
        scan.targetUrl,
        scan.status,
        scan.totalVulns,
        scan.criticalVulns,
        scan.highVulns,
        scan.mediumVulns,
        scan.lowVulns,
        scan.infoVulns,
        scan.unknownVulns || 0,
        new Date(scan.createdAt).toLocaleString(),
        scan.duration ? `${scan.duration}s` : 'N/A'
      ])

      const csvContent = [csvHeaders, ...csvRows]
        .map(row => row.map(field => `"${field}"`).join(','))
        .join('\n')

      return new NextResponse(csvContent, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="scans-export-${new Date().toISOString().split('T')[0]}.csv"`
        }
      })
    }

    // Default to JSON
    return NextResponse.json(exportData)

  } catch (error) {
    return handleApiError(error)
  }
}

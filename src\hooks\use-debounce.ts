import { useState, useEffect, useRef, useCallback } from 'react'

/**
 * Custom hook that debounces a value
 * @param value - The value to debounce
 * @param delay - The delay in milliseconds
 * @returns The debounced value
 */
export const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    // Handle zero delay case
    if (delay === 0) {
      setDebouncedValue(value)
      return
    }

    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

/**
 * Custom hook that debounces a callback function
 * @param callback - The callback function to debounce
 * @param delay - The delay in milliseconds
 * @param deps - Dependencies array for the callback
 * @returns The debounced callback function with cancel and flush methods
 */
export const useDebouncedCallback = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number,
  deps: React.DependencyList = []
) => {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const callbackRef = useRef(callback)
  const argsRef = useRef<Parameters<T> | undefined>()

  // Update callback ref when callback changes
  useEffect(() => {
    callbackRef.current = callback
  }, [callback, ...deps])

  const cancel = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
    }
  }, [])

  const flush = useCallback(() => {
    if (timeoutRef.current && argsRef.current) {
      cancel()
      callbackRef.current(...argsRef.current)
    }
  }, [cancel])

  const debouncedCallback = useCallback((...args: Parameters<T>) => {
    argsRef.current = args
    cancel()

    if (delay === 0) {
      callbackRef.current(...args)
      return
    }

    timeoutRef.current = setTimeout(() => {
      callbackRef.current(...args)
      timeoutRef.current = null
    }, delay)
  }, [delay, cancel]) as T & { cancel: () => void; flush: () => void }

  // Add cancel and flush methods to the debounced callback
  Object.assign(debouncedCallback, { cancel, flush })

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cancel()
    }
  }, [cancel])

  return debouncedCallback
}

// Application Constants

// Scan Types
export const SCAN_TYPES = {
  QUICK: 'quick',
  DEEP: 'deep',
  CUSTOM: 'custom'
} as const

// Vulnerability Severities
export const SEVERITIES = {
  CRITICAL: 'critical',
  HIGH: 'high',
  MEDIUM: 'medium',
  LOW: 'low',
  INFO: 'info',
  UNKNOWN: 'unknown'
} as const

// Scan Statuses
export const SCAN_STATUSES = {
  PENDING: 'PENDING',
  RUNNING: 'RUNNING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
  CANCELLED: 'CANCELLED'
} as const

// Asset Statuses
export const ASSET_STATUSES = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  ARCHIVED: 'ARCHIVED'
} as const

// API Routes
export const API_ROUTES = {
  AUTH: {
    LOGIN: '/api/auth/login',
    LOGOUT: '/api/auth/logout',
    REGISTER: '/api/auth/register'
  },
  SCANS: {
    LIST: '/api/scans',
    CREATE: '/api/scans',
    DETAIL: (id: string) => `/api/scans/${id}`,
    CANCEL: (id: string) => `/api/scans/${id}/cancel`,
    EXPORT: '/api/export/scans'
  },
  ASSETS: {
    LIST: '/api/assets',
    CREATE: '/api/assets',
    DETAIL: (id: string) => `/api/assets/${id}`,
    EXPORT: '/api/export/assets'
  },
  VULNERABILITIES: {
    LIST: '/api/vulnerabilities',
    DETAIL: (id: string) => `/api/vulnerabilities/${id}`,
    EXPORT: '/api/export/vulnerabilities'
  }
} as const

// UI Constants
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 10,
  PAGE_SIZE_OPTIONS: [10, 25, 50, 100]
} as const

// File Export Formats
export const EXPORT_FORMATS = {
  CSV: 'csv',
  JSON: 'json',
  PDF: 'pdf'
} as const

// Nuclei Template Categories
export const NUCLEI_CATEGORIES = {
  CVE: 'cve',
  PANEL: 'panel',
  XSS: 'xss',
  EXPOSURE: 'exposure',
  TECH: 'tech',
  HTTP: 'http',
  SSL: 'ssl',
  DNS: 'dns',
  WORDPRESS: 'wordpress',
  OSINT: 'osint',
  RCE: 'rce',
  LFI: 'lfi',
  AUTH_BYPASS: 'auth-bypass',
  JWT: 'jwt',
  OAUTH: 'oauth',
  SQLI: 'sqli',
  SSTI: 'ssti',
  SSRF: 'ssrf',
  XXE: 'xxe',
  CSRF: 'csrf',
  CORS: 'cors'
} as const

// Type exports
export type ScanType = typeof SCAN_TYPES[keyof typeof SCAN_TYPES]
export type Severity = typeof SEVERITIES[keyof typeof SEVERITIES]
export type ScanStatus = typeof SCAN_STATUSES[keyof typeof SCAN_STATUSES]
export type AssetStatus = typeof ASSET_STATUSES[keyof typeof ASSET_STATUSES]
export type ExportFormat = typeof EXPORT_FORMATS[keyof typeof EXPORT_FORMATS]

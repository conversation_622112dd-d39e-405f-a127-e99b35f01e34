'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/auth-context'
import { getInitials } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  Home,
  Settings,
  User,
  LogOut,
  Menu,
  X,
  Search,
  Shield,
  Database,
  Activity,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: Home },
  { name: '<PERSON>an', href: '/dashboard/scan', icon: Search },
  { name: 'Scans', href: '/dashboard/scans', icon: Activity },
  { name: 'Assets', href: '/dashboard/assets', icon: Database },
  { name: 'Vulnerabilities', href: '/dashboard/vulnerabilities', icon: Shield },
  { name: 'Profile', href: '/dashboard/profile', icon: User },
  { name: 'Setting<PERSON>', href: '/dashboard/settings', icon: Settings },
]

export function Sidebar() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [isCollapsed, setIsCollapsed] = useState(false)
  const { user, logout } = useAuth()
  const router = useRouter()

  const handleLogout = async () => {
    try {
      await logout()
      router.push('/login')
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  if (!user) return null

  return (
    <>
      {/* Mobile menu button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <Button
          variant="outline"
          size="icon"
          className="bg-white/90 backdrop-blur-sm border-gray-200/60 shadow-lg hover:shadow-xl transition-all duration-200"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        >
          {isMobileMenuOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
        </Button>
      </div>

      {/* Mobile menu overlay */}
      {isMobileMenuOpen && (
        <div 
          className="lg:hidden fixed inset-0 z-40 bg-black/60 backdrop-blur-sm"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`
        fixed inset-y-0 left-0 z-40 bg-white/95 backdrop-blur-xl border-r border-gray-200/60 shadow-xl transform transition-all duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0
        ${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'}
        ${isCollapsed ? 'lg:w-16' : 'lg:w-48'}
        w-64
      `}>
        <div className="flex flex-col h-full">
          {/* Header with Logo and Collapse Button */}
          <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200/60 bg-gradient-to-r from-gray-50/50 to-white/50">
            <div className={`flex items-center space-x-3 transition-all duration-300 ${isCollapsed ? 'lg:justify-center lg:space-x-0' : ''}`}>
              <div className="w-8 h-8 bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700
 rounded-lg flex items-center justify-center shadow-lg">
                <Shield className="h-4 w-4 text-white" />
              </div>
              <h1 className={`text-sm font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent transition-all duration-300 ${isCollapsed ? 'lg:hidden' : ''}`}>
                CTB Scanner
              </h1>
            </div>
            
            {/* Desktop collapse button */}
            <Button
              variant="ghost"
              size="icon"
              className={`hidden lg:flex h-8 w-8 rounded-lg hover:bg-gray-100/80 transition-all duration-200 ${isCollapsed ? 'lg:hidden' : ''}`}
              onClick={() => setIsCollapsed(!isCollapsed)}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
          </div>

          {/* Expand button for collapsed state */}
          {isCollapsed && (
            <div className="hidden lg:flex justify-center p-2">
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 rounded-lg hover:bg-gray-100/80 transition-all duration-200"
                onClick={() => setIsCollapsed(!isCollapsed)}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          )}

          {/* User Profile */}
          <div className={`p-4 border-b border-gray-200/60 bg-gradient-to-r from-gray-50/30 to-white/30 transition-all duration-300 ${isCollapsed ? 'lg:px-2' : ''}`}>
            <div className={`flex items-center space-x-3 transition-all duration-300 ${isCollapsed ? 'lg:justify-center lg:space-x-0' : ''}`}>
              <div className="flex-shrink-0 relative">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700
 rounded-xl flex items-center justify-center shadow-lg ring-2 ring-white/20">
                  <span className="text-sm font-semibold text-white">
                    {getInitials(user.firstName, user.lastName)}
                  </span>
                </div>
                <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white shadow-sm"></div>
              </div>
              <div className={`flex-1 min-w-0 transition-all duration-300 ${isCollapsed ? 'lg:hidden' : ''}`}>
                <p className="text-sm font-semibold text-gray-900 truncate">
                  {user.firstName} {user.lastName}
                </p>
                <p className="text-xs text-gray-500 truncate">
                  {user.email}
                </p>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-3 py-4 space-y-1 overflow-y-auto">
            {navigation.map((item) => {
              const Icon = item.icon
              return (
                <div key={item.name} className="relative group">
                  <Link
                    href={item.href}
                    className={`group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-gray-700 hover:bg-blue-200 transition-all duration-200 hover:shadow-sm active:scale-[0.98] ${isCollapsed ? 'lg:justify-center lg:px-3' : ''}`}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <Icon className={`h-5 w-5 text-gray-500 group-hover:text-blue-700 transition-colors duration-200 ${isCollapsed ? 'lg:mr-0' : 'mr-3'}`} />
                    <span className={`transition-all duration-300 ${isCollapsed ? 'lg:hidden' : ''}`}>
                      {item.name}
                    </span>
                    
                  </Link>
                </div>
              )
            })}
          </nav>

          {/* Logout Button */}
          <div className={`p-4 border-t border-gray-200/60 bg-gradient-to-r from-gray-50/30 to-white/30 transition-all duration-300 ${isCollapsed ? 'lg:px-2' : ''}`}>
            <div className="relative group">
              <Button
                variant="outline"
                className={`w-full justify-start text-sm font-medium border-gray-200/60 hover:border-red-200 hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 hover:text-red-700 transition-all duration-200 hover:shadow-sm active:scale-[0.98] ${isCollapsed ? 'lg:justify-center lg:px-3' : ''}`}
                onClick={handleLogout}
              >
                <LogOut className={`h-4 w-4 transition-colors duration-200 ${isCollapsed ? 'lg:mr-0' : 'mr-3'}`} />
                <span className={`transition-all duration-300 ${isCollapsed ? 'lg:hidden' : ''}`}>
                  Sign out
                </span>
                
                {/* Tooltip for collapsed state */}
                {isCollapsed && (
                  <div className="hidden lg:group-hover:block absolute left-full top-1/2 -translate-y-1/2 ml-2 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg shadow-lg whitespace-nowrap z-50 transition-all duration-200">
                    Sign out
                    <div className="absolute top-1/2 -left-1 -translate-y-1/2 w-2 h-2 bg-gray-900 rotate-45"></div>
                  </div>
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>

    </>
  )
}
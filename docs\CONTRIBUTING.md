# 🤝 Contributing Guide

<div align="center">

[![Contributors](https://img.shields.io/badge/Contributors-Welcome-brightgreen?style=for-the-badge&logo=github)](https://github.com/your-org/ctb-scanner/contributors)
[![Code of Conduct](https://img.shields.io/badge/Code_of_Conduct-Enforced-blue?style=for-the-badge&logo=github)](./CODE_OF_CONDUCT.md)
[![License](https://img.shields.io/badge/License-MIT-yellow?style=for-the-badge)](../LICENSE)

*Welcome to the CTB Scanner community! We're excited to have you contribute to our enterprise-grade vulnerability scanning platform.*

</div>

---

## 🌟 Welcome Contributors!

Thank you for your interest in contributing to **CTB Scanner**! This guide will help you get started with contributing to our codebase, whether you're fixing bugs, adding features, improving documentation, or enhancing tests.

## 🚀 Quick Start

### 1. 🍴 Fork & Clone

```bash
# Fork the repository on GitHub, then clone your fork
git clone https://github.com/YOUR_USERNAME/ctb-scanner.git
cd ctb-scanner

# Add the original repository as upstream
git remote add upstream https://github.com/original-org/ctb-scanner.git
```

### 2. 🔧 Setup Development Environment

```bash
# Install dependencies
npm install

# Copy environment configuration
cp .env.example .env.local
# Edit .env.local with your configuration

# Setup database
npx prisma migrate dev
npx prisma generate

# Start development server
npm run dev
```

### 3. 🌿 Create Feature Branch

```bash
# Create and switch to a new branch
git checkout -b feature/your-feature-name

# Or for bug fixes
git checkout -b fix/bug-description

# Or for documentation
git checkout -b docs/documentation-improvement
```

## 📋 Contribution Types

### 🐛 Bug Reports

**Before submitting a bug report:**
- Check existing issues to avoid duplicates
- Test with the latest version
- Gather relevant information

**Bug Report Template:**
```markdown
## Bug Description
A clear description of the bug.

## Steps to Reproduce
1. Go to '...'
2. Click on '...'
3. See error

## Expected Behavior
What you expected to happen.

## Actual Behavior
What actually happened.

## Environment
- OS: [e.g., Windows 10, macOS 12, Ubuntu 20.04]
- Node.js version: [e.g., 18.17.0]
- Browser: [e.g., Chrome 115, Firefox 116]
- CTB Scanner version: [e.g., 1.2.3]

## Additional Context
Screenshots, logs, or other relevant information.
```

### ✨ Feature Requests

**Feature Request Template:**
```markdown
## Feature Description
A clear description of the feature you'd like to see.

## Problem Statement
What problem does this feature solve?

## Proposed Solution
How would you like this feature to work?

## Alternatives Considered
Other solutions you've considered.

## Additional Context
Mockups, examples, or references.
```

### 🔧 Code Contributions

**Types of code contributions we welcome:**
- 🐛 **Bug fixes**
- ✨ **New features**
- 🎨 **UI/UX improvements**
- ⚡ **Performance optimizations**
- 🔒 **Security enhancements**
- 📚 **Documentation improvements**
- 🧪 **Test coverage improvements**

## 🏗️ Development Guidelines

### 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API routes
│   ├── dashboard/         # Dashboard pages
│   └── (auth)/           # Authentication pages
├── components/            # React components (Atomic Design)
│   ├── atoms/            # Basic building blocks
│   ├── molecules/        # Simple combinations
│   ├── organisms/        # Complex components
│   └── templates/        # Page layouts
├── lib/                  # Utility libraries
├── hooks/                # Custom React hooks
├── contexts/             # React contexts
└── types/                # TypeScript definitions
```

### 🎨 Coding Standards

**TypeScript Guidelines:**
```typescript
// ✅ Good: Use proper typing
interface ScanResult {
  id: string
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED'
  vulnerabilities: Vulnerability[]
}

// ✅ Good: Use descriptive names
const calculateVulnerabilitySeverityDistribution = (vulnerabilities: Vulnerability[]) => {
  // Implementation
}

// ❌ Avoid: Any types
const processData = (data: any) => {
  // Avoid this
}
```

**React Component Guidelines:**
```typescript
// ✅ Good: Functional components with proper typing
interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'destructive'
  size?: 'sm' | 'md' | 'lg'
  disabled?: boolean
  onClick?: () => void
  children: React.ReactNode
}

const Button: React.FC<ButtonProps> = ({ 
  variant = 'primary', 
  size = 'md', 
  disabled = false,
  onClick,
  children 
}) => {
  return (
    <button
      className={cn(buttonVariants({ variant, size }))}
      disabled={disabled}
      onClick={onClick}
    >
      {children}
    </button>
  )
}
```

**API Route Guidelines:**
```typescript
// ✅ Good: Proper error handling and validation
export async function POST(request: NextRequest) {
  try {
    // Validate input
    const body = await request.json()
    const validation = validateData(scanSchema, body)
    
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Validation failed', details: validation.errors },
        { status: 400 }
      )
    }

    // Process request
    const result = await processScanRequest(validation.data)
    
    return NextResponse.json(result, { status: 201 })
  } catch (error) {
    return handleApiError(error)
  }
}
```

### 🧪 Testing Requirements

**Test Coverage Requirements:**
- **Minimum 80% coverage** for new code
- **Unit tests** for all utility functions
- **Component tests** for React components
- **Integration tests** for API routes
- **E2E tests** for critical user flows

**Testing Examples:**
```typescript
// Unit test example
describe('calculateSeverityDistribution', () => {
  it('should correctly calculate vulnerability distribution', () => {
    const vulnerabilities = [
      { severity: 'CRITICAL' },
      { severity: 'HIGH' },
      { severity: 'HIGH' },
      { severity: 'MEDIUM' }
    ]
    
    const result = calculateSeverityDistribution(vulnerabilities)
    
    expect(result).toEqual({
      CRITICAL: 1,
      HIGH: 2,
      MEDIUM: 1,
      LOW: 0,
      INFO: 0
    })
  })
})

// Component test example
describe('Button Component', () => {
  it('should render with correct variant class', () => {
    render(<Button variant="destructive">Delete</Button>)
    
    const button = screen.getByRole('button', { name: /delete/i })
    expect(button).toHaveClass('bg-red-600')
  })
})
```

### 📝 Documentation Standards

**Code Documentation:**
```typescript
/**
 * Initiates a vulnerability scan for the specified target URL
 * 
 * @param targetUrl - The URL to scan for vulnerabilities
 * @param options - Scan configuration options
 * @returns Promise resolving to scan result with unique scan ID
 * 
 * @example
 * ```typescript
 * const result = await initiateScan('https://example.com', {
 *   severity: ['critical', 'high'],
 *   templates: ['cve-2023-*']
 * })
 * ```
 */
async function initiateScan(
  targetUrl: string, 
  options: ScanOptions
): Promise<ScanResult> {
  // Implementation
}
```

## 🔄 Pull Request Process

### 1. 📝 Before Submitting

**Pre-submission Checklist:**
- [ ] Code follows project style guidelines
- [ ] Self-review completed
- [ ] Tests added/updated and passing
- [ ] Documentation updated if needed
- [ ] No merge conflicts with main branch
- [ ] Commit messages are descriptive

### 2. 📋 Pull Request Template

```markdown
## Description
Brief description of changes made.

## Type of Change
- [ ] Bug fix (non-breaking change that fixes an issue)
- [ ] New feature (non-breaking change that adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] E2E tests pass (if applicable)
- [ ] Manual testing completed

## Screenshots (if applicable)
Add screenshots to help explain your changes.

## Checklist
- [ ] My code follows the style guidelines
- [ ] I have performed a self-review
- [ ] I have commented my code where necessary
- [ ] I have made corresponding changes to documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally
```

### 3. 🔍 Review Process

**What to expect:**
1. **Automated checks** run (tests, linting, type checking)
2. **Code review** by maintainers
3. **Feedback** and requested changes (if any)
4. **Approval** and merge

**Review criteria:**
- Code quality and style
- Test coverage
- Documentation completeness
- Performance impact
- Security considerations

## 🎯 Contribution Areas

### 🔧 Backend Development

**Areas needing contribution:**
- API endpoint optimization
- Database query performance
- Nuclei integration improvements
- Security enhancements
- Error handling improvements

### 🎨 Frontend Development

**Areas needing contribution:**
- UI/UX improvements
- Component library expansion
- Accessibility enhancements
- Mobile responsiveness
- Performance optimizations

### 🧪 Testing & Quality

**Areas needing contribution:**
- Test coverage improvements
- E2E test scenarios
- Performance testing
- Security testing
- Accessibility testing

### 📚 Documentation

**Areas needing contribution:**
- API documentation
- User guides
- Developer tutorials
- Code examples
- Video tutorials

## 🏆 Recognition

### 🌟 Contributor Levels

**Contributor Badges:**
- 🥉 **Bronze**: 1-5 merged PRs
- 🥈 **Silver**: 6-15 merged PRs
- 🥇 **Gold**: 16+ merged PRs
- 💎 **Diamond**: Significant feature contributions
- 🏆 **Maintainer**: Core team member

### 📊 Contribution Stats

Contributors are recognized in:
- README.md contributors section
- Release notes
- Annual contributor report
- Community Discord highlights

## 📞 Getting Help

### 💬 Communication Channels

- **GitHub Issues**: Bug reports and feature requests
- **GitHub Discussions**: General questions and ideas
- **Discord**: Real-time community chat
- **Email**: <EMAIL> (security issues only)

### 🆘 Support

**Need help with:**
- Setting up development environment
- Understanding codebase architecture
- Writing tests
- Submitting your first PR

**Reach out to:**
- Create a discussion on GitHub
- Ask in Discord #contributors channel
- Tag @maintainers in issues

## 📜 Code of Conduct

We are committed to providing a welcoming and inspiring community for all. Please read our [Code of Conduct](./CODE_OF_CONDUCT.md) before contributing.

**Key principles:**
- Be respectful and inclusive
- Welcome newcomers
- Focus on constructive feedback
- Maintain professional communication
- Report inappropriate behavior

## 📄 License

By contributing to CTB Scanner, you agree that your contributions will be licensed under the [MIT License](../LICENSE).

---

<div align="center">

**Thank you for contributing to CTB Scanner! 🎉**

**[⬅️ Back to Main Documentation](../README.md)**

</div>

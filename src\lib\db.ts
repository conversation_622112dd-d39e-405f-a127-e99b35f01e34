/**
 * Database Connection Configuration
 *
 * Configures and exports a singleton Prisma client instance for database
 * operations throughout the application. Implements connection pooling
 * and development-friendly logging.
 *
 * Features:
 * - Singleton pattern to prevent multiple connections
 * - Development query logging for debugging
 * - Production-optimized connection handling
 * - Global instance management for hot reloading
 *
 * Connection Details:
 * - Database: MySQL (configured via DATABASE_URL)
 * - ORM: Prisma Client with type safety
 * - Connection pooling: Automatic via Prisma
 * - Query logging: Enabled in development only
 *
 * Usage:
 * ```typescript
 * import { db } from '@/lib/db'
 *
 * const users = await db.user.findMany()
 * const scan = await db.scan.create({ data: {...} })
 * ```
 *
 * Security:
 * - SQL injection prevention via parameterized queries
 * - Type-safe database operations
 * - Connection string validation
 *
 * <AUTHOR> Scanner Team
 * @version 1.0.0
 */

import { PrismaClient } from '@prisma/client'

/**
 * Global Prisma instance type definition
 *
 * Extends the global object to include our Prisma client
 * for development hot reloading support.
 */
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

/**
 * Database client singleton instance
 *
 * Creates a single Prisma client instance that is reused across
 * the application. In development, stores the instance globally
 * to prevent multiple connections during hot reloading.
 */
export const db =
  globalForPrisma.prisma ??
  new PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['warn', 'error'] : ['error'],
    datasources: {
      db: {
        url: process.env.DATABASE_URL,
      },
    },
  })

// Performance monitoring and connection management
if (process.env.NODE_ENV !== 'production') {
  globalForPrisma.prisma = db

  // Note: Query performance monitoring can be added via Prisma middleware
  // or external monitoring tools in production
  console.log('� Database performance monitoring enabled in development')
}

// Graceful shutdown handling
process.on('beforeExit', async () => {
  console.log('🔌 Disconnecting from database...')
  await db.$disconnect()
})

process.on('SIGINT', async () => {
  console.log('🔌 Disconnecting from database...')
  await db.$disconnect()
  process.exit(0)
})

process.on('SIGTERM', async () => {
  console.log('🔌 Disconnecting from database...')
  await db.$disconnect()
  process.exit(0)
})

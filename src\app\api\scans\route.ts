/**
 * Scan Management API Routes
 *
 * This module handles all scan-related API operations including:
 * - Creating new vulnerability scans (POST)
 * - Retrieving scan history and details (GET)
 * - Managing scan lifecycle and status
 * - Rate limiting and user authentication
 *
 * Security Features:
 * - JWT authentication required for all operations
 * - Rate limiting: 200 requests per 15 minutes (handled by middleware)
 * - Input validation using Zod schemas
 * - URL validation and sanitization
 * - SQL injection prevention via Prisma ORM
 *
 * Scan Workflow:
 * 1. Validate user authentication and rate limits
 * 2. Validate and sanitize input URL and parameters
 * 3. Create or find existing asset
 * 4. Create scan record in database
 * 5. Add scan to background job queue
 * 6. Return scan details to client
 *
 * <AUTHOR> Scanner Team
 * @version 1.0.0
 */

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { requireAuth } from '@/lib/auth'
import { scanUrlSchema, validateData } from '@/lib/validations'
import { handleApiError, ValidationError } from '@/lib/errors'
import { jobQueue } from '@/lib/job-queue'
import { validateScanUrl, validateScanParameters, getScanRateLimit } from '@/lib/security'
import { initializeServices } from '@/lib/init'
import { timedApiCall, timedQuery } from '@/lib/performance'
import { caches, createCacheKey } from '@/lib/cache'
import { withErrorHandling, withDatabaseRecovery } from '@/lib/api-error-handler'

/**
 * POST /api/scans - Create a new vulnerability scan
 *
 * Creates a new scan request, validates the target URL, creates or finds
 * the associated asset, and queues the scan for background processing.
 *
 * @param {NextRequest} request - HTTP request containing scan parameters
 * @returns {Promise<NextResponse>} Scan details or error response
 */
const postHandler = async (request: NextRequest) => {
  return timedApiCall('scans_create', async () => {
    try {
    // ========================================================================
    // INITIALIZATION
    // ========================================================================

    // Ensure services are initialized with enhanced health checks
    try {
      initializeServices()
    } catch (error) {
      console.warn('⚠️ Standard initialization failed, attempting enhanced initialization...')
      const { initializeServicesEnhanced } = await import('@/lib/init')
      const result = await initializeServicesEnhanced({
        skipNucleiCheck: false,
        retries: 2
      })

      if (!result.success && result.errors.length > 0) {
        throw new Error(`Service initialization failed: ${result.errors.join(', ')}`)
      }
    }

    // ========================================================================
    // RATE LIMITING
    // ========================================================================

    // Rate limiting is handled by middleware for all /api/scans routes
    // No additional rate limiting needed here

    // ========================================================================
    // AUTHENTICATION
    // ========================================================================

    // Verify user authentication via JWT token
    const currentUser = await requireAuth()

    // ========================================================================
    // INPUT VALIDATION
    // ========================================================================

    // Parse and validate request body against schema
    const body = await request.json()
    const validation = validateData(scanUrlSchema, body)

    if (!validation.success) {
      throw new ValidationError('Invalid scan parameters')
    }

    const {
      url,
      target,
      severity,
      tags,
      templates,
      excludeTemplates,
      scanType,
      scanMode,
      inputType
    } = validation.data

    // Use target field if provided, otherwise fall back to url for backward compatibility
    const targetUrl = target || url

    if (!targetUrl) {
      throw new ValidationError('Target URL is required')
    }

    // Enhanced URL validation
    const urlValidation = validateScanUrl(targetUrl)
    if (!urlValidation.isValid) {
      throw new ValidationError(urlValidation.error || 'Invalid target')
    }

    // Validate scan parameters
    const paramValidation = validateScanParameters({ severity, tags, templates, excludeTemplates })
    if (!paramValidation.isValid) {
      throw new ValidationError(paramValidation.errors.join(', '))
    }

    // Check user scan rate limits
    const rateLimits = getScanRateLimit(currentUser.userId)

    // Check concurrent scans
    const runningScans = await db.scan.count({
      where: {
        userId: currentUser.userId,
        status: 'RUNNING'
      }
    })

    if (runningScans >= rateLimits.maxConcurrentScans) {
      return NextResponse.json(
        { error: `Maximum ${rateLimits.maxConcurrentScans} concurrent scans allowed` },
        { status: 429 }
      )
    }

    // Check hourly scan limit
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)
    const recentScans = await db.scan.count({
      where: {
        userId: currentUser.userId,
        createdAt: {
          gte: oneHourAgo
        }
      }
    })

    if (recentScans >= rateLimits.maxScansPerHour) {
      return NextResponse.json(
        { error: `Maximum ${rateLimits.maxScansPerHour} scans per hour allowed` },
        { status: 429 }
      )
    }

    // Use sanitized URL
    const sanitizedUrl = urlValidation.sanitizedUrl!
    const domain = new URL(sanitizedUrl).hostname

    // Check if asset exists, create if not
    let asset = await db.asset.findFirst({
      where: {
        userId: currentUser.userId,
        url: targetUrl
      }
    })

    if (!asset) {
      asset = await db.asset.create({
        data: {
          url: targetUrl,
          domain,
          userId: currentUser.userId,
          title: `Asset for ${domain}`,
          status: 'ACTIVE'
        }
      })
    }

    // Create scan record with options
    const scan = await db.scan.create({
      data: {
        targetUrl: targetUrl,
        status: 'PENDING',
        userId: currentUser.userId,
        assetId: asset.id,
        // Store scan configuration options
        severity: severity || undefined,
        tags: tags || undefined,
        templates: templates || undefined,
        excludeTemplates: excludeTemplates || undefined,
        scanType: scanType || 'web-api',
        scanMode: scanMode || 'basic',
        inputType: inputType || 'single'
      }
    })

    // Add scan to job queue
    await jobQueue.addScanJob({
      id: scan.id,
      scanId: scan.id,
      targetUrl: targetUrl,
      userId: currentUser.userId,
      assetId: asset.id,
      options: {
        severity,
        tags,
        templates,
        excludeTemplates,
        scanType,
        scanMode,
        inputType
      }
    })

    return NextResponse.json(
      {
        message: 'Scan initiated successfully',
        scanId: scan.id,
        status: 'PENDING'
      },
      {
        status: 201
      }
    )

    } catch (error) {
      return handleApiError(error)
    }
  }, { endpoint: '/api/scans', method: 'POST' })
}

// Export with conditional error handling
export const POST = process.env.NODE_ENV === 'production'
  ? withErrorHandling(postHandler)
  : postHandler

const getHandler = async (request: NextRequest) => {
  return timedApiCall('scans_list', async () => {
    try {
      // Authentication
      const currentUser = await requireAuth()

      // Get query parameters
      const { searchParams } = new URL(request.url)
      const page = parseInt(searchParams.get('page') || '1')
      const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 50)
      const status = searchParams.get('status')

      // Check if this is a real-time request (has cache buster)
      const isRealTimeRequest = searchParams.has('_t')

      // Create cache key for this query
      const cacheKey = createCacheKey('scans_list', currentUser.userId || 'unknown', page, limit, status || 'all')

      // Try to get from cache first (only if not real-time request)
      if (!isRealTimeRequest) {
        const cached = caches.scans.get(cacheKey)
        if (cached) {
          return NextResponse.json(cached)
        }
      }
    const offset = (page - 1) * limit

    // Build where clause
    const where: Record<string, unknown> = {
      userId: currentUser.userId
    }

    if (status) {
      where.status = status.toUpperCase()
    }

      // Get scans with pagination using timed query
      const [scans, total] = await timedQuery('scans_list_query', async () => {
        return Promise.all([
          db.scan.findMany({
            where,
            include: {
              asset: {
                select: {
                  id: true,
                  domain: true,
                  title: true
                }
              },
              _count: {
                select: {
                  vulnerabilities: true
                }
              }
            },
            orderBy: {
              createdAt: 'desc'
            },
            skip: offset,
            take: limit
          }),
          db.scan.count({ where })
        ])
      }, { userId: currentUser.userId, page: page.toString(), limit: limit.toString() })

      const result = {
        scans,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }

      // Cache the result for 5 minutes (only if not real-time request)
      if (!isRealTimeRequest) {
        caches.scans.set(cacheKey, result, 300000)
      }

      return NextResponse.json(result)

    } catch (error) {
      return handleApiError(error)
    }
  }, { endpoint: '/api/scans', method: 'GET' })
}

// Export with conditional error handling
export const GET = process.env.NODE_ENV === 'production'
  ? withErrorHandling(getHandler)
  : getHandler

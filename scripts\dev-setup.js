#!/usr/bin/env node

/**
 * CTB Scanner - Development Setup Script
 * 
 * This script helps developers set up the CTB Scanner application
 * for local development across different platforms (Windows, macOS, Linux).
 */

const fs = require('fs');
const path = require('path');
const { execSync, spawn } = require('child_process');
const os = require('os');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Utility functions
const log = (message) => console.log(`${colors.green}[INFO]${colors.reset} ${message}`);
const error = (message) => console.error(`${colors.red}[ERROR]${colors.reset} ${message}`);
const warning = (message) => console.warn(`${colors.yellow}[WARNING]${colors.reset} ${message}`);
const info = (message) => console.info(`${colors.blue}[INFO]${colors.reset} ${message}`);

// Check if command exists
function commandExists(command) {
  try {
    execSync(`${command} --version`, { stdio: 'ignore' });
    return true;
  } catch {
    return false;
  }
}

// Execute command with error handling
function executeCommand(command, description) {
  try {
    log(`${description}...`);
    execSync(command, { stdio: 'inherit' });
    return true;
  } catch (err) {
    error(`Failed to ${description.toLowerCase()}: ${err.message}`);
    return false;
  }
}

// Check Node.js version
function checkNodeVersion() {
  log('Checking Node.js version...');
  
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  
  if (majorVersion >= 18) {
    info(`Node.js version: ${nodeVersion} ✓`);
    return true;
  } else {
    error(`Node.js version ${nodeVersion} is not supported. Please install Node.js 18 or later.`);
    return false;
  }
}

// Check npm version
function checkNpmVersion() {
  log('Checking npm version...');
  
  try {
    const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
    info(`npm version: ${npmVersion} ✓`);
    return true;
  } catch {
    error('npm is not installed or not in PATH');
    return false;
  }
}

// Check if MySQL is running
function checkMysqlConnection() {
  log('Checking MySQL connection...');
  
  // Try to connect to MySQL using the default development credentials
  const testCommand = os.platform() === 'win32' 
    ? 'mysql -u root -prootroot -e "SELECT 1;" 2>nul'
    : 'mysql -u root -prootroot -e "SELECT 1;" 2>/dev/null';
  
  try {
    execSync(testCommand, { stdio: 'ignore' });
    info('MySQL connection: OK ✓');
    return true;
  } catch {
    warning('MySQL connection failed. Please ensure MySQL is running and accessible.');
    info('Default credentials: root/rootroot');
    info('You can also use Docker: docker run --name mysql-ctb -e MYSQL_ROOT_PASSWORD=rootroot -p 3306:3306 -d mysql:8.0');
    return false;
  }
}

// Check Nuclei installation
function checkNucleiInstallation() {
  log('Checking Nuclei installation...');
  
  if (commandExists('nuclei')) {
    try {
      const nucleiVersion = execSync('nuclei -version', { encoding: 'utf8' }).trim();
      info(`Nuclei version: ${nucleiVersion} ✓`);
      return true;
    } catch {
      warning('Nuclei is installed but version check failed');
      return false;
    }
  } else {
    warning('Nuclei is not installed or not in PATH');
    info('Please install Nuclei from: https://github.com/projectdiscovery/nuclei');
    return false;
  }
}

// Setup environment file
function setupEnvironmentFile() {
  log('Setting up environment file...');
  
  const envPath = '.env';
  const envExamplePath = '.env.example';
  
  if (fs.existsSync(envPath)) {
    info('.env file already exists ✓');
    return true;
  }
  
  if (fs.existsSync(envExamplePath)) {
    try {
      fs.copyFileSync(envExamplePath, envPath);
      info('.env file created from template ✓');
      warning('Please review and update the .env file with your settings');
      return true;
    } catch (err) {
      error(`Failed to copy .env.example to .env: ${err.message}`);
      return false;
    }
  } else {
    error('.env.example file not found');
    return false;
  }
}

// Install dependencies
function installDependencies() {
  log('Installing dependencies...');
  
  if (!executeCommand('npm ci', 'Install dependencies')) {
    // Fallback to npm install
    return executeCommand('npm install', 'Install dependencies (fallback)');
  }
  
  return true;
}

// Setup database
function setupDatabase() {
  log('Setting up database...');
  
  // Check if Prisma is available
  if (!commandExists('npx prisma')) {
    error('Prisma CLI not available');
    return false;
  }
  
  // Generate Prisma client
  if (!executeCommand('npx prisma generate', 'Generate Prisma client')) {
    return false;
  }
  
  // Push database schema
  if (!executeCommand('npx prisma db push', 'Push database schema')) {
    warning('Database schema push failed. You may need to create the database manually.');
    info('Run: CREATE DATABASE ctb_scanner CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;');
    return false;
  }
  
  return true;
}

// Update Nuclei templates
function updateNucleiTemplates() {
  if (commandExists('nuclei')) {
    log('Updating Nuclei templates...');
    return executeCommand('nuclei -update-templates', 'Update Nuclei templates');
  } else {
    warning('Skipping Nuclei template update (Nuclei not installed)');
    return true;
  }
}

// Run tests
function runTests() {
  log('Running tests...');
  
  try {
    execSync('npm test', { stdio: 'inherit' });
    info('All tests passed ✓');
    return true;
  } catch {
    warning('Some tests failed. This is normal for a fresh setup.');
    return true; // Don't fail setup for test failures
  }
}

// Build application
function buildApplication() {
  log('Building application...');
  return executeCommand('npm run build', 'Build application');
}

// Display platform-specific instructions
function displayPlatformInstructions() {
  const platform = os.platform();
  
  info('\n=== Platform-specific Setup Instructions ===');
  
  switch (platform) {
    case 'win32':
      info('Windows Setup:');
      info('1. Install Node.js from: https://nodejs.org/');
      info('2. Install MySQL from: https://dev.mysql.com/downloads/installer/');
      info('3. Install Nuclei: Download from GitHub releases and add to PATH');
      info('4. Consider using Docker Desktop for easier MySQL setup');
      break;
      
    case 'darwin':
      info('macOS Setup:');
      info('1. Install Homebrew: /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"');
      info('2. Install Node.js: brew install node@18');
      info('3. Install MySQL: brew install mysql');
      info('4. Install Nuclei: brew install nuclei');
      break;
      
    case 'linux':
      info('Linux Setup:');
      info('1. Install Node.js: curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash - && sudo apt install -y nodejs');
      info('2. Install MySQL: sudo apt install -y mysql-server');
      info('3. Install Nuclei: Download from GitHub releases');
      info('4. Consider using Docker for easier setup');
      break;
      
    default:
      info('Platform-specific instructions not available for your OS');
  }
}

// Main setup function
async function runSetup() {
  console.log(`${colors.cyan}
╔══════════════════════════════════════════════════════════════╗
║                    CTB Scanner Dev Setup                     ║
║                                                              ║
║  This script will help you set up CTB Scanner for           ║
║  local development on your machine.                         ║
╚══════════════════════════════════════════════════════════════╝
${colors.reset}`);

  const checks = [
    { name: 'Node.js version', fn: checkNodeVersion },
    { name: 'npm version', fn: checkNpmVersion },
    { name: 'MySQL connection', fn: checkMysqlConnection },
    { name: 'Nuclei installation', fn: checkNucleiInstallation }
  ];

  const setup = [
    { name: 'Environment file', fn: setupEnvironmentFile },
    { name: 'Dependencies', fn: installDependencies },
    { name: 'Database', fn: setupDatabase },
    { name: 'Nuclei templates', fn: updateNucleiTemplates },
    { name: 'Tests', fn: runTests },
    { name: 'Build', fn: buildApplication }
  ];

  // Run prerequisite checks
  info('\n=== Prerequisite Checks ===');
  let allChecksPassed = true;
  
  for (const check of checks) {
    if (!check.fn()) {
      allChecksPassed = false;
    }
  }

  if (!allChecksPassed) {
    warning('\nSome prerequisite checks failed. Please address the issues above before continuing.');
    displayPlatformInstructions();
    process.exit(1);
  }

  // Run setup steps
  info('\n=== Setup Steps ===');
  let setupSuccess = true;
  
  for (const step of setup) {
    if (!step.fn()) {
      setupSuccess = false;
      error(`Setup step failed: ${step.name}`);
    }
  }

  // Final status
  console.log(`\n${colors.cyan}=== Setup Complete ===${colors.reset}`);
  
  if (setupSuccess) {
    console.log(`${colors.green}
✅ CTB Scanner development environment is ready!

Next steps:
1. Review and update .env file if needed
2. Start development server: npm run dev
3. Open http://localhost:3000 in your browser
4. Check the documentation in docs/ directory

Happy coding! 🚀
${colors.reset}`);
  } else {
    console.log(`${colors.yellow}
⚠️  Setup completed with some issues.

Please review the errors above and fix them manually.
Check the troubleshooting guide: docs/TROUBLESHOOTING.md

You can re-run this script after fixing the issues.
${colors.reset}`);
  }
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
  console.log(`
CTB Scanner Development Setup Script

Usage: node scripts/dev-setup.js [options]

Options:
  --help, -h     Show this help message
  --check-only   Run prerequisite checks only
  --no-build     Skip the build step
  --no-test      Skip running tests

Examples:
  node scripts/dev-setup.js              # Full setup
  node scripts/dev-setup.js --check-only # Check prerequisites only
  node scripts/dev-setup.js --no-build   # Setup without building
`);
  process.exit(0);
}

if (args.includes('--check-only')) {
  // Run checks only
  info('Running prerequisite checks only...');
  
  const checks = [
    { name: 'Node.js version', fn: checkNodeVersion },
    { name: 'npm version', fn: checkNpmVersion },
    { name: 'MySQL connection', fn: checkMysqlConnection },
    { name: 'Nuclei installation', fn: checkNucleiInstallation }
  ];
  
  let allPassed = true;
  for (const check of checks) {
    if (!check.fn()) {
      allPassed = false;
    }
  }
  
  if (allPassed) {
    info('All prerequisite checks passed! ✓');
  } else {
    warning('Some checks failed. See messages above.');
    displayPlatformInstructions();
  }
  
  process.exit(allPassed ? 0 : 1);
}

// Run the main setup
runSetup().catch(err => {
  error(`Setup failed: ${err.message}`);
  process.exit(1);
});

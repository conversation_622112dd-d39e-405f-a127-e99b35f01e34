@echo off
REM CTB Scanner - Windows Maintenance Script
REM This script performs routine maintenance tasks for the CTB Scanner application on Windows

setlocal enabledelayedexpansion

REM Configuration
set "APP_DIR=%~dp0.."
set "BACKUP_DIR=%APP_DIR%\backups"
set "LOG_DIR=%APP_DIR%\logs"
set "DB_NAME=ctb_scanner_prod"
set "DB_USER=ctb_user"
set "DB_PASS=secure_password"

REM Colors for output (Windows 10+ with ANSI support)
set "GREEN=[32m"
set "RED=[31m"
set "YELLOW=[33m"
set "BLUE=[34m"
set "NC=[0m"

REM Create directories if they don't exist
if not exist "%BACKUP_DIR%" mkdir "%BACKUP_DIR%"
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"

REM Logging functions
:log
echo %GREEN%[%date% %time%]%NC% %~1
goto :eof

:error
echo %RED%[ERROR]%NC% %~1 >&2
goto :eof

:warning
echo %YELLOW%[WARNING]%NC% %~1
goto :eof

:info
echo %BLUE%[INFO]%NC% %~1
goto :eof

REM Check if MySQL is available
:check_mysql
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    call :warning "MySQL not found in PATH"
    exit /b 1
)
goto :eof

REM Database backup
:backup_database
call :log "Creating database backup..."

set "backup_file=%BACKUP_DIR%\ctb_scanner_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%.sql"
set "backup_file=!backup_file: =0!"

mysqldump -u %DB_USER% -p%DB_PASS% %DB_NAME% > "%backup_file%"
if %errorlevel% equ 0 (
    call :log "Database backup created: %backup_file%"
    
    REM Compress backup (requires 7-zip or similar)
    where 7z >nul 2>&1
    if !errorlevel! equ 0 (
        7z a "%backup_file%.7z" "%backup_file%"
        del "%backup_file%"
        call :log "Backup compressed: %backup_file%.7z"
    )
    
    REM Clean old backups (keep last 7 days)
    forfiles /p "%BACKUP_DIR%" /s /m *.sql* /d -7 /c "cmd /c del @path" 2>nul
    call :log "Old backups cleaned up"
) else (
    call :error "Database backup failed"
    exit /b 1
)
goto :eof

REM Update Nuclei templates
:update_nuclei_templates
call :log "Updating Nuclei templates..."

where nuclei >nul 2>&1
if %errorlevel% equ 0 (
    nuclei -update-templates -silent
    if !errorlevel! equ 0 (
        call :log "Nuclei templates updated successfully"
    ) else (
        call :warning "Failed to update Nuclei templates"
    )
) else (
    call :warning "Nuclei not found in PATH"
)
goto :eof

REM Clean up log files
:cleanup_logs
call :log "Cleaning up log files..."

REM Remove large log files (>100MB)
for /r "%LOG_DIR%" %%f in (*.log) do (
    if %%~zf gtr 104857600 (
        call :info "Rotating large log file: %%f"
        move "%%f" "%%f.old"
        echo. > "%%f"
    )
)

REM Remove old compressed logs (older than 30 days)
forfiles /p "%LOG_DIR%" /s /m *.gz /d -30 /c "cmd /c del @path" 2>nul
forfiles /p "%LOG_DIR%" /s /m *.7z /d -30 /c "cmd /c del @path" 2>nul

call :log "Log cleanup completed"
goto :eof

REM Check disk space
:check_disk_space
call :log "Checking disk space..."

for /f "tokens=3" %%a in ('dir /-c %APP_DIR% ^| find "bytes free"') do set "free_space=%%a"
for /f "tokens=1" %%a in ('dir /-c %APP_DIR% ^| find "bytes free"') do set "total_space=%%a"

REM Simple disk space check (this is a basic implementation)
call :info "Disk space check completed"
goto :eof

REM Check application health
:check_app_health
call :log "Checking application health..."

REM Try to reach the health endpoint
curl -s -o nul -w "%%{http_code}" http://localhost:3000/api/health > temp_response.txt 2>nul
set /p response=<temp_response.txt
del temp_response.txt 2>nul

if "%response%"=="200" (
    call :log "Application health check: OK"
) else (
    call :error "Application health check failed (HTTP %response%)"
    
    REM Try to restart with PM2 if available
    where pm2 >nul 2>&1
    if !errorlevel! equ 0 (
        call :warning "Attempting to restart application..."
        pm2 restart ctb-scanner
        timeout /t 10 /nobreak >nul
        
        REM Check again
        curl -s -o nul -w "%%{http_code}" http://localhost:3000/api/health > temp_response2.txt 2>nul
        set /p response2=<temp_response2.txt
        del temp_response2.txt 2>nul
        
        if "!response2!"=="200" (
            call :log "Application restarted successfully"
        ) else (
            call :error "Application restart failed"
        )
    )
)
goto :eof

REM Check SSL certificate (basic check)
:check_ssl_certificate
call :log "Checking SSL certificate..."

set "domain=your-domain.com"

REM Basic SSL check using PowerShell
powershell -Command "try { $cert = [System.Net.ServicePointManager]::ServerCertificateValidationCallback = {$true}; $req = [System.Net.WebRequest]::Create('https://%domain%'); $req.GetResponse() | Out-Null; Write-Host 'SSL certificate check: OK' } catch { Write-Host 'SSL certificate check failed' }"

goto :eof

REM Generate maintenance report
:generate_report
call :log "Generating maintenance report..."

set "report_file=%LOG_DIR%\maintenance_report_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%.txt"
set "report_file=!report_file: =0!"

(
    echo CTB Scanner Maintenance Report
    echo Generated: %date% %time%
    echo ================================
    echo.
    echo System Information:
    echo - Computer: %COMPUTERNAME%
    echo - OS: %OS%
    echo - User: %USERNAME%
    echo.
    echo Disk Space:
    dir /-c %APP_DIR% | find "bytes free"
    echo.
    echo Application Status:
    where pm2 >nul 2>&1
    if !errorlevel! equ 0 (
        pm2 status
    ) else (
        echo PM2 not available
    )
    echo.
    echo Database Status:
    sc query mysql >nul 2>&1
    if !errorlevel! equ 0 (
        echo MySQL: Running
    ) else (
        echo MySQL: Not running or not installed as service
    )
    echo.
    echo Recent Backups:
    if exist "%BACKUP_DIR%" (
        dir /b /o-d "%BACKUP_DIR%\*.sql*" 2>nul | head -5
    ) else (
        echo No backup directory found
    )
    echo.
    echo Log Files:
    if exist "%LOG_DIR%" (
        dir /b "%LOG_DIR%\*.log" 2>nul | head -10
    ) else (
        echo No log files found
    )
) > "%report_file%"

call :log "Maintenance report saved: %report_file%"
goto :eof

REM Main maintenance function
:run_maintenance
call :log "Starting CTB Scanner maintenance..."

call :backup_database
if %errorlevel% neq 0 exit /b 1

call :update_nuclei_templates
call :cleanup_logs
call :check_disk_space
call :check_app_health
call :check_ssl_certificate
call :generate_report

call :log "Maintenance completed successfully!"
goto :eof

REM Help function
:show_help
echo CTB Scanner Maintenance Script (Windows)
echo.
echo Usage: %~nx0 [OPTION]
echo.
echo Options:
echo   --full          Run full maintenance (default)
echo   --backup        Database backup only
echo   --update        Update Nuclei templates only
echo   --cleanup       Log cleanup only
echo   --health        Health check only
echo   --ssl           SSL certificate check only
echo   --report        Generate report only
echo   --help          Show this help message
echo.
echo Examples:
echo   %~nx0                    # Run full maintenance
echo   %~nx0 --backup          # Backup database only
echo   %~nx0 --health          # Check application health
goto :eof

REM Main script logic
if "%~1"=="--backup" (
    call :check_mysql
    if !errorlevel! equ 0 call :backup_database
) else if "%~1"=="--update" (
    call :update_nuclei_templates
) else if "%~1"=="--cleanup" (
    call :cleanup_logs
) else if "%~1"=="--health" (
    call :check_app_health
) else if "%~1"=="--ssl" (
    call :check_ssl_certificate
) else if "%~1"=="--report" (
    call :generate_report
) else if "%~1"=="--help" (
    call :show_help
) else if "%~1"=="--full" (
    call :check_mysql
    if !errorlevel! equ 0 call :run_maintenance
) else if "%~1"=="" (
    call :check_mysql
    if !errorlevel! equ 0 call :run_maintenance
) else (
    call :error "Unknown option: %~1"
    call :show_help
    exit /b 1
)

endlocal

/**
 * Button Component - Atomic Design System
 *
 * A highly reusable button component that serves as the foundation for all
 * interactive elements in the application. Built with accessibility,
 * performance, and design consistency in mind.
 *
 * Features:
 * - Multiple visual variants (default, destructive, outline, secondary, ghost, link)
 * - Four size options (default, sm, lg, icon)
 * - Loading state with spinner
 * - Icon support (left and right positioning)
 * - Full width option
 * - Complete accessibility support
 * - Tailwind CSS styling with consistent design tokens
 *
 * Design System:
 * - Follows atomic design principles
 * - Consistent spacing and typography
 * - Hover and focus states
 * - Disabled state handling
 * - Loading state management
 *
 * <AUTHOR> Scanner Team
 * @version 1.0.0
 */

import * as React from 'react'
import { cn } from '@/lib/utils'
import { LoadingSpinner } from './loading-spinner'

/**
 * Button component props interface
 *
 * Extends native HTML button attributes while adding custom properties
 * for enhanced functionality and styling options.
 *
 * @interface ButtonProps
 */
export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link' // Visual style variants
  size?: 'default' | 'sm' | 'lg' | 'icon'                                         // Size options
  loading?: boolean                                                               // Loading state
  leftIcon?: React.ReactNode                                                     // Icon on the left
  rightIcon?: React.ReactNode                                                    // Icon on the right
  fullWidth?: boolean                                                            // Full width option
}

/**
 * Button variant styles configuration
 *
 * Defines the visual appearance for each button variant.
 * Each variant includes base, hover, and focus states.
 */
const buttonVariants = {
  default: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',        // Primary action button
  destructive: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',      // Dangerous actions (delete, etc.)
  outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500', // Secondary actions
  secondary: 'bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500',  // Tertiary actions
  ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500',                  // Minimal styling
  link: 'text-blue-600 underline-offset-4 hover:underline focus:ring-blue-500'   // Link-style button
}

/**
 * Button size styles configuration
 *
 * Defines height, padding, and text size for each button size.
 * Ensures consistent spacing and typography across the application.
 */
const buttonSizes = {
  default: 'h-10 px-4 py-2 text-sm',  // Standard button size
  sm: 'h-8 px-3 py-1 text-xs',        // Compact button for tight spaces
  lg: 'h-12 px-6 py-3 text-base',     // Large button for emphasis
  icon: 'h-10 w-10 p-0'               // Square button for icons only
}

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ 
    className, 
    variant = 'default', 
    size = 'default', 
    loading = false,
    leftIcon,
    rightIcon,
    fullWidth = false,
    children, 
    disabled,
    ...props 
  }, ref) => {
    const isDisabled = disabled || loading

    return (
      <button
        className={cn(
          'inline-flex items-center justify-center rounded-md font-medium transition-colors',
          'focus:outline-none focus:ring-2 focus:ring-offset-2',
          'disabled:opacity-50 disabled:pointer-events-none',
          buttonVariants[variant],
          buttonSizes[size],
          fullWidth && 'w-full',
          className
        )}
        ref={ref}
        disabled={isDisabled}
        {...props}
      >
        {loading && (
          <LoadingSpinner size="sm" className="mr-2" />
        )}
        {!loading && leftIcon && (
          <span className="mr-2">{leftIcon}</span>
        )}
        {children}
        {!loading && rightIcon && (
          <span className="ml-2">{rightIcon}</span>
        )}
      </button>
    )
  }
)

Button.displayName = 'Button'

import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { requireAuth } from '@/lib/auth'
import { handleApiError, NotFoundError, AuthorizationError } from '@/lib/errors'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authentication
    const currentUser = await requireAuth()

    // Await params
    const { id } = await params

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const companyName = searchParams.get('companyName') || 'CTB Security'
    const reportTitle = searchParams.get('reportTitle')

    // Get asset with latest scan and vulnerabilities
    const asset = await db.asset.findUnique({
      where: { id },
      include: {
        scans: {
          where: {
            status: 'COMPLETED'
          },
          orderBy: {
            completedAt: 'desc'
          },
          take: 1,
          include: {
            vulnerabilities: {
              orderBy: [
                { severity: 'desc' },
                { timestamp: 'desc' }
              ]
            }
          }
        }
      }
    })

    if (!asset) {
      throw new NotFoundError('Asset not found')
    }

    // Check ownership
    if (asset.userId !== currentUser.userId) {
      throw new AuthorizationError('Access denied')
    }

    // Get the latest scan
    const latestScan = asset.scans[0]
    if (!latestScan) {
      throw new NotFoundError('No completed scans found for this asset')
    }

    // Return report data for client-side PDF generation
    const reportData = {
      scan: latestScan,
      asset: {
        id: asset.id,
        url: asset.url,
        domain: asset.domain,
        title: asset.title,
        description: asset.description,
        status: asset.status
      },
      vulnerabilities: latestScan.vulnerabilities,
      reportType: 'asset' as const,
      generatedAt: new Date().toISOString(),
      companyName,
      reportTitle: reportTitle || `Asset Security Report - ${asset.domain}`
    }

    return NextResponse.json(reportData)

  } catch (error) {
    return handleApiError(error)
  }
}

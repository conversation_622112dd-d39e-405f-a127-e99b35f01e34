import { useState, useCallback, useMemo } from 'react'

export interface PaginationState {
  page: number
  limit: number
  total: number
  pages: number
}

export interface UsePaginationOptions {
  initialPage?: number
  initialLimit?: number
  total?: number
}

export interface UsePaginationReturn {
  pagination: PaginationState
  setPage: (page: number) => void
  setLimit: (limit: number) => void
  setTotal: (total: number) => void
  nextPage: () => void
  prevPage: () => void
  canNextPage: boolean
  canPrevPage: boolean
  reset: () => void
  getOffset: () => number
}

export const usePagination = ({
  initialPage = 1,
  initialLimit = 10,
  total = 0
}: UsePaginationOptions = {}): UsePaginationReturn => {
  const [page, setPageState] = useState(initialPage)
  const [limit, setLimitState] = useState(initialLimit)
  const [totalItems, setTotalItems] = useState(total)

  const pages = useMemo(() => {
    return Math.ceil(totalItems / limit) || 1
  }, [totalItems, limit])

  const pagination: PaginationState = useMemo(() => ({
    page,
    limit,
    total: totalItems,
    pages
  }), [page, limit, totalItems, pages])

  const setPage = useCallback((newPage: number) => {
    const clampedPage = Math.max(1, Math.min(newPage, pages))
    setPageState(clampedPage)
  }, [pages])

  const setLimit = useCallback((newLimit: number) => {
    setLimitState(newLimit)
    // Reset to first page when changing limit
    setPageState(1)
  }, [])

  const setTotal = useCallback((newTotal: number) => {
    setTotalItems(newTotal)
    // Adjust current page if it's beyond the new total pages
    const newPages = Math.ceil(newTotal / limit) || 1
    if (page > newPages) {
      setPageState(newPages)
    }
  }, [page, limit])

  const nextPage = useCallback(() => {
    setPage(page + 1)
  }, [page, setPage])

  const prevPage = useCallback(() => {
    setPage(page - 1)
  }, [page, setPage])

  const canNextPage = useMemo(() => page < pages, [page, pages])
  const canPrevPage = useMemo(() => page > 1, [page])

  const reset = useCallback(() => {
    setPageState(initialPage)
    setLimitState(initialLimit)
    setTotalItems(0)
  }, [initialPage, initialLimit])

  const getOffset = useCallback(() => {
    return (page - 1) * limit
  }, [page, limit])

  return {
    pagination,
    setPage,
    setLimit,
    setTotal,
    nextPage,
    prevPage,
    canNextPage,
    canPrevPage,
    reset,
    getOffset
  }
}

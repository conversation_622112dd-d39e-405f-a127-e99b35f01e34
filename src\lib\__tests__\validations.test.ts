/**
 * Jest tests for validation schemas
 */

import { signupSchema, loginSchema, validateData } from '../validations'

describe('Validation Schemas', () => {
  describe('Signup Validation', () => {
    it('should pass validation with valid signup data', () => {
      const validData = {
        firstName: 'John',
        lastName: 'Doe',
        companyName: 'Acme Corp',
        country: 'United States',
        email: '<EMAIL>',
        password: 'SecurePass123!',
        confirmPassword: 'SecurePass123!'
      }

      const result = validateData(signupSchema, validData)
      expect(result.success).toBe(true)
    })

    it('should fail validation with invalid email', () => {
      const invalidData = {
        firstName: 'John',
        lastName: 'Doe',
        companyName: 'Acme Corp',
        country: 'United States',
        email: 'invalid-email',
        password: 'SecurePass123!',
        confirmPassword: 'SecurePass123!'
      }

      const result = validateData(signupSchema, invalidData)
      expect(result.success).toBe(false)
    })

    it('should fail validation with weak password', () => {
      const weakPasswordData = {
        firstName: 'John',
        lastName: 'Doe',
        companyName: 'Acme Corp',
        country: 'United States',
        email: '<EMAIL>',
        password: 'weak',
        confirmPassword: 'weak'
      }

      const result = validateData(signupSchema, weakPasswordData)
      expect(result.success).toBe(false)
    })

    it('should fail validation with mismatched passwords', () => {
      const mismatchedData = {
        firstName: 'John',
        lastName: 'Doe',
        companyName: 'Acme Corp',
        country: 'United States',
        email: '<EMAIL>',
        password: 'SecurePass123!',
        confirmPassword: 'DifferentPass123!'
      }

      const result = validateData(signupSchema, mismatchedData)
      expect(result.success).toBe(false)
    })
  })

  describe('Login Validation', () => {
    it('should pass validation with valid login data', () => {
      const validData = {
        email: '<EMAIL>',
        password: 'SecurePass123!'
      }

      const result = validateData(loginSchema, validData)
      expect(result.success).toBe(true)
    })

    it('should fail validation with empty email', () => {
      const invalidData = {
        email: '',
        password: 'SecurePass123!'
      }

      const result = validateData(loginSchema, invalidData)
      expect(result.success).toBe(false)
    })
  })
})

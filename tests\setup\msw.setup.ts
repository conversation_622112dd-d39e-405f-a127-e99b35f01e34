import { setupServer } from 'msw/node'
import { rest } from 'msw'
import { createMockUser, createMockScan, createMockAsset, createMockVulnerability } from '../utils/test-helpers'

// Mock API handlers
export const handlers = [
  // Auth endpoints
  rest.post('/api/auth/signup', (req, res, ctx) => {
    return res(
      ctx.status(201),
      ctx.json({
        message: 'User created successfully',
        user: createMockUser(),
      })
    )
  }),

  rest.post('/api/auth/login', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        message: 'Login successful',
        user: createMockUser(),
      }),
      ctx.cookie('auth-token', 'mock-jwt-token')
    )
  }),

  rest.post('/api/auth/logout', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({ message: 'Logout successful' }),
      ctx.cookie('auth-token', '', { maxAge: 0 })
    )
  }),

  rest.get('/api/auth/me', (req, res, ctx) => {
    const authCookie = req.cookies['auth-token']
    
    if (!authCookie) {
      return res(
        ctx.status(401),
        ctx.json({ error: 'Unauthorized' })
      )
    }

    return res(
      ctx.status(200),
      ctx.json({
        user: createMockUser(),
      })
    )
  }),

  // Scans endpoints
  rest.post('/api/scans', (req, res, ctx) => {
    return res(
      ctx.status(201),
      ctx.json({
        message: 'Scan initiated successfully',
        scanId: 'scan-123',
        status: 'PENDING',
      })
    )
  }),

  rest.get('/api/scans', (req, res, ctx) => {
    const page = Number(req.url.searchParams.get('page')) || 1
    const limit = Number(req.url.searchParams.get('limit')) || 10
    const status = req.url.searchParams.get('status')

    let scans = [
      createMockScan({ id: '1', status: 'COMPLETED' }),
      createMockScan({ id: '2', status: 'RUNNING' }),
      createMockScan({ id: '3', status: 'FAILED' }),
    ]

    if (status) {
      scans = scans.filter(scan => scan.status === status)
    }

    return res(
      ctx.status(200),
      ctx.json({
        scans,
        pagination: {
          page,
          pages: Math.ceil(scans.length / limit),
          total: scans.length,
          limit,
        },
      })
    )
  }),

  rest.get('/api/scans/:id', (req, res, ctx) => {
    const { id } = req.params

    if (id === 'non-existent') {
      return res(
        ctx.status(404),
        ctx.json({ error: 'Scan not found' })
      )
    }

    return res(
      ctx.status(200),
      ctx.json({
        scan: {
          ...createMockScan({ id: id as string }),
          asset: createMockAsset(),
          vulnerabilities: [
            createMockVulnerability({ id: '1' }),
            createMockVulnerability({ id: '2' }),
          ],
        },
      })
    )
  }),

  // Assets endpoints
  rest.get('/api/assets', (req, res, ctx) => {
    const page = Number(req.url.searchParams.get('page')) || 1
    const limit = Number(req.url.searchParams.get('limit')) || 10

    const assets = [
      createMockAsset({ id: '1', url: 'https://example1.com' }),
      createMockAsset({ id: '2', url: 'https://example2.com' }),
      createMockAsset({ id: '3', url: 'https://example3.com' }),
    ]

    return res(
      ctx.status(200),
      ctx.json({
        assets,
        pagination: {
          page,
          pages: Math.ceil(assets.length / limit),
          total: assets.length,
          limit,
        },
      })
    )
  }),

  rest.get('/api/assets/:id', (req, res, ctx) => {
    const { id } = req.params

    return res(
      ctx.status(200),
      ctx.json({
        asset: createMockAsset({ id: id as string }),
      })
    )
  }),

  // Vulnerabilities endpoints
  rest.get('/api/vulnerabilities', (req, res, ctx) => {
    const page = Number(req.url.searchParams.get('page')) || 1
    const limit = Number(req.url.searchParams.get('limit')) || 10
    const severity = req.url.searchParams.get('severity')

    let vulnerabilities = [
      createMockVulnerability({ id: '1', severity: 'CRITICAL' }),
      createMockVulnerability({ id: '2', severity: 'HIGH' }),
      createMockVulnerability({ id: '3', severity: 'MEDIUM' }),
    ]

    if (severity) {
      vulnerabilities = vulnerabilities.filter(vuln => vuln.severity === severity)
    }

    return res(
      ctx.status(200),
      ctx.json({
        vulnerabilities,
        pagination: {
          page,
          pages: Math.ceil(vulnerabilities.length / limit),
          total: vulnerabilities.length,
          limit,
        },
      })
    )
  }),

  // Export endpoints
  rest.get('/api/scans/export/csv', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.set('Content-Type', 'text/csv'),
      ctx.set('Content-Disposition', 'attachment; filename="scans.csv"'),
      ctx.text('Target URL,Status,Vulnerabilities,Date\nhttps://example.com,COMPLETED,5,2024-01-01')
    )
  }),

  rest.get('/api/scans/export/json', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.set('Content-Type', 'application/json'),
      ctx.set('Content-Disposition', 'attachment; filename="scans.json"'),
      ctx.json([createMockScan()])
    )
  }),

  // Dashboard stats
  rest.get('/api/dashboard/stats', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        totalScans: 25,
        activeAssets: 10,
        totalVulnerabilities: 45,
        criticalVulnerabilities: 5,
        recentScans: [
          createMockScan({ id: '1' }),
          createMockScan({ id: '2' }),
        ],
      })
    )
  }),

  // Error handlers for testing error states
  rest.post('/api/scans/error', (req, res, ctx) => {
    return res(
      ctx.status(500),
      ctx.json({ error: 'Internal server error' })
    )
  }),

  rest.get('/api/scans/timeout', (req, res, ctx) => {
    return res(
      ctx.delay('infinite')
    )
  }),
]

// Setup MSW server
export const server = setupServer(...handlers)

// Start server before all tests
beforeAll(() => server.listen({ onUnhandledRequest: 'error' }))

// Reset handlers after each test
afterEach(() => server.resetHandlers())

// Clean up after all tests
afterAll(() => server.close())

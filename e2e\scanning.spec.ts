import { test, expect } from '@playwright/test'

test.describe('Vulnerability Scanning', () => {
  test.beforeEach(async ({ page }) => {
    // Create account and login
    const email = `scan-test-${Date.now()}@example.com`
    const password = 'TestPassword123!'
    
    await page.goto('/')
    await page.getByRole('link', { name: /sign up/i }).click()
    await page.getByPlaceholder(/first name/i).fill('John')
    await page.getByPlaceholder(/last name/i).fill('Doe')
    await page.getByPlaceholder(/company name/i).fill('Test Company')
    await page.getByPlaceholder(/country/i).fill('United States')
    await page.getByPlaceholder(/email/i).fill(email)
    await page.getByPlaceholder(/^password$/i).fill(password)
    await page.getByPlaceholder(/confirm password/i).fill(password)
    await page.getByRole('button', { name: /create account/i }).click()
    
    await expect(page).toHaveURL('/dashboard')
  })

  test('should display scan form on dashboard', async ({ page }) => {
    await expect(page.getByRole('heading', { name: /vulnerability scanner/i })).toBeVisible()
    await expect(page.getByPlaceholder(/enter target url/i)).toBeVisible()
    await expect(page.getByRole('button', { name: /start scan/i })).toBeVisible()
  })

  test('should show validation error for empty URL', async ({ page }) => {
    await page.getByRole('button', { name: /start scan/i }).click()
    await expect(page.getByText(/url is required/i)).toBeVisible()
  })

  test('should show validation error for invalid URL', async ({ page }) => {
    await page.getByPlaceholder(/enter target url/i).fill('invalid-url')
    await page.getByRole('button', { name: /start scan/i }).click()
    await expect(page.getByText(/invalid url/i)).toBeVisible()
  })

  test('should reject private IP addresses', async ({ page }) => {
    await page.getByPlaceholder(/enter target url/i).fill('http://***********')
    await page.getByRole('button', { name: /start scan/i }).click()
    await expect(page.getByText(/private.*not allowed/i)).toBeVisible()
  })

  test('should reject localhost URLs', async ({ page }) => {
    await page.getByPlaceholder(/enter target url/i).fill('http://localhost:3000')
    await page.getByRole('button', { name: /start scan/i }).click()
    await expect(page.getByText(/localhost.*not allowed/i)).toBeVisible()
  })

  test('should initiate scan with valid URL', async ({ page }) => {
    await page.getByPlaceholder(/enter target url/i).fill('https://example.com')
    await page.getByRole('button', { name: /start scan/i }).click()
    
    // Should show success message
    await expect(page.getByText(/scan initiated/i)).toBeVisible()
    
    // Should navigate to scans page or show scan progress
    await expect(page.getByText(/scan.*progress/i).or(page.getByText(/pending/i))).toBeVisible()
  })

  test('should display scan type options', async ({ page }) => {
    await expect(page.getByText(/quick scan/i)).toBeVisible()
    await expect(page.getByText(/deep scan/i)).toBeVisible()
  })

  test('should allow selecting scan type', async ({ page }) => {
    // Select deep scan
    await page.getByText(/deep scan/i).click()
    await expect(page.getByText(/deep scan/i)).toBeChecked()
    
    // Select quick scan
    await page.getByText(/quick scan/i).click()
    await expect(page.getByText(/quick scan/i)).toBeChecked()
  })

  test('should display severity level options', async ({ page }) => {
    await expect(page.getByText(/critical/i)).toBeVisible()
    await expect(page.getByText(/high/i)).toBeVisible()
    await expect(page.getByText(/medium/i)).toBeVisible()
    await expect(page.getByText(/low/i)).toBeVisible()
    await expect(page.getByText(/info/i)).toBeVisible()
  })

  test('should allow selecting multiple severity levels', async ({ page }) => {
    // Select critical and high
    await page.getByRole('checkbox', { name: /critical/i }).check()
    await page.getByRole('checkbox', { name: /high/i }).check()
    
    await expect(page.getByRole('checkbox', { name: /critical/i })).toBeChecked()
    await expect(page.getByRole('checkbox', { name: /high/i })).toBeChecked()
  })

  test('should navigate to scans history', async ({ page }) => {
    await page.getByRole('link', { name: /scans/i }).click()
    await expect(page).toHaveURL('/scans')
    await expect(page.getByRole('heading', { name: /scan history/i })).toBeVisible()
  })

  test('should display scans table', async ({ page }) => {
    await page.getByRole('link', { name: /scans/i }).click()
    
    await expect(page.getByRole('columnheader', { name: /target/i })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: /status/i })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: /vulnerabilities/i })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: /date/i })).toBeVisible()
  })

  test('should show empty state when no scans', async ({ page }) => {
    await page.getByRole('link', { name: /scans/i }).click()
    await expect(page.getByText(/no scans found/i)).toBeVisible()
  })

  test('should create scan and show in history', async ({ page }) => {
    // Create a scan
    await page.getByPlaceholder(/enter target url/i).fill('https://test-scan.com')
    await page.getByRole('button', { name: /start scan/i }).click()
    await expect(page.getByText(/scan initiated/i)).toBeVisible()
    
    // Navigate to scans history
    await page.getByRole('link', { name: /scans/i }).click()
    
    // Should see the scan in the table
    await expect(page.getByText('https://test-scan.com')).toBeVisible()
    await expect(page.getByText(/pending|running/i)).toBeVisible()
  })

  test('should navigate to assets page', async ({ page }) => {
    await page.getByRole('link', { name: /assets/i }).click()
    await expect(page).toHaveURL('/assets')
    await expect(page.getByRole('heading', { name: /asset inventory/i })).toBeVisible()
  })

  test('should display assets table', async ({ page }) => {
    await page.getByRole('link', { name: /assets/i }).click()
    
    await expect(page.getByRole('columnheader', { name: /url/i })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: /domain/i })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: /status/i })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: /last scanned/i })).toBeVisible()
  })

  test('should show empty state when no assets', async ({ page }) => {
    await page.getByRole('link', { name: /assets/i }).click()
    await expect(page.getByText(/no assets found/i)).toBeVisible()
  })

  test('should create asset when scanning new URL', async ({ page }) => {
    // Create a scan
    await page.getByPlaceholder(/enter target url/i).fill('https://new-asset.com')
    await page.getByRole('button', { name: /start scan/i }).click()
    await expect(page.getByText(/scan initiated/i)).toBeVisible()
    
    // Navigate to assets
    await page.getByRole('link', { name: /assets/i }).click()
    
    // Should see the new asset
    await expect(page.getByText('https://new-asset.com')).toBeVisible()
    await expect(page.getByText('new-asset.com')).toBeVisible()
  })

  test('should support pagination in scans table', async ({ page }) => {
    await page.getByRole('link', { name: /scans/i }).click()
    
    // Check if pagination controls exist (may not be visible if no data)
    const pagination = page.getByRole('navigation', { name: /pagination/i })
    if (await pagination.isVisible()) {
      await expect(pagination).toBeVisible()
    }
  })

  test('should support pagination in assets table', async ({ page }) => {
    await page.getByRole('link', { name: /assets/i }).click()
    
    // Check if pagination controls exist (may not be visible if no data)
    const pagination = page.getByRole('navigation', { name: /pagination/i })
    if (await pagination.isVisible()) {
      await expect(pagination).toBeVisible()
    }
  })

  test('should display scan statistics on dashboard', async ({ page }) => {
    await expect(page.getByText(/total scans/i)).toBeVisible()
    await expect(page.getByText(/active assets/i)).toBeVisible()
    await expect(page.getByText(/vulnerabilities found/i)).toBeVisible()
  })

  test('should show responsive design on mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Check if mobile menu toggle is visible
    const mobileMenuButton = page.getByRole('button', { name: /menu/i })
    if (await mobileMenuButton.isVisible()) {
      await expect(mobileMenuButton).toBeVisible()
    }
    
    // Check if main content is still accessible
    await expect(page.getByRole('heading', { name: /dashboard/i })).toBeVisible()
  })
})

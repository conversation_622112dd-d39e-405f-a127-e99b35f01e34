import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { requireAuth } from '@/lib/auth'
import { handleApiError, NotFoundError, AuthorizationError } from '@/lib/errors'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authentication
    const currentUser = await requireAuth()

    // Await params
    const { id } = await params

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const severity = searchParams.get('severity')
    const search = searchParams.get('search')
    const format = searchParams.get('format') || 'json'

    // Check if scan exists and belongs to user
    const scan = await db.scan.findUnique({
      where: { id },
      include: {
        asset: {
          select: {
            id: true,
            url: true,
            domain: true,
            title: true,
            description: true
          }
        }
      }
    })

    if (!scan) {
      throw new NotFoundError('Scan not found')
    }

    if (scan.userId !== currentUser.userId) {
      throw new AuthorizationError('Access denied')
    }

    // Build where clause for vulnerabilities
    const where: any = {
      scanId: id
    }

    if (severity) {
      where.severity = severity.toUpperCase()
    }

    if (search) {
      where.OR = [
        { name: { contains: search } },
        { templateId: { contains: search } },
        { host: { contains: search } },
        { description: { contains: search } }
      ]
    }

    // Get ALL vulnerabilities for this scan
    const vulnerabilities = await db.vulnerability.findMany({
      where,
      orderBy: [
        { severity: 'desc' },
        { timestamp: 'desc' }
      ]
    })

    const exportData = {
      scan: {
        id: scan.id,
        targetUrl: scan.targetUrl,
        status: scan.status,
        duration: scan.duration,
        startedAt: scan.startedAt,
        completedAt: scan.completedAt,
        createdAt: scan.createdAt,
        totalVulnerabilities: scan.totalVulns,
        severityBreakdown: {
          critical: scan.criticalVulns,
          high: scan.highVulns,
          medium: scan.mediumVulns,
          low: scan.lowVulns,
          info: scan.infoVulns,
          unknown: scan.unknownVulns || 0
        }
      },
      asset: scan.asset,
      vulnerabilities,
      statistics: {
        totalVulnerabilities: vulnerabilities.length,
        severityBreakdown: {
          critical: vulnerabilities.filter(v => v.severity === 'CRITICAL').length,
          high: vulnerabilities.filter(v => v.severity === 'HIGH').length,
          medium: vulnerabilities.filter(v => v.severity === 'MEDIUM').length,
          low: vulnerabilities.filter(v => v.severity === 'LOW').length,
          info: vulnerabilities.filter(v => v.severity === 'INFO').length,
          unknown: vulnerabilities.filter(v => v.severity === 'UNKNOWN').length
        }
      },
      exportedAt: new Date().toISOString(),
      filters: {
        severity,
        search
      }
    }

    if (format === 'csv') {
      const csvHeaders = ['Name', 'Severity', 'Host', 'Template ID', 'Timestamp']
      const csvRows = vulnerabilities.map(vuln => [
        vuln.name,
        vuln.severity,
        vuln.host,
        vuln.templateId,
        new Date(vuln.timestamp).toLocaleString()
      ])

      const csvContent = [csvHeaders, ...csvRows]
        .map(row => row.map(field => `"${field}"`).join(','))
        .join('\n')

      return new NextResponse(csvContent, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="scan-${scan.id}-vulnerabilities-${new Date().toISOString().split('T')[0]}.csv"`
        }
      })
    }

    // Default to JSON
    return NextResponse.json(exportData)

  } catch (error) {
    return handleApiError(error)
  }
}

/**
 * Enhanced Logging Utility for CTB Scanner
 *
 * Provides structured logging with different levels and contexts
 * for better debugging and monitoring of the scanning workflow.
 */

import { writeFileSync, appendFileSync, existsSync, mkdirSync } from 'fs'
import { dirname } from 'path'

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  FATAL = 4
}

export interface LogContext {
  scanId?: string
  userId?: string
  targetUrl?: string
  component?: string
  action?: string
  requestId?: string
  ip?: string
  userAgent?: string
  duration?: number
  fingerprint?: string
  severity?: string
  count?: number
  alertType?: string
  error?: {
    name: string
    message: string
    stack?: string
  }
}

export interface LogEntry {
  timestamp: string
  level: string
  message: string
  context?: LogContext
}

class Logger {
  private logLevel!: LogLevel
  private enableFileLogging!: boolean
  private logFilePath!: string
  private logFormat!: 'json' | 'text'

  constructor() {
    this.configure()
  }

  /**
   * Configure logger based on environment variables
   */
  configure() {
    // Set log level based on environment
    const envLevel = process.env.LOG_LEVEL?.toUpperCase()
    switch (envLevel) {
      case 'DEBUG':
        this.logLevel = LogLevel.DEBUG
        break
      case 'INFO':
        this.logLevel = LogLevel.INFO
        break
      case 'WARN':
        this.logLevel = LogLevel.WARN
        break
      case 'ERROR':
        this.logLevel = LogLevel.ERROR
        break
      case 'FATAL':
        this.logLevel = LogLevel.FATAL
        break
      default:
        this.logLevel = process.env.NODE_ENV === 'production' ? LogLevel.INFO : LogLevel.DEBUG
    }

    // File logging configuration
    this.enableFileLogging = process.env.LOG_FILE_ENABLED === 'true'
    this.logFilePath = process.env.LOG_FILE_PATH || './logs/app.log'
    this.logFormat = (process.env.LOG_FORMAT as 'json' | 'text') || 'json'

    // Ensure log directory exists
    if (this.enableFileLogging) {
      const logDir = dirname(this.logFilePath)
      if (!existsSync(logDir)) {
        mkdirSync(logDir, { recursive: true })
      }
    }
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.logLevel
  }

  private formatMessage(level: string, message: string, context?: LogContext): string {
    const timestamp = new Date().toISOString()
    const contextStr = context ? ` [${Object.entries(context).map(([k, v]) => `${k}=${v}`).join(', ')}]` : ''
    const formattedMessage = `${timestamp} [${level}]${contextStr} ${message}`

    // Also write to file if enabled
    if (this.enableFileLogging) {
      this.writeToFile(level, message, context, timestamp)
    }

    return formattedMessage
  }

  private formatMessageForFile(level: string, message: string, context?: LogContext, timestamp?: string): string {
    const ts = timestamp || new Date().toISOString()
    const contextStr = context ? ` [${Object.entries(context).map(([k, v]) => `${k}=${v}`).join(', ')}]` : ''
    return `${ts} [${level}]${contextStr} ${message}`
  }

  private writeToFile(level: string, message: string, context?: LogContext, timestamp?: string): void {
    try {
      const logEntry: LogEntry = {
        timestamp: timestamp || new Date().toISOString(),
        level,
        message,
        ...(context && { context })
      }

      const output = this.logFormat === 'json'
        ? JSON.stringify(logEntry) + '\n'
        : this.formatMessageForFile(level, message, context, timestamp) + '\n'

      appendFileSync(this.logFilePath, output, 'utf8')
    } catch (error) {
      console.error('Failed to write to log file:', error)
    }
  }

  debug(message: string, context?: LogContext): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      console.log(this.formatMessage('DEBUG', message, context))
    }
  }

  info(message: string, context?: LogContext): void {
    if (this.shouldLog(LogLevel.INFO)) {
      console.log(this.formatMessage('INFO', message, context))
    }
  }

  warn(message: string, context?: LogContext): void {
    if (this.shouldLog(LogLevel.WARN)) {
      console.warn(this.formatMessage('WARN', message, context))
    }
  }

  error(message: string, error?: Error, context?: LogContext): void {
    if (this.shouldLog(LogLevel.ERROR)) {
      const errorContext = error ? {
        ...context,
        error: {
          name: error.name,
          message: error.message,
          stack: error.stack
        }
      } : context

      const errorStr = error ? ` - ${error.message}\n${error.stack}` : ''
      console.error(this.formatMessage('ERROR', message + errorStr, errorContext))
    }
  }

  fatal(message: string, error?: Error, context?: LogContext): void {
    if (this.shouldLog(LogLevel.FATAL)) {
      const errorContext = error ? {
        ...context,
        error: {
          name: error.name,
          message: error.message,
          stack: error.stack
        }
      } : context

      const errorStr = error ? ` - ${error.message}\n${error.stack}` : ''
      console.error(this.formatMessage('FATAL', message + errorStr, errorContext))
    }
  }

  // Request logging
  request(method: string, url: string, statusCode: number, responseTime: number, context?: LogContext): void {
    this.info(`${method} ${url} - ${statusCode} (${responseTime}ms)`, {
      ...context,
      component: 'REQUEST',
      duration: responseTime
    })
  }

  // Performance logging
  performance(operation: string, duration: number, context?: LogContext): void {
    this.info(`Performance: ${operation} completed in ${duration}ms`, {
      ...context,
      component: 'PERFORMANCE',
      duration
    })
  }

  // Security event logging
  security(event: string, severity: 'low' | 'medium' | 'high' | 'critical', context?: LogContext): void {
    const level = severity === 'critical' ? LogLevel.FATAL :
                 severity === 'high' ? LogLevel.ERROR : LogLevel.WARN

    if (this.shouldLog(level)) {
      const securityContext = {
        ...context,
        component: 'SECURITY',
        severity
      }

      const levelName = LogLevel[level]
      console.log(this.formatMessage(levelName, `Security Event: ${event}`, securityContext))
    }
  }

  // Specialized logging methods for different components
  scan(message: string, scanId: string, context?: Partial<LogContext>): void {
    this.info(message, { ...context, scanId, component: 'SCAN' })
  }

  queue(message: string, context?: LogContext): void {
    this.info(message, { ...context, component: 'QUEUE' })
  }

  nuclei(message: string, context?: LogContext): void {
    this.info(message, { ...context, component: 'NUCLEI' })
  }

  api(message: string, context?: LogContext): void {
    this.info(message, { ...context, component: 'API' })
  }

  db(message: string, context?: LogContext): void {
    this.debug(message, { ...context, component: 'DATABASE' })
  }
}

// Singleton instance
export const logger = new Logger()

// Convenience functions for common logging patterns
export const logScanStart = (scanId: string, targetUrl: string, userId: string) => {
  logger.scan('Scan started', scanId, { targetUrl, userId, action: 'START' })
}

export const logScanComplete = (scanId: string, duration: number, vulnCount: number) => {
  logger.scan(`Scan completed in ${duration}s with ${vulnCount} vulnerabilities`, scanId, { action: 'COMPLETE' })
}

export const logScanError = (scanId: string, error: Error) => {
  logger.error('Scan failed', error, { scanId, component: 'SCAN', action: 'ERROR' })
}

export const logVulnerabilityFound = (scanId: string, templateId: string, severity: string) => {
  logger.scan(`Vulnerability found: ${templateId} (${severity})`, scanId, { action: 'VULNERABILITY' })
}

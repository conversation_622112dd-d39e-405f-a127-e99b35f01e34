import React from 'react'
import { Shield, AlertTriangle, CheckCircle, XCircle, Clock, Globe, Calendar, User } from 'lucide-react'

interface Vulnerability {
  id: string
  name: string
  severity: string
  host: string
  templateId: string
  description?: string
  timestamp: string
  matchedAt?: string
}

interface Scan {
  id: string
  targetUrl: string
  status: string
  duration?: number
  startedAt?: string
  completedAt?: string
  createdAt: string
  totalVulns: number
  criticalVulns: number
  highVulns: number
  mediumVulns: number
  lowVulns: number
  infoVulns: number
  unknownVulns?: number
}

interface Asset {
  id: string
  url: string
  domain: string
  title: string
  description?: string
  status: string
}

interface PDFReportTemplateProps {
  scan: Scan
  asset?: Asset
  vulnerabilities: Vulnerability[]
  reportType: 'scan' | 'asset'
  generatedAt: string
  companyName?: string
  reportTitle?: string
}

export const PDFReportTemplate: React.FC<PDFReportTemplateProps> = ({
  scan,
  asset,
  vulnerabilities,
  reportType,
  generatedAt,
  companyName = 'CTB Security',
  reportTitle
}) => {
  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical': return '#dc2626'
      case 'high': return '#ea580c'
      case 'medium': return '#d97706'
      case 'low': return '#65a30d'
      case 'info': return '#0284c7'
      default: return '#6b7280'
    }
  }

  const getSeverityIcon = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical': return <XCircle className="w-4 h-4" />
      case 'high': return <AlertTriangle className="w-4 h-4" />
      case 'medium': return <AlertTriangle className="w-4 h-4" />
      case 'low': return <CheckCircle className="w-4 h-4" />
      case 'info': return <CheckCircle className="w-4 h-4" />
      default: return <Shield className="w-4 h-4" />
    }
  }

  const getRiskLevel = () => {
    if (scan.criticalVulns > 0) return { level: 'Critical', color: '#dc2626' }
    if (scan.highVulns > 0) return { level: 'High', color: '#ea580c' }
    if (scan.mediumVulns > 0) return { level: 'Medium', color: '#d97706' }
    if (scan.lowVulns > 0) return { level: 'Low', color: '#65a30d' }
    return { level: 'Minimal', color: '#10b981' }
  }

  const riskAssessment = getRiskLevel()

  return (
    <div
      id="pdf-report"
      className="bg-white text-black p-8 max-w-4xl mx-auto"
      style={{
        fontFamily: 'Arial, sans-serif',
        backgroundColor: '#ffffff',
        color: '#000000'
      }}
    >
      {/* Header */}
      <div className="border-b-4 border-blue-600 pb-6 mb-8">
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              {reportTitle || `${reportType === 'scan' ? 'Vulnerability Scan' : 'Asset Security'} Report`}
            </h1>
            <p className="text-lg text-gray-600">{companyName}</p>
          </div>
          <div className="text-right">
            <div className="flex items-center text-gray-600 mb-2">
              <Calendar className="w-4 h-4 mr-2" />
              <span>{new Date(generatedAt).toLocaleDateString()}</span>
            </div>
            <div className="flex items-center text-gray-600">
              <Clock className="w-4 h-4 mr-2" />
              <span>{new Date(generatedAt).toLocaleTimeString()}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Executive Summary */}
      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-4 border-b-2 border-gray-200 pb-2">
          Executive Summary
        </h2>
        <div className="grid grid-cols-2 gap-6">
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold text-gray-900 mb-3">Target Information</h3>
            <div className="space-y-2 text-sm">
              <div className="flex items-center">
                <Globe className="w-4 h-4 mr-2 text-gray-500" />
                <span className="font-medium">URL:</span>
                <span className="ml-2 break-all">{scan.targetUrl}</span>
              </div>
              {asset && (
                <>
                  <div className="flex items-center">
                    <span className="font-medium">Domain:</span>
                    <span className="ml-2">{asset.domain}</span>
                  </div>
                  <div className="flex items-center">
                    <span className="font-medium">Title:</span>
                    <span className="ml-2">{asset.title}</span>
                  </div>
                </>
              )}
              <div className="flex items-center">
                <span className="font-medium">Scan Date:</span>
                <span className="ml-2">{new Date(scan.createdAt).toLocaleDateString()}</span>
              </div>
              {scan.duration && (
                <div className="flex items-center">
                  <span className="font-medium">Duration:</span>
                  <span className="ml-2">{Math.floor(scan.duration / 60)}m {scan.duration % 60}s</span>
                </div>
              )}
            </div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold text-gray-900 mb-3">Risk Assessment</h3>
            <div className="text-center">
              <div 
                className="inline-flex items-center px-4 py-2 rounded-full text-white font-bold text-lg mb-3"
                style={{ backgroundColor: riskAssessment.color }}
              >
                {riskAssessment.level} Risk
              </div>
              <p className="text-sm text-gray-600">
                {scan.totalVulns} total vulnerabilities discovered
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Vulnerability Summary */}
      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-4 border-b-2 border-gray-200 pb-2">
          Vulnerability Summary
        </h2>
        <div className="grid grid-cols-6 gap-4">
          {[
            { label: 'Critical', count: scan.criticalVulns, color: '#dc2626' },
            { label: 'High', count: scan.highVulns, color: '#ea580c' },
            { label: 'Medium', count: scan.mediumVulns, color: '#d97706' },
            { label: 'Low', count: scan.lowVulns, color: '#65a30d' },
            { label: 'Info', count: scan.infoVulns, color: '#0284c7' },
            { label: 'Unknown', count: scan.unknownVulns || 0, color: '#6b7280' }
          ].map((item) => (
            <div key={item.label} className="text-center p-4 border rounded-lg">
              <div 
                className="text-2xl font-bold mb-1"
                style={{ color: item.color }}
              >
                {item.count}
              </div>
              <div className="text-sm text-gray-600">{item.label}</div>
            </div>
          ))}
        </div>
      </section>

      {/* Detailed Findings */}
      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-4 border-b-2 border-gray-200 pb-2">
          Detailed Findings
        </h2>
        {vulnerabilities.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Shield className="w-12 h-12 mx-auto mb-4" />
            <p>No vulnerabilities found. The target appears to be secure.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {vulnerabilities
              .sort((a, b) => {
                const severityOrder = { 'CRITICAL': 5, 'HIGH': 4, 'MEDIUM': 3, 'LOW': 2, 'INFO': 1, 'UNKNOWN': 0 }
                return (severityOrder[b.severity as keyof typeof severityOrder] || 0) - 
                       (severityOrder[a.severity as keyof typeof severityOrder] || 0)
              })
              .map((vuln, index) => (
                <div key={vuln.id} className="border rounded-lg p-4 bg-gray-50">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center">
                      <span style={{ color: getSeverityColor(vuln.severity) }}>
                        {getSeverityIcon(vuln.severity)}
                      </span>
                      <h3 className="font-semibold text-gray-900 ml-2">{vuln.name}</h3>
                    </div>
                    <span 
                      className="px-2 py-1 rounded text-xs font-medium text-white"
                      style={{ backgroundColor: getSeverityColor(vuln.severity) }}
                    >
                      {vuln.severity}
                    </span>
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium text-gray-700">Host:</span>
                      <span className="ml-2 text-gray-600">{vuln.host}</span>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Template ID:</span>
                      <span className="ml-2 text-gray-600">{vuln.templateId}</span>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Discovered:</span>
                      <span className="ml-2 text-gray-600">{new Date(vuln.timestamp).toLocaleString()}</span>
                    </div>
                  </div>
                  {vuln.description && (
                    <div className="mt-3 pt-3 border-t border-gray-200">
                      <span className="font-medium text-gray-700">Description:</span>
                      <p className="text-gray-600 mt-1 text-sm">{vuln.description}</p>
                    </div>
                  )}
                </div>
              ))}
          </div>
        )}
      </section>

      {/* Recommendations */}
      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-4 border-b-2 border-gray-200 pb-2">
          Recommendations
        </h2>
        <div className="space-y-4">
          {scan.criticalVulns > 0 && (
            <div className="bg-red-50 border-l-4 border-red-500 p-4">
              <h3 className="font-semibold text-red-800 mb-2">Immediate Action Required</h3>
              <p className="text-red-700 text-sm">
                Critical vulnerabilities were found that require immediate attention. These vulnerabilities 
                pose a significant risk to your system security and should be addressed as soon as possible.
              </p>
            </div>
          )}
          
          {scan.highVulns > 0 && (
            <div className="bg-orange-50 border-l-4 border-orange-500 p-4">
              <h3 className="font-semibold text-orange-800 mb-2">High Priority</h3>
              <p className="text-orange-700 text-sm">
                High-severity vulnerabilities should be addressed within the next few days. 
                These issues could potentially be exploited by attackers.
              </p>
            </div>
          )}

          <div className="bg-blue-50 border-l-4 border-blue-500 p-4">
            <h3 className="font-semibold text-blue-800 mb-2">General Security Recommendations</h3>
            <ul className="text-blue-700 text-sm space-y-1">
              <li>• Implement regular security scanning as part of your development lifecycle</li>
              <li>• Keep all software components and dependencies up to date</li>
              <li>• Follow secure coding practices and conduct code reviews</li>
              <li>• Implement proper input validation and output encoding</li>
              <li>• Use HTTPS for all communications</li>
              <li>• Implement proper authentication and authorization mechanisms</li>
            </ul>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t-2 border-gray-200 pt-4 mt-8 text-center text-sm text-gray-500">
        <p>This report was generated by {companyName} Security Scanner on {new Date(generatedAt).toLocaleString()}</p>
        <p className="mt-1">Report ID: {scan.id}</p>
      </footer>
    </div>
  )
}

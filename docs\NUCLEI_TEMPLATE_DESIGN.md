## 🏗️ Template Structure & Scan Modes

The CTB Scanner supports multiple scan modes and types, each using different sets of templates for optimal coverage and performance. Templates are organized and selected dynamically based on the scan configuration.

### Scan Types
- **web-api**: For web applications, APIs, and cloud/web technologies
- **network**: For network protocols, infrastructure, and services
- **hybrid**: Combines web and network checks

### Scan Modes (for each type)
- **basic**: Fast, high-signal checks using essential templates (e.g., CVEs, panels, XSS, exposures, tech detection)
- **advanced**: Broader coverage, including more vulnerability classes, authentication, misconfigurations, and protocol-specific checks
- **comprehensive**: Maximum coverage, all supported templates for the scan type, including advanced fuzzing, DAST, and passive checks

#### Example: Web/API Scan Template Groups
- **Basic**: `cve`, `panel`, `xss`, `exposure`, `tech`, `http`, `ssl`, `dns`
- **Advanced**: All basic templates plus `wordpress`, `osint`, `rce`, `lfi`, `auth-bypass`, `jwt`, `oauth`, `sqli`, `ssti`, `ssrf`, `xxe`, `csrf`, `cors`, `redirect`, `traversal`, `file-upload`, `misconfiguration`, `exposures`, `backup`, `debug`, `config`, `cloud`, `api`, `graphql`, etc.
- **Comprehensive**: All advanced templates plus headless, DAST, fuzzing, workflows, passive, and all available tags/categories from the latest Nuclei templates repository.

#### Example: Network Scan Template Groups
- **Basic**: `network/`, `ssl/`, `dns/`, `tcp/`, `udp/`
- **Advanced**: All basic templates plus `misconfiguration/`, `exposures/`, `ftp/`, `ssh/`, `smtp/`, `rdp/`, `vnc/`, `database/`, `iot/`, etc.
- **Comprehensive**: All advanced templates plus SCADA, ICS, cloud, container, and all protocol/service templates.

#### How Scan Modes Are Used
- The scan mode and type are set in the scan options (see `ScanOptions` in `nuclei.ts`).
- The scanner dynamically selects the appropriate template set for the requested scan.
- You can override or extend the template set by specifying custom `templates`, `tags`, or `severity` in the scan options.

---
# Nuclei Template Design Guide

This document provides guidance and best practices for designing, organizing, and using Nuclei templates within the CTB Scanner platform. It is based on the advanced configuration and template management strategies implemented in the codebase (`src/lib/nuclei.ts`).

## 🎯 Template Philosophy
- **Comprehensive Coverage:** Templates should cover all major vulnerability types, protocols, and attack vectors.
- **Modular & Reusable:** Use tags, categories, and severity levels to organize templates for flexible scanning.
- **Performance Optimized:** Templates should be efficient and avoid unnecessary network or resource usage.
- **Real-World Relevance:** Focus on vulnerabilities that are exploitable in real-world scenarios.

## 🗂️ Template Categories & Tags
- **CVE Vulnerabilities:** `cve`, `cves/`
- **Admin Panels & Exposures:** `panel`, `exposure`, `exposures/`
- **XSS, SQLi, RCE, LFI, SSRF, etc.:** Use tags like `xss`, `sqli`, `rce`, `lfi`, `ssrf`
- **Technology/Platform:** `wordpress`, `drupal`, `joomla`, `api`, `graphql`, `cloud`, `aws`, `docker`, `k8s`, etc.
- **Network Protocols:** `network/`, `ssl/`, `dns/`, `tcp/`, `udp/`, etc.
- **Fuzzing & DAST:** `fuzz`, `dast`, `passive`, `headless`, etc.

## 🏷️ Template Metadata
Each template should include:
- **Name**: Human-readable name
- **Author**: List of authors
- **Tags**: For filtering and grouping
- **Severity**: `critical`, `high`, `medium`, `low`, `info`, `unknown`
- **Description**: (Optional) Brief explanation
- **Reference**: (Optional) CVE, CWE, or external links
- **Classification**: (Optional) CVE/CWE IDs, CVSS score

## 📄 Example Template Metadata
```yaml
template: example-xss
info:
  name: Example XSS Detection
  author: ["yataking"]
  tags: ["xss", "web", "example"]
  severity: high
  description: Detects a sample XSS vulnerability
  reference:
    - https://owasp.org/www-community/attacks/xss/
  classification:
    cve-id: ["CVE-2021-0000"]
    cwe-id: ["CWE-79"]
    cvss-score: 7.5
```

## 🛠️ Template Design Best Practices
- **Use Tags Wisely:** Tags enable flexible scan selection and filtering.
- **Set Severity Accurately:** Use the correct severity for each template.
- **Keep Templates Modular:** One template per vulnerability or check.
- **Support Bulk & Single Scans:** Templates should work for both single and bulk targets.
- **Optimize for Performance:** Avoid excessive requests or slow checks.
- **Document Clearly:** Use `description` and `reference` fields.

## 🚀 Template Usage in CTB Scanner
- Templates are selected dynamically based on scan type and mode (see `getDynamicTemplates` in `nuclei.ts`).
- Common template groups:
  - **Basic Web/API Scan:** `cve`, `panel`, `xss`, `exposure`, `tech`, `http`, `ssl`, `dns`
  - **Comprehensive Scan:** All major tags and categories
  - **Network Scan:** `network/`, `ssl/`, `dns/`, `tcp/`, `udp/`, etc.
- You can specify custom templates, tags, or severities in scan options.

## 🧩 Template File Organization
- Store templates in organized folders by category (e.g., `cves/`, `network/`, `web/`, `cloud/`).
- Use descriptive filenames (e.g., `wordpress-panel.yaml`, `cve-2023-1234.yaml`).

## 📝 Example Template File
```yaml
template: wordpress-panel
info:
  name: WordPress Admin Panel Exposure
  author: ["yataking"]
  tags: ["wordpress", "panel", "exposure"]
  severity: medium
  description: Detects exposed WordPress admin panels
  reference:
    - https://wordpress.org/support/article/security/
  classification:
    cwe-id: ["CWE-200"]
    cvss-score: 5.0
requests:
  - method: GET
    path:
      - "/wp-admin/"
    matchers:
      - type: word
        words:
          - "WordPress"
          - "Dashboard"
        condition: and
```

## 🔄 Updating & Testing Templates
- Update templates regularly using `nuclei -update-templates`.
- Test new templates with the CTB Scanner's test scripts or directly with Nuclei CLI.

## 📚 References
- [Nuclei Template Documentation](https://nuclei.projectdiscovery.io/templating-guide/)
- [Nuclei Templates GitHub](https://github.com/projectdiscovery/nuclei-templates)
- [OWASP Vulnerability Categories](https://owasp.org/www-project-top-ten/)

---

*This guide is inspired by the advanced template and scan configuration logic in the CTB Scanner codebase and the best practices of the Nuclei community.*

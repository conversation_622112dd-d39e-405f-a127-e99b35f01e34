/**
 * Performance Monitoring API Endpoint
 * 
 * Provides comprehensive performance metrics, cache statistics,
 * and system resource information for monitoring and optimization.
 */

import { NextRequest, NextResponse } from 'next/server'
import { performanceMonitor, getPerformanceReport } from '@/lib/performance'
import { getCacheStats } from '@/lib/cache'
import { timedApiCall } from '@/lib/performance'

/**
 * GET /api/performance
 * 
 * Returns comprehensive performance metrics including:
 * - System resource usage (memory, CPU, uptime)
 * - Performance statistics (response times, throughput)
 * - Cache performance (hit rates, sizes)
 * - Top slow operations
 */
export async function GET(request: NextRequest) {
  return timedApiCall('performance', async () => {
    try {
      const searchParams = request.nextUrl.searchParams
      const detailed = searchParams.get('detailed') === 'true'
      const since = searchParams.get('since')
      
      // Get basic performance report
      const performanceReport = getPerformanceReport()
      
      // Get cache statistics
      const cacheStats = getCacheStats()
      
      // Get recent metrics if requested
      let recentMetrics = undefined
      if (detailed) {
        const sinceTime = since ? parseInt(since) : Date.now() - (60 * 60 * 1000) // Last hour
        recentMetrics = performanceMonitor.getMetrics(sinceTime)
      }

      const response = {
        timestamp: new Date().toISOString(),
        system: {
          resources: performanceReport.resources,
          performance: performanceReport.metrics,
          cache: cacheStats
        },
        insights: {
          topSlowOperations: performanceReport.topSlowOperations,
          recommendations: generateRecommendations(performanceReport, cacheStats)
        },
        ...(detailed && { recentMetrics })
      }

      return NextResponse.json(response)
    } catch (error) {
      console.error('❌ Performance API error:', error)
      return NextResponse.json({
        error: 'Failed to retrieve performance metrics',
        timestamp: new Date().toISOString()
      }, { status: 500 })
    }
  }, { endpoint: '/api/performance' })
}

/**
 * POST /api/performance/clear
 * 
 * Clears performance metrics and cache data
 */
export async function POST(request: NextRequest) {
  return timedApiCall('performance_clear', async () => {
    try {
      const body = await request.json()
      const { type, olderThan } = body

      let result: any = {}

      switch (type) {
        case 'metrics':
          const cleared = performanceMonitor.clearMetrics(olderThan)
          result = { clearedMetrics: cleared }
          break
          
        case 'cache':
          const { cacheManager } = await import('@/lib/cache')
          cacheManager.clearAll()
          result = { message: 'All caches cleared' }
          break
          
        case 'all':
          const clearedMetrics = performanceMonitor.clearMetrics(olderThan)
          const { cacheManager: cm } = await import('@/lib/cache')
          cm.clearAll()
          result = { clearedMetrics, message: 'All performance data cleared' }
          break
          
        default:
          return NextResponse.json({
            error: 'Invalid clear type. Use: metrics, cache, or all'
          }, { status: 400 })
      }

      return NextResponse.json({
        success: true,
        timestamp: new Date().toISOString(),
        ...result
      })
    } catch (error) {
      console.error('❌ Performance clear error:', error)
      return NextResponse.json({
        error: 'Failed to clear performance data',
        timestamp: new Date().toISOString()
      }, { status: 500 })
    }
  }, { endpoint: '/api/performance/clear' })
}

/**
 * Generate performance recommendations based on metrics
 */
function generateRecommendations(
  performanceReport: any,
  cacheStats: any
): Array<{ type: 'warning' | 'info' | 'success'; message: string; action?: string }> {
  const recommendations = []
  const { resources, metrics } = performanceReport

  // Memory recommendations
  if (resources.memory.percentage > 80) {
    recommendations.push({
      type: 'warning' as const,
      message: `High memory usage: ${resources.memory.percentage}%`,
      action: 'Consider increasing memory allocation or optimizing memory-intensive operations'
    })
  } else if (resources.memory.percentage < 50) {
    recommendations.push({
      type: 'success' as const,
      message: `Memory usage is optimal: ${resources.memory.percentage}%`
    })
  }

  // Cache performance recommendations
  if (cacheStats.total.hitRate < 0.7) {
    recommendations.push({
      type: 'warning' as const,
      message: `Low cache hit rate: ${(cacheStats.total.hitRate * 100).toFixed(1)}%`,
      action: 'Review cache TTL settings and cache key strategies'
    })
  } else if (cacheStats.total.hitRate > 0.9) {
    recommendations.push({
      type: 'success' as const,
      message: `Excellent cache performance: ${(cacheStats.total.hitRate * 100).toFixed(1)}% hit rate`
    })
  }

  // API performance recommendations
  if (metrics.api.avg > 1000) {
    recommendations.push({
      type: 'warning' as const,
      message: `Slow API response times: ${metrics.api.avg}ms average`,
      action: 'Optimize database queries and consider adding more caching'
    })
  }

  // Database performance recommendations
  if (metrics.database.avg > 500) {
    recommendations.push({
      type: 'warning' as const,
      message: `Slow database queries: ${metrics.database.avg}ms average`,
      action: 'Review query performance and add database indexes'
    })
  }

  // Scan performance recommendations
  if (metrics.scans.avg > 30000) { // 30 seconds
    recommendations.push({
      type: 'info' as const,
      message: `Long scan times: ${(metrics.scans.avg / 1000).toFixed(1)}s average`,
      action: 'Consider optimizing Nuclei templates or scan scope'
    })
  }

  // Uptime recommendations
  if (resources.uptime > 86400 * 7) { // 7 days
    recommendations.push({
      type: 'info' as const,
      message: `Long uptime: ${Math.round(resources.uptime / 86400)} days`,
      action: 'Consider scheduled restarts for memory cleanup'
    })
  }

  // Cache size recommendations
  for (const [cacheName, stats] of Object.entries(cacheStats.caches)) {
    const cacheStats_typed = stats as any
    if (cacheStats_typed.size / cacheStats_typed.maxSize > 0.9) {
      recommendations.push({
        type: 'warning' as const,
        message: `Cache '${cacheName}' is nearly full: ${cacheStats_typed.size}/${cacheStats_typed.maxSize}`,
        action: 'Consider increasing cache size or reducing TTL'
      })
    }
  }

  // If no issues found
  if (recommendations.length === 0) {
    recommendations.push({
      type: 'success' as const,
      message: 'All performance metrics are within optimal ranges'
    })
  }

  return recommendations
}

/**
 * PUT /api/performance/config
 * 
 * Updates performance monitoring configuration
 */
export async function PUT(request: NextRequest) {
  return timedApiCall('performance_config', async () => {
    try {
      const body = await request.json()
      const { cacheConfig, monitoringConfig } = body

      // Update cache configurations
      if (cacheConfig) {
        // This would require implementing dynamic cache reconfiguration
        // For now, return success but note that restart may be required
        console.log('📊 Cache configuration update requested:', cacheConfig)
      }

      // Update monitoring configurations
      if (monitoringConfig) {
        console.log('📊 Monitoring configuration update requested:', monitoringConfig)
      }

      return NextResponse.json({
        success: true,
        message: 'Configuration updated successfully',
        note: 'Some changes may require application restart',
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      console.error('❌ Performance config error:', error)
      return NextResponse.json({
        error: 'Failed to update performance configuration',
        timestamp: new Date().toISOString()
      }, { status: 500 })
    }
  }, { endpoint: '/api/performance/config' })
}

/**
 * GET /api/performance/health
 * 
 * Quick health check endpoint for monitoring systems
 */
export async function HEAD() {
  try {
    const resources = performanceMonitor.getResourceMetrics()
    
    // Simple health check based on memory usage
    if (resources.memory.percentage > 90) {
      return new NextResponse(null, { status: 503 }) // Service Unavailable
    }
    
    return new NextResponse(null, { 
      status: 200,
      headers: {
        'X-Memory-Usage': `${resources.memory.percentage}%`,
        'X-Uptime': `${resources.uptime}s`
      }
    })
  } catch (error) {
    return new NextResponse(null, { status: 500 })
  }
}

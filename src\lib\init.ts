/**
 * Application Initialization
 * 
 * This module handles the initialization of background services
 * and ensures they start properly in both development and production.
 */

import { jobQueue } from './job-queue'
import { startupManager } from './startup'

let isInitialized = false

/**
 * Initialize all background services
 */
export function initializeServices() {
  if (isInitialized) {
    console.log('Services already initialized, skipping...')
    return
  }

  console.log('🚀 Initializing application services...')
  
  try {
    // Initialize job queue
    jobQueue.initialize()
    
    isInitialized = true
    console.log('✅ All services initialized successfully')
  } catch (error) {
    console.error('❌ Failed to initialize services:', error)
    throw error
  }
}

/**
 * Enhanced initialization with comprehensive health checks
 */
export async function initializeServicesEnhanced(options?: {
  skipNucleiCheck?: boolean
  retries?: number
}) {
  console.log('🚀 Starting enhanced service initialization...')

  const result = await startupManager.startup(options)

  if (result.success) {
    isInitialized = true
    console.log('✅ Enhanced initialization completed successfully')
  } else {
    console.error('❌ Enhanced initialization failed')
    console.error('Errors:', result.errors)
    console.warn('Warnings:', result.warnings)

    // Still mark as initialized if only warnings exist
    if (result.errors.length === 0) {
      isInitialized = true
      console.log('⚠️ Initialization completed with warnings only')
    }
  }

  return result
}

/**
 * Check if services are initialized
 */
export function areServicesInitialized(): boolean {
  return isInitialized
}

/**
 * Get detailed service status
 */
export function getServiceStatus() {
  return {
    initialized: isInitialized,
    startup: startupManager.getStatus(),
    jobQueue: {
      initialized: jobQueue.isInitialized(),
      status: jobQueue.getStatus()
    }
  }
}

/**
 * Reset initialization state (for testing)
 */
export function resetInitialization(): void {
  isInitialized = false
}

/**
 * Graceful shutdown of all services
 */
export async function shutdownServices(): Promise<void> {
  console.log('🛑 Shutting down all services...')

  try {
    await startupManager.shutdown()
    isInitialized = false
    console.log('✅ All services shut down successfully')
  } catch (error) {
    console.error('❌ Error during service shutdown:', error)
  }
}

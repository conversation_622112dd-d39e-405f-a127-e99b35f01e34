import * as React from 'react'
import { cn } from '@/lib/utils'
import { Card } from '../molecules/card'
import { EmptyState } from '../molecules/empty-state'
import { LoadingSpinner } from '../atoms/loading-spinner'
import { Button } from '../atoms/button'
import { Text } from '../atoms/text'
import { ChevronLeft, ChevronRight } from 'lucide-react'

export interface Column<T> {
  key: keyof T | string
  header: string
  render?: (item: T) => React.ReactNode
  className?: string
  sortable?: boolean
}

export interface DataTableProps<T> {
  data: T[]
  columns: Column<T>[]
  loading?: boolean
  emptyState?: {
    title: string
    description?: string
    action?: {
      label: string
      onClick: () => void
    }
  }
  pagination?: {
    page: number
    pages: number
    total: number
    limit?: number
    onPageChange: (page: number) => void
  }
  className?: string
  onRowClick?: (item: T) => void
  striped?: boolean
  onSort?: (key: string, order: 'asc' | 'desc') => void
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export function DataTable<T>({
  data,
  columns,
  loading = false,
  emptyState,
  pagination,
  className,
  onRowClick,
  striped = false,
  onSort,
  sortBy,
  sortOrder = 'asc'
}: DataTableProps<T>) {
  if (loading) {
    return (
      <Card className={cn('p-8', className)}>
        <div className="flex items-center justify-center">
          <LoadingSpinner size="lg" />
          <Text className="ml-3" color="muted">Loading...</Text>
        </div>
      </Card>
    )
  }

  if (!data.length && emptyState) {
    return (
      <Card className={className}>
        <EmptyState
          title={emptyState.title}
          description={emptyState.description}
          action={emptyState.action}
        />
      </Card>
    )
  }

  return (
    <Card className={cn('overflow-hidden', className)} padding="none">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50 border-b border-gray-200">
            <tr>
              {columns.map((column, index) => (
                <th
                  key={index}
                  className={cn(
                    'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider',
                    column.className,
                    column.sortable && onSort && 'cursor-pointer hover:bg-gray-100'
                  )}
                  onClick={() => {
                    if (column.sortable && onSort) {
                      const newOrder = sortBy === column.key && sortOrder === 'asc' ? 'desc' : 'asc'
                      onSort(column.key as string, newOrder)
                    }
                  }}
                >
                  <div className="flex items-center space-x-1">
                    <span>{column.header}</span>
                    {column.sortable && sortBy === column.key && (
                      <span className="text-gray-400" data-testid="sort-indicator">
                        {sortOrder === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {data.map((item, rowIndex) => (
              <tr
                key={rowIndex}
                className={cn(
                  'hover:bg-gray-50 transition-colors',
                  onRowClick && 'cursor-pointer',
                  striped && rowIndex % 2 === 0 && 'bg-gray-50'
                )}
                onClick={() => onRowClick?.(item)}
                onKeyDown={(e) => {
                  if (onRowClick && (e.key === 'Enter' || e.key === ' ')) {
                    e.preventDefault()
                    onRowClick(item)
                  }
                }}
                tabIndex={onRowClick ? 0 : undefined}
              >
                {columns.map((column, colIndex) => (
                  <td
                    key={colIndex}
                    className={cn(
                      'px-6 py-4 whitespace-nowrap text-sm',
                      column.className
                    )}
                  >
                    {column.render 
                      ? column.render(item)
                      : String((item as any)[column.key] || '')
                    }
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {pagination && (
        <div className="px-6 py-3 bg-gray-50 border-t border-gray-200 flex items-center justify-between">
          <Text variant="caption" color="muted">
            Showing {((pagination.page - 1) * (pagination.limit || 10)) + 1} to{' '}
            {Math.min(pagination.page * (pagination.limit || 10), pagination.total)} of{' '}
            {pagination.total} results
          </Text>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => pagination.onPageChange(pagination.page - 1)}
              disabled={pagination.page <= 1}
              leftIcon={<ChevronLeft />}
            >
              Previous
            </Button>
            
            <Text variant="caption">
              Page {pagination.page} of {pagination.pages}
            </Text>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => pagination.onPageChange(pagination.page + 1)}
              disabled={pagination.page >= pagination.pages}
              rightIcon={<ChevronRight />}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </Card>
  )
}

import { NextRequest, NextResponse } from 'next/server'
import { getServiceStatus } from '@/lib/init'
import { nucleiScanner } from '@/lib/nuclei'
import { db } from '@/lib/db'
import { logger } from '@/lib/logger'
import { jobQueue } from '@/lib/job-queue'
import { performanceMonitor } from '@/lib/performance'
import { cacheManager } from '@/lib/cache'

/**
 * GET /api/health - Application health check endpoint
 * 
 * Provides comprehensive health status for monitoring and diagnostics.
 * Used by load balancers, monitoring systems, and Docker health checks.
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now()

  try {
    logger.info('Health check requested', {
      component: 'HEALTH',
      ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      userAgent: request.headers.get('user-agent') || undefined
    })

    const healthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.APP_VERSION || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services: {
        database: { healthy: false, details: '', responseTime: 0 },
        nuclei: { healthy: false, details: '', responseTime: 0 },
        jobQueue: { healthy: false, details: '', responseTime: 0 },
        cache: { healthy: false, details: '', responseTime: 0 },
        performance: { healthy: false, details: '', responseTime: 0 }
      },
      system: {
        memory: process.memoryUsage(),
        platform: process.platform,
        nodeVersion: process.version,
        pid: process.pid
      },
      metrics: {
        totalScans: 0,
        activeScans: 0,
        queuedJobs: 0,
        cacheHitRate: 0,
        averageResponseTime: 0
      }
    }

    // Check database health
    const dbStartTime = Date.now()
    try {
      await db.$queryRaw`SELECT 1 as health_check`
      healthStatus.services.database = {
        healthy: true,
        details: 'Database connection successful',
        responseTime: Date.now() - dbStartTime
      }
    } catch (error) {
      healthStatus.services.database = {
        healthy: false,
        details: `Database error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        responseTime: Date.now() - dbStartTime
      }
      healthStatus.status = 'unhealthy'
    }

    // Check Nuclei health (quick check)
    const nucleiStartTime = Date.now()
    try {
      const nucleiHealthy = await nucleiScanner.checkNucleiInstallation()
      healthStatus.services.nuclei = {
        healthy: nucleiHealthy,
        details: nucleiHealthy ? 'Nuclei scanner available' : 'Nuclei scanner not available',
        responseTime: Date.now() - nucleiStartTime
      }
      
      if (!nucleiHealthy) {
        healthStatus.status = 'degraded'
      }
    } catch (error) {
      healthStatus.services.nuclei = {
        healthy: false,
        details: `Nuclei check error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        responseTime: Date.now() - nucleiStartTime
      }
      healthStatus.status = 'degraded'
    }

    // Check job queue status
    const queueStartTime = Date.now()
    try {
      const serviceStatus = getServiceStatus()
      const queueStatus = await jobQueue.getQueueStatus()

      healthStatus.services.jobQueue = {
        healthy: serviceStatus.jobQueue.initialized,
        details: serviceStatus.jobQueue.initialized ?
          `Job queue operational (${queueStatus.running} running, ${queueStatus.pending} pending)` :
          'Job queue not initialized',
        responseTime: Date.now() - queueStartTime
      }

      if (!serviceStatus.jobQueue.initialized) {
        healthStatus.status = 'degraded'
      }
    } catch (error) {
      healthStatus.services.jobQueue = {
        healthy: false,
        details: `Job queue error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        responseTime: Date.now() - queueStartTime
      }
      healthStatus.status = 'degraded'
    }

    // Check cache health
    const cacheStartTime = Date.now()
    try {
      const allCacheStats = cacheManager.getAllStats()
      const cacheCount = Object.keys(allCacheStats).length
      const totalHits = Object.values(allCacheStats).reduce((sum, stats) => sum + stats.hits, 0)
      const totalMisses = Object.values(allCacheStats).reduce((sum, stats) => sum + stats.misses, 0)
      const hitRate = totalHits + totalMisses > 0 ? (totalHits / (totalHits + totalMisses)) * 100 : 0

      healthStatus.services.cache = {
        healthy: true,
        details: `Cache operational (${cacheCount} caches, ${hitRate.toFixed(1)}% hit rate)`,
        responseTime: Date.now() - cacheStartTime
      }
    } catch (error) {
      healthStatus.services.cache = {
        healthy: false,
        details: `Cache error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        responseTime: Date.now() - cacheStartTime
      }
      healthStatus.status = 'degraded'
    }

    // Check performance monitoring
    const perfStartTime = Date.now()
    try {
      const perfStats = performanceMonitor.getStats()
      healthStatus.services.performance = {
        healthy: true,
        details: `Performance monitoring active (${perfStats.count} metrics collected, avg: ${perfStats.avg}ms)`,
        responseTime: Date.now() - perfStartTime
      }
    } catch (error) {
      healthStatus.services.performance = {
        healthy: false,
        details: `Performance monitoring error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        responseTime: Date.now() - perfStartTime
      }
      healthStatus.status = 'degraded'
    }

    // Get application metrics
    try {
      const [totalScans, activeScans] = await Promise.all([
        db.scan.count(),
        db.scan.count({ where: { status: 'RUNNING' } })
      ])

      const queueStatus = await jobQueue.getQueueStatus()
      const allCacheStats = cacheManager.getAllStats()
      const perfStats = performanceMonitor.getStats()

      // Calculate cache hit rate
      const totalHits = Object.values(allCacheStats).reduce((sum, stats) => sum + stats.hits, 0)
      const totalMisses = Object.values(allCacheStats).reduce((sum, stats) => sum + stats.misses, 0)
      const cacheHitRate = totalHits + totalMisses > 0 ? (totalHits / (totalHits + totalMisses)) * 100 : 0

      healthStatus.metrics = {
        totalScans,
        activeScans,
        queuedJobs: queueStatus.pending,
        cacheHitRate: Math.round(cacheHitRate * 100) / 100,
        averageResponseTime: perfStats.avg
      }
    } catch (error) {
      logger.error('Failed to get application metrics for health check', error as Error)
    }

    // Calculate total response time
    const totalResponseTime = Date.now() - startTime

    // Return appropriate HTTP status based on health
    const httpStatus = healthStatus.status === 'healthy' ? 200 : 
                      healthStatus.status === 'degraded' ? 200 : 503

    return NextResponse.json({
      ...healthStatus,
      responseTime: totalResponseTime
    }, { status: httpStatus })

  } catch (error) {
    console.error('Health check error:', error)
    
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
      responseTime: Date.now() - startTime
    }, { status: 503 })
  }
}

/**
 * POST /api/health - Perform comprehensive health check
 * 
 * Triggers a more thorough health check including Nuclei functionality test.
 * Should be used sparingly as it may take longer to complete.
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    const body = await request.json().catch(() => ({}))
    const { comprehensive = false } = body

    const healthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      comprehensive,
      services: {
        database: { healthy: false, details: '', responseTime: 0 },
        nuclei: { healthy: false, details: '', responseTime: 0 },
        jobQueue: { healthy: false, details: '', responseTime: 0 }
      }
    }

    // Database check (same as GET)
    const dbStartTime = Date.now()
    try {
      await db.$queryRaw`SELECT 1 as health_check`
      healthStatus.services.database = {
        healthy: true,
        details: 'Database connection successful',
        responseTime: Date.now() - dbStartTime
      }
    } catch (error) {
      healthStatus.services.database = {
        healthy: false,
        details: `Database error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        responseTime: Date.now() - dbStartTime
      }
      healthStatus.status = 'unhealthy'
    }

    // Comprehensive Nuclei check
    const nucleiStartTime = Date.now()
    try {
      if (comprehensive) {
        const nucleiHealth = await nucleiScanner.performHealthCheck(1, 1000)
        healthStatus.services.nuclei = {
          healthy: nucleiHealth.healthy,
          details: nucleiHealth.details,
          responseTime: Date.now() - nucleiStartTime
        }
      } else {
        const nucleiHealthy = await nucleiScanner.checkNucleiInstallation()
        healthStatus.services.nuclei = {
          healthy: nucleiHealthy,
          details: nucleiHealthy ? 'Nuclei scanner available' : 'Nuclei scanner not available',
          responseTime: Date.now() - nucleiStartTime
        }
      }
      
      if (!healthStatus.services.nuclei.healthy) {
        healthStatus.status = 'degraded'
      }
    } catch (error) {
      healthStatus.services.nuclei = {
        healthy: false,
        details: `Nuclei check error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        responseTime: Date.now() - nucleiStartTime
      }
      healthStatus.status = 'degraded'
    }

    // Job queue check (same as GET)
    try {
      const serviceStatus = getServiceStatus()
      healthStatus.services.jobQueue = {
        healthy: serviceStatus.jobQueue.initialized,
        details: serviceStatus.jobQueue.initialized ? 'Job queue operational' : 'Job queue not initialized',
        responseTime: 0
      }
      
      if (!serviceStatus.jobQueue.initialized) {
        healthStatus.status = 'degraded'
      }
    } catch (error) {
      healthStatus.services.jobQueue = {
        healthy: false,
        details: `Job queue error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        responseTime: 0
      }
      healthStatus.status = 'degraded'
    }

    const totalResponseTime = Date.now() - startTime
    const httpStatus = healthStatus.status === 'healthy' ? 200 : 
                      healthStatus.status === 'degraded' ? 200 : 503

    return NextResponse.json({
      ...healthStatus,
      responseTime: totalResponseTime
    }, { status: httpStatus })

  } catch (error) {
    console.error('Comprehensive health check error:', error)
    
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
      responseTime: Date.now() - startTime
    }, { status: 503 })
  }
}

'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useRouter, usePathname } from 'next/navigation'
import { useAuth } from '@/contexts/auth-context'
import { getInitials } from '@/lib/utils'
import { Button } from '../atoms/button'
import { Avatar } from '../atoms/avatar'
import { Text } from '../atoms/text'
import { Heading } from '../atoms/heading'
import { Icon } from '../atoms/icon'
import {
  Home,
  Settings,
  User,
  LogOut,
  Menu,
  X,
  Search,
  Shield,
  Database,
  Activity
} from 'lucide-react'

export interface SidebarProps {
  className?: string
}

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: Home },
  { name: '<PERSON>an', href: '/dashboard/scan', icon: Search },
  { name: 'Scans', href: '/dashboard/scans', icon: Activity },
  { name: 'Assets', href: '/dashboard/assets', icon: Database },
  { name: 'Vulnerabilities', href: '/dashboard/vulnerabilities', icon: Shield },
  { name: 'Profile', href: '/dashboard/profile', icon: User },
  { name: 'Setting<PERSON>', href: '/dashboard/settings', icon: Settings },
]

export const Sidebar: React.FC<SidebarProps> = ({ className }) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const { user, logout } = useAuth()
  const router = useRouter()
  const pathname = usePathname()

  const handleLogout = async () => {
    try {
      await logout()
      router.push('/login')
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  if (!user) return null

  const isActiveRoute = (href: string) => {
    if (href === '/dashboard') {
      return pathname === '/dashboard'
    }
    return pathname.startsWith(href)
  }

  return (
    <>
      {/* Mobile menu button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <Button
          variant="outline"
          size="icon"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="bg-white shadow-md"
        >
          {isMobileMenuOpen ? (
            <Icon icon={X} size="md" />
          ) : (
            <Icon icon={Menu} size="md" />
          )}
        </Button>
      </div>

      {/* Mobile backdrop */}
      {isMobileMenuOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div
        className={`
          fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out
          lg:translate-x-0 lg:static lg:inset-0
          ${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
          ${className}
        `}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <Icon icon={Shield} size="md" color="white" />
              </div>
              <Heading level={4} color="primary">
                CTB Scanner
              </Heading>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2">
            {navigation.map((item) => {
              const isActive = isActiveRoute(item.href)
              
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  onClick={() => setIsMobileMenuOpen(false)}
                  className={`
                    flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors
                    ${isActive
                      ? 'bg-blue-50 text-blue-700 border border-blue-200'
                      : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                    }
                  `}
                >
                  <Icon 
                    icon={item.icon} 
                    size="md" 
                    color={isActive ? 'primary' : 'default'}
                    className="mr-3"
                  />
                  {item.name}
                </Link>
              )
            })}
          </nav>

          {/* User Profile */}
          <div className="p-4 border-t border-gray-200">
            <div className="flex items-center space-x-3 mb-4">
              <Avatar
                fallback={getInitials(user.email)}
                size="md"
              />
              <div className="flex-1 min-w-0">
                <Text variant="body" weight="medium" className="truncate">
                  {user.email}
                </Text>
                <Text variant="caption" color="muted">
                  User Account
                </Text>
              </div>
            </div>
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleLogout}
              leftIcon={<LogOut />}
              fullWidth
              className="text-red-600 border-red-200 hover:bg-red-50"
            >
              Sign Out
            </Button>
          </div>
        </div>
      </div>
    </>
  )
}
